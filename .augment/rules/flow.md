---
type: "always_apply"
---

### Generación de Archivos
- **NO generar** archivos de documentación, ejemplos, README, o cualquier contenido adicional que no haya sido explícitamente solicitado
- Crear únicamente los archivos de código necesarios para cumplir con la tarea específica
- Evitar archivos auxiliares como tests, configuraciones de ejemplo, o demos a menos que se requieran

### Finalización de Turnos
- **SIEMPRE** terminar cada turno llamando a `ask_to_leader_project`
- Esta función debe ejecutarse al completar cualquier tarea, sin excepciones
- Usar esta llamada para reportar el estado y solicitar la siguiente instrucción

### Manejo de Errores y Fallos
- Cuando algo falle, **NO asumir automáticamente** que es un error del código
- **Revisar la consola** en busca de señales de interrupción como `Ctrl+C`
- Si se detecta una interrupción manual, dirigirse inmediatamente al líder del proyecto para consultar el motivo
- Distinguir entre errores técnicos reales e interrupciones intencionales del usuario

### Consultas sobre Camoufoux
- **SIEMPRE usar DeepWiki** con el repositorio de Camoufoux (https://github.com/daijro/camoufox) para resolver dudas
- Dado que Camoufoux es una librería nueva, es crítico verificar cada implementación antes de proceder
- No hacer suposiciones sobre funcionalidades sin consultar la documentación oficial

### Gestión de Sesiones
- **Reutilizar siempre** las sesiones almacenadas en los archivos del proyecto
- Esta práctica es esencial para evitar baneos y límites de tasa
- Mantener la persistencia de sesión entre diferentes ejecuciones del código
- Verificar la existencia de archivos de sesión antes de crear nuevas conexiones

### Flujo de Trabajo
1. Leer y entender la tarea específica
2. Consultar DeepWiki si hay dudas sobre Camoufoux
3. Implementar solo lo solicitado
4. Reutilizar sesiones existentes
5. Terminar con `ask_to_leader_project`  
