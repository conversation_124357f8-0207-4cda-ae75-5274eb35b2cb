---
type: "always_apply"
---

PROHIBIDO generar documentación en archivos separados. El equipo se ha quejado repetidamente por la desorganización causada por archivos .md dispersos. UTILIZA ÚNICAMENTE docstrings dentro del código. No crees archivos .md, .txt, README o cualquier otro archivo de documentación externa. Toda la documentación DEBE estar integrada directamente en el código fuente mediante docstrings. Esta es una directriz obligatoria del equipo para mantener el orden y la organización del proyecto.
