/**
 * ===================================================================================
 * ||                                                                               ||
 * ||                SCRAPER INTELIGENTE PARA FACEBOOK (VERSIÓN 5.6)                  ||
 * ||       - Lógica de Cola de Procesamiento para Eliminar Race Conditions         ||
 * ||                                                                               ||
 * ===================================================================================
 * 
 * INSTRUCCIONES:
 * 1. Pega este código en la consola (F12 > Consola).
 * 2. Escribe `iniciarScraping()` y presiona Enter.
 * 3. El script funcionará de forma autónoma.
 * 4. Para detener, escribe `finalizarScraping()` y presiona Enter.
 * 5. Los resultados se mostrarán ORDENADOS por su posición en la página.
 * 
 */
const facebookScraper = (() => {
    let datosRecopilados = new Map(); // Usar un Map para evitar duplicados por posinset
    let pendingPosts = new Map(); // Mapa de elementos pendientes de procesamiento completo
    let observer = null;
    let scrollTimerId = null;
    let processingIntervalId = null;
    let isAtBottom = false;

    const SCROLL_INTERVAL_MS = 3000;
    const PROCESSING_INTERVAL_MS = 500; // Revisar la cola de pendientes cada 500ms

    const simulateClick = (element) => {
        const event = new MouseEvent('click', { bubbles: true, cancelable: true, view: window });
        element.dispatchEvent(event);
    };

    const extraerDatosDePost = (postElement) => {
        const posinset = parseInt(postElement.getAttribute('aria-posinset'), 10) || 0;
        const autorLinkElement = postElement.querySelector('h2 a, h3 a, h4 a');
        const nombreAutor = autorLinkElement?.innerText ?? 'No encontrado';
        const linkAutor = autorLinkElement?.href ?? 'No encontrado';
        const contentContainer = postElement.querySelector('[data-ad-rendering-role="story_message"], div[dir="auto"]:not(:has(h2, h3, h4))');
        const contenidoPost = contentContainer?.innerText?.trim() || 'No encontrado';
        const imagenes = Array.from(postElement.querySelectorAll('img')).filter(img => img.height > 80 && !img.src.includes('data:image')).map(img => img.src);
        const linkPost = postElement.querySelector('a[href*="/posts/"], a[href*="/videos/"], a[href*="/reel/"]')?.href ?? 'No encontrado';
        return { posinset, nombreAutor, linkAutor, contenidoPost, imagenes, linkPost };
    };
    
    // Nueva función que revisa la cola de posts pendientes
    const processPendingQueue = () => {
        if (pendingPosts.size === 0) return;

        let postsProcesadosEsteCiclo = 0;

        // Ordena los pendientes por posinset ascendente para asegurar el orden
        const pendientesOrdenados = Array.from(pendingPosts.entries()).sort((a, b) => a[0] - b[0]);

        for (const [posinset, postElement] of pendientesOrdenados) {
            // Si el elemento fue removido del DOM por la virtualización, lo eliminamos
            if (!document.body.contains(postElement)) {
                pendingPosts.delete(posinset);
                continue;
            }

            const postData = extraerDatosDePost(postElement);

            const esPostValido = postData.nombreAutor !== 'No encontrado' &&
                                 postData.linkAutor !== 'No encontrado' &&
                                 postData.contenidoPost !== 'No encontrado' &&
                                 postData.posinset > 0;

            if (!esPostValido) {
                continue; // No está listo, lo re-intentamos en el próximo ciclo
            }

            // Espera a que todos los anteriores consecutivos hayan sido capturados
            let faltaAnterior = false;
            for (let i = 1; i < posinset; i++) {
                if (!datosRecopilados.has(i)) {
                    faltaAnterior = true;
                    break;
                }
            }
            if (faltaAnterior) {
                // No proceses este post aún, espera a que aparezcan los anteriores
                continue;
            }

            const contentContainer = postElement.querySelector('[data-ad-rendering-role="story_message"]');
            const verMasButton = contentContainer ? Array.from(contentContainer.querySelectorAll('div[role="button"]')).find(el => ['Ver más', 'See More'].includes(el.textContent.trim())) : null;

            if (verMasButton) {
                simulateClick(verMasButton);
                // No lo eliminamos de pendientes, el clic causará una mutación que lo hará válido sin el botón.
            } else {
                // El post es válido y está completamente cargado y todos los anteriores ya están
                datosRecopilados.set(posinset, postData);
                pendingPosts.delete(posinset); // Lo eliminamos de la cola
                postsProcesadosEsteCiclo++;
            }
        }

        if (postsProcesadosEsteCiclo > 0) {
            console.log(`%c✅ +${postsProcesadosEsteCiclo} posts procesados. Total: ${datosRecopilados.size}.`, 'color: #28a745; font-weight: bold;');
        }
    };

    const autoScroll = () => {
        if (isAtBottom) { finalizar(); return; }
        const scrollableHeight = document.documentElement.scrollHeight - window.innerHeight;
        if (window.scrollY >= scrollableHeight - 100) {
            console.log('%c🏁 Se ha llegado al final del feed. Esperando procesamiento final...', 'color: #ffc107; font-weight: bold;');
            isAtBottom = true;
            // Damos unos segundos extra para que se procesen los últimos elementos antes de finalizar
            setTimeout(() => finalizar(), PROCESSING_COOLDOWN_MS * 2); 
            return;
        }
        console.log(`%c🔄 Scrolleando...`, 'color: #6c757d;');
        window.scrollBy(0, window.innerHeight);
        scrollTimerId = setTimeout(autoScroll, SCROLL_INTERVAL_MS);
    };
    
    const mutationCallback = (mutationsList) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { 
                        const posts = node.matches && node.matches('div[aria-posinset]') ? [node] : node.querySelectorAll('div[aria-posinset]');
                        posts.forEach(post => {
                            const posinset = parseInt(post.getAttribute('aria-posinset'), 10);
                            // Solo añadimos a pendientes si es un post válido y no lo tenemos ya
                            if (posinset && !datosRecopilados.has(posinset) && !pendingPosts.has(posinset)) {
                                pendingPosts.set(posinset, post);
                            }
                        });
                    }
                });
            }
        }
    };

    function iniciar() {
        console.clear();
        console.log('%c🚀 Iniciando Scraper v5.6... 🚀', 'color: #007bff; font-size: 18px; font-weight: bold;');
        
        const feedContainer = document.querySelector('div[role="feed"]');
        if (!feedContainer) { console.error("Error: Contenedor del feed ('div[role=\"feed\"]') no encontrado."); return; }

        console.log("Analizando contenido inicial y activando observer...");
        feedContainer.querySelectorAll('div[aria-posinset]').forEach(post => {
            const posinset = parseInt(post.getAttribute('aria-posinset'), 10);
            if (posinset) pendingPosts.set(posinset, post);
        });
        
        observer = new MutationObserver(mutationCallback);
        const config = { childList: true, subtree: true };
        observer.observe(feedContainer, config);

        // Inicia el ciclo de procesamiento de la cola
        processingIntervalId = setInterval(processPendingQueue, PROCESSING_INTERVAL_MS);
        
        console.log('%c✅ Observer y cola de procesamiento activados. Iniciando auto-scroll.', 'color: #17a2b8; font-size: 16px;');
        autoScroll();
    }
    
    function finalizar() {
        if (observer) { observer.disconnect(); observer = null; console.log('%c🛑 Observer detenido.', 'color: #dc3545; font-size: 14px;'); }
        if (scrollTimerId) { clearTimeout(scrollTimerId); scrollTimerId = null; console.log('%c🛑 Temporizador de auto-scroll detenido.', 'color: #dc3545; font-size: 14px;'); }
        if (processingIntervalId) { clearInterval(processingIntervalId); processingIntervalId = null; console.log('%c🛑 Procesador de cola detenido.', 'color: #dc3545; font-size: 14px;'); }
        
        // Un último intento de procesar lo que quedó pendiente
        processPendingQueue();

        console.log(`%c🎉 Scraping finalizado. Total de posts recopilados: ${datosRecopilados.size}`, 'color: #28a745; font-size: 18px; font-weight: bold;');
        
        if (datosRecopilados.size > 0) {
            const finalData = Array.from(datosRecopilados.values());
            finalData.sort((a, b) => a.posinset - b.posinset);

            console.log("Mostrando datos en formato de tabla (ordenados por posición):");
            console.table(finalData, ['posinset', 'nombreAutor', 'contenidoPost', 'linkPost', 'imagenes']);
            
            console.log('%c👇 Copia el siguiente bloque de código JSON (ordenado):', 'color: #ffc107; font-size: 16px;');
            console.log(JSON.stringify(finalData, null, 2));
        } else {
            console.log("No se recopiló ningún dato válido.");
        }
    }

    return { iniciar, finalizar };
})();

// --- FUNCIONES DE CONTROL ---
function iniciarScraping() {
    facebookScraper.iniciar();
}

function finalizarScraping() {
    facebookScraper.finalizar();
}