[mypy]
# Configuración de mypy para Facebook Flow - Modo progresivo
python_version = 3.12
warn_return_any = False
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = False
disallow_untyped_decorators = False
no_implicit_optional = False
warn_redundant_casts = True
warn_unused_ignores = False
warn_no_return = False
warn_unreachable = False
strict_equality = False
show_error_codes = True

# Directorios a verificar
files = flow/

# Ignorar librerías externas sin tipos
[mypy-camoufox.*]
ignore_missing_imports = True

[mypy-browserforge.*]
ignore_missing_imports = True

[mypy-playwright.*]
ignore_missing_imports = True

# Configuración específica para archivos de test
[mypy-flow/test_*]
ignore_errors = True
