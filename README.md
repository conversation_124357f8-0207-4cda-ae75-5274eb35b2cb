# 🦊 Guía Completa de Camoufox

Esta guía proporciona instrucciones detalladas para instalar, configurar y usar Camoufox con GeoIP y fingerprints, basada en información obtenida de [DeepWiki](https://deepwiki.com) del repositorio oficial.

## 📋 Tabla de Contenidos

1. [Instalación](#instalación)
2. [Descarga del Binario](#descarga-del-binario)
3. [Configuración de GeoIP](#configuración-de-geoip)
4. [Uso de Fingerprints](#uso-de-fingerprints)
5. [Configuración de Proxies](#configuración-de-proxies)
6. [Ejemplos Prácticos](#ejemplos-prácticos)
7. [Opciones Avanzadas](#opciones-avanzadas)

## 🚀 Instalación

### 1. Configurar Entorno Virtual

```bash
# Crear entorno virtual
python3 -m venv .venv

# Activar entorno virtual
source .venv/bin/activate  # Linux/macOS
# o
.venv\Scripts\activate     # Windows
```

### 2. Instalar Camoufox

```bash
# Instalar con soporte GeoIP (recomendado)
pip install -U camoufox[geoip]

# O instalación básica
pip install -U camoufox
```

## 📦 Descarga del Binario

Después de instalar la biblioteca Python, debes descargar el binario del navegador:

```bash
# Linux/macOS
python3 -m camoufox fetch

# Windows
camoufox fetch
```

El comando descarga:
- Archivos de definición de modelos
- Binarios del navegador Camoufox
- Base de datos GeoIP (si está habilitado)

## 🌍 Configuración de GeoIP

### Habilitar GeoIP

Para usar GeoIP, instala con el parámetro `geoip`:

```bash
pip install -U camoufox[geoip]
```

### Uso en Código

```python
from camoufox import Camoufox

# Habilitar GeoIP automático
with Camoufox(geoip=True) as browser:
    page = browser.new_page()
    page.goto("https://example.com")
    # La geolocalización se configura automáticamente basada en la IP
```

**Beneficios del GeoIP:**
- Determina automáticamente longitud y latitud
- Configura zona horaria apropiada
- Establece país y configuración regional
- Funciona especialmente bien con proxies

## 🎭 Uso de Fingerprints

Los fingerprints en Camoufox simulan características de dispositivos reales para evitar detección de bots.

### Configuración Básica

```python
from camoufox import Camoufox
from browserforge.fingerprints import Screen

with Camoufox(
    os="linux",                                    # Sistema operativo
    screen=Screen(max_width=1920, max_height=1080), # Restricciones de pantalla
    window=(1200, 800),                            # Tamaño de ventana fijo
    locale="es-ES",                                # Configuración regional
    humanize=True,                                 # Movimiento humanizado
    block_webrtc=True,                            # Bloquear WebRTC
    block_webgl=False                             # Permitir WebGL
) as browser:
    page = browser.new_page()
    page.goto("https://example.com")
```

### Opciones de Fingerprint

| Parámetro | Descripción | Valores |
|-----------|-------------|---------|
| `os` | Sistema operativo | `"windows"`, `"macos"`, `"linux"` |
| `screen` | Restricciones de pantalla | Objeto `Screen()` |
| `window` | Tamaño de ventana fijo | Tupla `(width, height)` |
| `fingerprint` | Fingerprint personalizado | Objeto `Fingerprint` |
| `locale` | Configuración regional | `"en-US"`, `"es-ES"`, etc. |
| `humanize` | Movimiento humanizado | `True`/`False` |
| `block_webrtc` | Bloquear WebRTC | `True`/`False` |
| `block_webgl` | Bloquear WebGL | `True`/`False` |

## 🔗 Configuración de Proxies

```python
from camoufox import Camoufox

# Configuración de proxy con autenticación
with Camoufox(
    proxy={
        "server": "http://proxy_server:port",
        "username": "usuario",
        "password": "contraseña"
    },
    geoip=True  # Recomendado con proxies
) as browser:
    page = browser.new_page()
    page.goto("https://httpbin.org/ip")
    print(page.text_content())
```

## 💡 Ejemplos Prácticos

### Ejemplo Básico

```python
from camoufox import Camoufox

with Camoufox() as browser:
    page = browser.new_page()
    page.goto("https://example.com")
    title = page.title()
    print(f"Título: {title}")
```

### Ejemplo con GeoIP

```python
with Camoufox(geoip=True) as browser:
    page = browser.new_page()
    page.goto("https://ipinfo.io/json")
    geo_info = page.text_content()
    print(f"Información geográfica: {geo_info}")
```

### Ejemplo Avanzado

```python
from camoufox import Camoufox
from browserforge.fingerprints import Screen

with Camoufox(
    os="windows",
    screen=Screen(max_width=1920, max_height=1080),
    geoip=True,
    humanize=True,
    block_webrtc=True,
    locale="en-US",
    headless=False  # Navegador visible
) as browser:
    page = browser.new_page()
    
    # Test de detección de bots
    page.goto("https://bot.sannysoft.com/")
    
    # Interacción humanizada
    page.click("selector")
    page.fill("input", "texto")
```

## ⚙️ Opciones Avanzadas

### Configuración Completa

```python
with Camoufox(
    # Fingerprinting
    os="linux",
    screen=Screen(max_width=1920, max_height=1080),
    window=(1200, 800),
    fingerprint=custom_fingerprint,  # Fingerprint personalizado
    
    # Geolocalización y Red
    geoip=True,
    proxy=proxy_config,
    
    # Comportamiento
    humanize=True,
    headless=False,
    
    # Bloqueos
    block_images=False,
    block_webrtc=True,
    block_webgl=False,
    
    # Configuración Regional
    locale="es-ES",
    
    # Firefox específico
    ff_version="125.0",
    firefox_user_prefs={"privacy.trackingprotection.enabled": True},
    
    # Sistema
    executable_path="/custom/path/to/firefox",
    args=["--no-sandbox"],
    env={"DISPLAY": ":0"},
    
    # Desarrollo
    debug=True,
    enable_cache=True
) as browser:
    # Tu código aquí
    pass
```

### Manejo de Errores

```python
try:
    with Camoufox(geoip=True, humanize=True) as browser:
        page = browser.new_page()
        page.goto("https://example.com", timeout=30000)
        
except Exception as e:
    print(f"Error: {e}")
    # Manejar error específico
```

## 🔧 Comandos Útiles

```bash
# Verificar instalación
python3 -m camoufox --version

# Actualizar binarios
python3 -m camoufox fetch

# Limpiar instalación
python3 -m camoufox clean

# Ejecutar script de ejemplo
python3 camoufox_guide.py
```

## 📚 Recursos Adicionales

- **Documentación oficial**: [camoufox.com/python](https://camoufox.com/python)
- **Repositorio GitHub**: [github.com/daijro/camoufox](https://github.com/daijro/camoufox)
- **DeepWiki**: Información detallada sobre la arquitectura y uso
- **BrowserForge**: Para fingerprints personalizados

## ⚠️ Notas Importantes

1. **GeoIP**: Requiere conexión a internet para funcionar correctamente
2. **Proxies**: Configura credenciales válidas para evitar errores
3. **Fingerprints**: No manipules manualmente propiedades como `navigator` o `geolocation`
4. **Detección**: La inyección de fingerprints se realiza a nivel C++, haciéndola indetectable
5. **Actualizaciones**: Mantén Camoufox actualizado para mejor compatibilidad

## 🐛 Solución de Problemas

### Error de instalación
```bash
pip install --upgrade pip
pip install -U camoufox[geoip] --force-reinstall
```

### Error de descarga de binarios
```bash
python3 -m camoufox clean
python3 -m camoufox fetch
```

### Error de permisos (Linux)
```bash
chmod +x ~/.local/share/camoufox/firefox/firefox
```

---

**Versión de Camoufox**: v135.0.1-beta.24  
**Última actualización**: Enero 2025
