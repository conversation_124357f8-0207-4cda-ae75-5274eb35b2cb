#!/usr/bin/env python3
"""
Groups Manager - Herramienta CLI para gestionar la base de datos de grupos
Permite añadir, consultar y gestionar grupos desde línea de comandos
"""

import sys
import argparse
from typing import List, Dict, Any
from database.database_manager import DatabaseManager


def add_group(repo: DatabaseManager, args) -> None:
    """Añade un grupo a la base de datos"""
    try:
        group_id = repo.add_group(
            user_id=1,  # Usuario por defecto
            nombre=args.nombre,
            url=args.url,
            in_flow=args.in_flow,
            metadata={"source": "manual_cli"}
        )
        print(f"✅ Grupo añadido exitosamente con ID: {group_id}")
    except Exception as e:
        print(f"❌ Error añadiendo grupo: {e}")


def list_groups(repo: DatabaseManager, args) -> None:
    """Lista grupos según filtros"""
    if args.in_flow_only:
        groups = repo.get_groups_in_flow()
        print(f"\n🔄 GRUPOS EN FLUJO ({len(groups)} encontrados)")
    else:
        groups = repo.get_all_groups(limit=args.limit)
        print(f"\n📋 TODOS LOS GRUPOS ({len(groups)} encontrados)")
    
    print("=" * 60)
    
    if not groups:
        print("   No hay grupos para mostrar")
        return
    
    for i, group in enumerate(groups, 1):
        status_icon = "🔄" if group['in_flow'] else "⏸️"
        print(f"{i:2d}. {status_icon} {group['nombre']}")
        print(f"     🔗 {group['url']}")
        print(f"     📅 Creado: {group['created_at']}")
        if group.get('metadata') and group['metadata'] != '{}':
            print(f"     📊 Metadata: {group['metadata']}")
        print()


def update_flow_status(repo: DatabaseManager, args) -> None:
    """Actualiza el estado in_flow de un grupo"""
    updated = repo.update_group_flow_status(args.url, args.in_flow)
    if updated:
        status = "en flujo" if args.in_flow else "fuera de flujo"
        print(f"✅ Grupo actualizado: ahora está {status}")
    else:
        print(f"❌ No se encontró grupo con URL: {args.url}")


def show_stats(repo: DatabaseManager, args) -> None:
    """Muestra estadísticas de la base de datos"""
    repo.print_stats()


def search_groups(repo: DatabaseManager, args) -> None:
    """Busca grupos por término en nombre o URL"""
    all_groups = repo.get_all_groups()
    search_term = args.term.lower()
    
    matching_groups = [
        group for group in all_groups
        if search_term in group['nombre'].lower() or search_term in group['url'].lower()
    ]
    
    print(f"\n🔍 BÚSQUEDA: '{args.term}' ({len(matching_groups)} resultados)")
    print("=" * 60)
    
    if not matching_groups:
        print("   No se encontraron grupos que coincidan")
        return
    
    for i, group in enumerate(matching_groups, 1):
        status_icon = "🔄" if group['in_flow'] else "⏸️"
        print(f"{i:2d}. {status_icon} {group['nombre']}")
        print(f"     🔗 {group['url']}")
        print()


def import_from_json(repo: DatabaseManager, args) -> None:
    """Importa grupos desde archivo JSON existente"""
    import json
    import os
    
    if not os.path.exists(args.file):
        print(f"❌ Archivo no encontrado: {args.file}")
        return
    
    try:
        with open(args.file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Adaptar formato según estructura del archivo
        groups_to_import = []
        
        if isinstance(data, list):
            # Lista directa de grupos
            for item in data:
                url = item.get('url') or item.get('href')
                if isinstance(item, dict) and url:
                    groups_to_import.append({
                        'nombre': item.get('text', item.get('nombre', 'Grupo sin nombre')),
                        'url': url,
                        'in_flow': False,
                        'metadata': {
                            'source': 'json_import',
                            'original_data': item
                        }
                    })
        elif isinstance(data, dict):
            # Diccionario con estructura específica
            if 'groups' in data:
                for item in data['groups']:
                    url = item.get('url') or item.get('href')
                    if url:
                        groups_to_import.append({
                            'nombre': item.get('text', item.get('nombre', 'Grupo sin nombre')),
                            'url': url,
                            'in_flow': False,
                            'metadata': {
                                'source': 'json_import',
                                'original_data': item
                            }
                        })
        
        if groups_to_import:
            added, duplicates = repo.bulk_add_groups(groups_to_import)
            print(f"✅ Importación completada: {added} añadidos, {duplicates} duplicados")
        else:
            print("⚠️ No se encontraron grupos válidos en el archivo")
            
    except Exception as e:
        print(f"❌ Error importando archivo: {e}")


def main():
    """Función principal del CLI"""
    parser = argparse.ArgumentParser(
        description="Gestor de base de datos de grupos de Facebook",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  %(prog)s stats                                    # Mostrar estadísticas
  %(prog)s list                                     # Listar todos los grupos
  %(prog)s list --in-flow-only                      # Solo grupos en flujo
  %(prog)s add "Mi Grupo" "https://facebook.com/groups/migrupo"
  %(prog)s add "Mi Grupo" "https://facebook.com/groups/migrupo" --in-flow
  %(prog)s update "https://facebook.com/groups/migrupo" --in-flow
  %(prog)s search "marketing"                       # Buscar grupos
  %(prog)s import results/groups_data.json          # Importar desde JSON
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Comandos disponibles')
    
    # Comando: stats
    stats_parser = subparsers.add_parser('stats', help='Mostrar estadísticas')
    
    # Comando: list
    list_parser = subparsers.add_parser('list', help='Listar grupos')
    list_parser.add_argument('--in-flow-only', action='store_true', 
                           help='Solo mostrar grupos en flujo')
    list_parser.add_argument('--limit', type=int, default=None,
                           help='Limitar número de resultados')
    
    # Comando: add
    add_parser = subparsers.add_parser('add', help='Añadir grupo')
    add_parser.add_argument('nombre', help='Nombre del grupo')
    add_parser.add_argument('url', help='URL del grupo')
    add_parser.add_argument('--in-flow', action='store_true',
                          help='Marcar grupo como en flujo')
    
    # Comando: update
    update_parser = subparsers.add_parser('update', help='Actualizar estado de grupo')
    update_parser.add_argument('url', help='URL del grupo')
    update_parser.add_argument('--in-flow', action='store_true',
                             help='Marcar como en flujo')
    update_parser.add_argument('--out-flow', dest='in_flow', action='store_false',
                             help='Marcar como fuera de flujo')
    
    # Comando: search
    search_parser = subparsers.add_parser('search', help='Buscar grupos')
    search_parser.add_argument('term', help='Término de búsqueda')
    
    # Comando: import
    import_parser = subparsers.add_parser('import', help='Importar desde JSON')
    import_parser.add_argument('file', help='Archivo JSON a importar')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Inicializar repositorio
    try:
        repo = DatabaseManager()
    except Exception as e:
        print(f"❌ Error inicializando repositorio: {e}")
        return
    
    # Ejecutar comando
    commands = {
        'stats': show_stats,
        'list': list_groups,
        'add': add_group,
        'update': update_flow_status,
        'search': search_groups,
        'import': import_from_json
    }
    
    if args.command in commands:
        commands[args.command](repo, args)
    else:
        print(f"❌ Comando desconocido: {args.command}")


if __name__ == "__main__":
    main()
