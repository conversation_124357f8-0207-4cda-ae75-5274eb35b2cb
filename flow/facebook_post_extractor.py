"""
Facebook Post Extractor - Clase para extracción de posts con humanización
Integra con Playwright/Camoufox para extracción optimizada de posts de Facebook
"""

import json
import random
import time
from datetime import datetime
from typing import List, Dict, Optional, Any
from pathlib import Path

# Importar Playwright sync API como el resto del proyecto
from playwright.sync_api import Page
from utils.path_utils import get_results_path


class FacebookPostExtractor:
    """
    Clase para extraer posts de Facebook con comportamiento humanizado.
    Se enfoca únicamente en la lógica de extracción, sin manejar el navegador.
    """
    
    def __init__(self, is_test: bool = False, test_max_posts: int = 10):
        """
            Inicializa el extractor con configuración por defecto
        
            Args:
            is_test: Si está en modo test (limita posts y guarda JSON)
            test_max_posts: Máximo de posts en modo test
        """

        self.posts_extraidos: List[Dict[str, Any]] = []
        self.ids_vistos: set[str] = set()

        # Configuración de test
        self.is_test = is_test
        self.test_max_posts = test_max_posts
        # Usar ruta absoluta anclada al proyecto para evitar duplicaciones de 'flow/flow'
        self.test_results_dir = get_results_path() / "test_extractions"
        self.script_js = self._get_embedded_script()

    
    def _get_embedded_script(self) -> str:
        """Script JavaScript embebido con mejoras de humanización.

        Notas:
        - No usamos f-string directo en el cuerpo JS porque hay template literals de JS (${...}).
          Un f-string intentaría evaluar las llaves internas como expresiones Python.
        - Empleamos marcadores (__MAX_POSTS_LIMIT__, __IS_TEST_MODE__) y luego hacemos replace().
        """
        max_posts_limit = self.test_max_posts if self.is_test else 999999
        is_test_mode = str(self.is_test).lower()

        script = """
        const humanizedFacebookScraper = (() => {
            
            const MAX_POSTS_LIMIT = __MAX_POSTS_LIMIT__;
            const IS_TEST_MODE = __IS_TEST_MODE__;
            // Map de posts finalizados (clave: posinset)
            let datosRecopilados = new Map();
            // Cola de posts pendientes (clave: posinset)
            let pendingPosts = new Map();
            let idsDePostsVistos = new Set(); // Deduplicación semántica adicional (URL/contenido)
            let observer = null;
            let scrollTimerId = null;
            let processingIntervalId = null;
            let isAtBottom = false;
            let estadisticas = {
                postsEncontrados: 0,
                clicksVerMas: 0,
                scrollsRealizados: 0,
                tiempoInicio: Date.now(),
                ciclosCola: 0
            };
            // Estado de gestión de gaps (posinset faltantes)
            let gapState = { pos: null, since: 0, trigger: 0, recoveryAt: 0, microScrollDone: false };
            // Gaps marcados como "phantom" tras intento fallido de recuperación
            let phantomGaps = new Set();

            // Configuración de tiempos humanizados + intervalo de cola
            const CONFIG = {
                SCROLL_MIN_MS: 2000,
                SCROLL_MAX_MS: 5000,
                PROCESSING_MIN_MS: 1500,
                PROCESSING_MAX_MS: 4000,
                CLICK_DELAY_MIN_MS: 200,
                CLICK_DELAY_MAX_MS: 800,
                PROCESSING_INTERVAL_MS: 500,
                // Configuración recuperación gaps
                GAP_TRIGGER_MIN_MS: 2000,
                GAP_TRIGGER_MAX_MS: 3000,
                GAP_MICRO_SCROLL_MIN_PX: 10,
                GAP_MICRO_SCROLL_MAX_PX: 15,
                GAP_POST_RECOVERY_WAIT_MS: 5000
            };

            const getRandomDelay = (min, max) => Math.random() * (max - min) + min;

            const generarIdUnico = (postData) => {
                const urlPart = postData.linkPost?.split('?')[0];
                if (urlPart && urlPart !== 'No encontrado') return urlPart;
                return `${postData.linkAutor}-${postData.contenidoPost.substring(0, 50)}`;
            };

            const humanClick = (element) => {
                return new Promise(resolve => {
                    const delay = getRandomDelay(CONFIG.CLICK_DELAY_MIN_MS, CONFIG.CLICK_DELAY_MAX_MS);
                    setTimeout(() => {
                        const event = new MouseEvent('click', { bubbles: true, cancelable: true, view: window });
                        element.dispatchEvent(event);
                        estadisticas.clicksVerMas++;
                        resolve();
                    }, delay);
                });
            };

            const extraerDatosDePost = (postElement) => {
                const posinset = parseInt(postElement.getAttribute('aria-posinset'), 10) || 0;
                const autorLinkElement = postElement.querySelector('h2 a, h3 a, h4 a');
                const nombreAutor = autorLinkElement?.innerText ?? 'No encontrado';
                const linkAutor = autorLinkElement?.href ?? 'No encontrado';
                const contentContainer = postElement.querySelector('[data-ad-rendering-role="story_message"], div[dir="auto"]:not(:has(h2, h3, h4))');
                const contenidoPost = contentContainer?.innerText?.trim() || 'No encontrado';
                const imagenes = Array.from(postElement.querySelectorAll('img'))
                    .filter(img => img.height > 80 && !img.src.includes('data:image'))
                    .map(img => img.src);
                const linkPost = postElement.querySelector('a[href*="/posts/"], a[href*="/videos/"], a[href*="/reel/"]')?.href ?? 'No encontrado';

                return {
                    posinset,
                    nombreAutor,
                    linkAutor,
                    contenidoPost,
                    imagenes,
                    linkPost,
                    timestamp: Date.now(),
                    processingTime: Date.now() - estadisticas.tiempoInicio
                };
            };

            const esPostListo = (postData) => (
                postData.posinset > 0 &&
                postData.nombreAutor !== 'No encontrado' &&
                postData.linkAutor !== 'No encontrado' &&
                postData.contenidoPost !== 'No encontrado'
            );

            const processPendingQueue = async () => {
                /*
                 * Cola de procesamiento con:
                 *  - Consecutividad estricta
                 *  - Mecanismo de recuperación de gaps (posinset faltantes) mediante micro-scroll
                 *  - Expiración de gap tras intento de recuperación + espera configurable
                 *
                 * Flujo gap:
                 *  1. Se detecta primer índice faltante (missingIndex) requerido antes de procesar un posinset mayor.
                 *  2. Se espera un tiempo aleatorio (GAP_TRIGGER_MIN_MS..MAX_MS).
                 *  3. Si persiste, se ejecuta micro-scroll abajo y luego arriba (10-15px) para "despertar" virtualización.
                 *  4. Se espera GAP_POST_RECOVERY_WAIT_MS. Si aún no aparece, se marca phantom y no bloquea más.
                 *  5. Si eventualmente aparece un posinset marcado phantom, se procesa normalmente y se remueve del set.
                 */
                if (IS_TEST_MODE && datosRecopilados.size >= MAX_POSTS_LIMIT) {
                    console.log(`%c🧪 MODO TEST: Límite de ${MAX_POSTS_LIMIT} posts alcanzado. Finalizando...`, 'color: #ff6b6b; font-weight: bold;');
                    finalizar();
                    return;
                }
                if (pendingPosts.size === 0) return;
                estadisticas.ciclosCola++;
    
                let procesadosEsteCiclo = 0;
    
                // Orden estricto por posinset para asegurar procesamiento secuencial
                const pendientesOrdenados = Array.from(pendingPosts.entries()).sort((a, b) => a[0] - b[0]);
    
                for (const [posinset, postElement] of pendientesOrdenados) {
                    // Si el elemento desapareció (virtualización), lo removemos
                    if (!document.body.contains(postElement)) {
                        pendingPosts.delete(posinset);
                        continue;
                    }
    
                    const postData = extraerDatosDePost(postElement);
    
                    if (!esPostListo(postData)) {
                        continue; // Reintentar en siguiente ciclo
                    }
    
                    // Verificar consecutividad estricta considerando gaps phantom
                    let missingIndex = null;
                    for (let i = 1; i < posinset; i++) {
                        if (!datosRecopilados.has(i) && !phantomGaps.has(i)) {
                            missingIndex = i;
                            break;
                        }
                    }
    
                    if (missingIndex !== null) {
                        // Gestión del estado del gap
                        if (gapState.pos !== missingIndex) {
                            gapState = {
                                pos: missingIndex,
                                since: Date.now(),
                                trigger: getRandomDelay(CONFIG.GAP_TRIGGER_MIN_MS, CONFIG.GAP_TRIGGER_MAX_MS),
                                recoveryAt: 0,
                                microScrollDone: false
                            };
                            console.log(`%c⏳ Gap detectado en pos ${missingIndex}. Esperando recuperación natural...`, 'color:#ff9800;');
                        } else {
                            const elapsed = Date.now() - gapState.since;
                            if (!gapState.microScrollDone && elapsed >= gapState.trigger) {
                                // Ejecutar micro-scroll de recuperación
                                const delta = Math.round(getRandomDelay(CONFIG.GAP_MICRO_SCROLL_MIN_PX, CONFIG.GAP_MICRO_SCROLL_MAX_PX));
                                try {
                                    window.scrollBy(0, delta);
                                    setTimeout(() => {
                                        // Pequeño ajuste para volver casi al mismo sitio
                                        window.scrollBy(0, -delta + Math.round(getRandomDelay(-1, 1)));
                                    }, 120 + getRandomDelay(0, 80));
                                    gapState.microScrollDone = true;
                                    gapState.recoveryAt = Date.now();
                                    console.log(`%c🧯 Micro-scroll recovery ejecutado para gap pos ${missingIndex}`, 'color:#ff9800; font-weight:bold;');
                                } catch (e) {
                                    console.log('Error en micro-scroll recovery', e);
                                }
                            } else if (gapState.microScrollDone && (Date.now() - gapState.recoveryAt) >= CONFIG.GAP_POST_RECOVERY_WAIT_MS) {
                                // Gap se da por fantasma
                                phantomGaps.add(gapState.pos);
                                console.log(`%c⏭️ Gap pos ${gapState.pos} marcado como phantom tras espera de ${CONFIG.GAP_POST_RECOVERY_WAIT_MS}ms`, 'color:#ff9800; font-weight:bold;');
                                gapState = { pos: null, since: 0, trigger: 0, recoveryAt: 0, microScrollDone: false };
                            }
                        }
                        continue;
                    } else {
                        // Si ya no hay gap y el gapState apuntaba a algo previo, reiniciar
                        if (gapState.pos !== null && datosRecopilados.has(gapState.pos)) {
                            gapState = { pos: null, since: 0, trigger: 0, recoveryAt: 0, microScrollDone: false };
                        }
                    }
    
                    const contentContainer = postElement.querySelector('[data-ad-rendering-role="story_message"]');
                    const verMasButton = contentContainer ?
                        Array.from(contentContainer.querySelectorAll('div[role="button"]'))
                            .find(el => ['Ver más', 'See More'].includes(el.textContent.trim())) : null;
    
                    if (verMasButton) {
                        await humanClick(verMasButton);
                        continue;
                    }
    
                    // Deduplicación por posinset y semántica
                    const idUnico = generarIdUnico(postData);
                    if (!datosRecopilados.has(posinset) && !idsDePostsVistos.has(idUnico)) {
                        datosRecopilados.set(posinset, postData);
                        idsDePostsVistos.add(idUnico);
                        procesadosEsteCiclo++;
                        estadisticas.postsEncontrados++;
                        resetAutoScrollTimer();
                        pendingPosts.delete(posinset);
    
                        if (phantomGaps.has(posinset)) {
                            // Si llegó un pos que se había marcado phantom, lo retiramos del set
                            phantomGaps.delete(posinset);
                        }
                        if (gapState.pos === posinset) {
                            gapState = { pos: null, since: 0, trigger: 0, recoveryAt: 0, microScrollDone: false };
                        }
    
                        if (IS_TEST_MODE && datosRecopilados.size >= MAX_POSTS_LIMIT) {
                            console.log(`%c🧪 MODO TEST: Límite de ${MAX_POSTS_LIMIT} posts alcanzado durante procesamiento.`, 'color: #ff6b6b; font-weight: bold;');
                            break;
                        }
                    } else {
                        pendingPosts.delete(posinset);
                    }
                }
    
                if (procesadosEsteCiclo > 0) {
                    console.log(`%c✅ +${procesadosEsteCiclo} posts procesados (cola, consecutivo+recovery). Total: ${datosRecopilados.size}.`, 'color: #28a745; font-weight: bold;');
                }
    
                if (IS_TEST_MODE && datosRecopilados.size >= MAX_POSTS_LIMIT) {
                    finalizar();
                }
            };

            const humanScroll = () => {
                if (isAtBottom) {
                    finalizar();
                    return;
                }

                const scrollableHeight = document.documentElement.scrollHeight - window.innerHeight;
                if (window.scrollY >= scrollableHeight - 100) {
                    console.log('%c🏁 Final del feed detectado. Esperando últimos procesos...', 'color: #ffc107; font-weight: bold;');
                    isAtBottom = true;
                    // Dar unos ciclos extra de cola antes de finalizar
                    setTimeout(() => finalizar(), getRandomDelay(1500, 3000));
                    return;
                }

                const scrollAmount = getRandomDelay(window.innerHeight * 0.3, window.innerHeight * 0.8);
                window.scrollBy(0, scrollAmount);
                estadisticas.scrollsRealizados++;
                console.log(`%c🔄 Scroll humano (${estadisticas.scrollsRealizados})`, 'color: #6c757d;');

                const nextScrollDelay = getRandomDelay(CONFIG.SCROLL_MIN_MS, CONFIG.SCROLL_MAX_MS);
                scrollTimerId = setTimeout(humanScroll, nextScrollDelay);
            };

            const resetAutoScrollTimer = () => {
                clearTimeout(scrollTimerId);
                if (!isAtBottom) {
                    const processingDelay = getRandomDelay(CONFIG.PROCESSING_MIN_MS, CONFIG.PROCESSING_MAX_MS);
                    scrollTimerId = setTimeout(humanScroll, processingDelay);
                }
            };

            const mutationCallback = (mutationsList) => {
                for (const mutation of mutationsList) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === 1) {
                                const candidates = node.matches && node.matches('div[aria-posinset]')
                                    ? [node]
                                    : Array.from(node.querySelectorAll('div[aria-posinset]'));
                                candidates.forEach(post => {
                                    const posinset = parseInt(post.getAttribute('aria-posinset'), 10);
                                    if (posinset &&
                                        !datosRecopilados.has(posinset) &&
                                        !pendingPosts.has(posinset)) {
                                        pendingPosts.set(posinset, post);
                                    }
                                });
                            }
                        });
                    }
                    if (mutation.type === 'characterData' || mutation.type === 'subtree') {
                        const postElement = mutation.target.parentElement?.closest('div[aria-posinset]');
                        if (postElement) {
                            const posinset = parseInt(postElement.getAttribute('aria-posinset'), 10);
                            if (posinset &&
                                !datosRecopilados.has(posinset) &&
                                !pendingPosts.has(posinset)) {
                                pendingPosts.set(posinset, postElement);
                            }
                        }
                    }
                }
            };

            function iniciar() {
                console.clear();
                console.log('%c🚀 Iniciando Scraper Humanizado v6.1 (cola posinset)... 🚀', 'color: #007bff; font-size: 18px; font-weight: bold;');

                const feedContainer = document.querySelector('div[role="feed"]');
                if (!feedContainer) {
                    console.error("Error: Contenedor del feed no encontrado.");
                    return { success: false, error: "Feed container not found" };
                }

                console.log("Analizando contenido inicial y llenando cola...");
                feedContainer.querySelectorAll('div[aria-posinset]').forEach(post => {
                    const posinset = parseInt(post.getAttribute('aria-posinset'), 10);
                    if (posinset && !pendingPosts.has(posinset) && !datosRecopilados.has(posinset)) {
                        pendingPosts.set(posinset, post);
                    }
                });

                observer = new MutationObserver(mutationCallback);
                const config = { childList: true, subtree: true, characterData: true };
                observer.observe(feedContainer, config);

                processingIntervalId = setInterval(processPendingQueue, CONFIG.PROCESSING_INTERVAL_MS);

                console.log('%c✅ Observer y cola de procesamiento activos. Iniciando scroll humanizado...', 'color: #17a2b8; font-size: 16px;');

                const inicialDelay = getRandomDelay(1000, 3000);
                setTimeout(humanScroll, inicialDelay);

                return { success: true, message: "Extractor iniciado correctamente" };
            }

            function finalizar() {
                if (observer) {
                    observer.disconnect();
                    observer = null;
                    console.log('%c🛑 Observer detenido.', 'color: #dc3545; font-size: 14px;');
                }
                if (scrollTimerId) {
                    clearTimeout(scrollTimerId);
                    scrollTimerId = null;
                    console.log('%c🛑 Scroll detenido.', 'color: #dc3545; font-size: 14px;');
                }
                if (processingIntervalId) {
                    clearInterval(processingIntervalId);
                    processingIntervalId = null;
                    console.log('%c🛑 Procesador de cola detenido.', 'color: #dc3545; font-size: 14px;');
                }

                // Intento final de procesar cola
                if (pendingPosts.size > 0) {
                    console.log('%c⌛ Procesando cola final antes de cerrar...', 'color: #ffc107;');
                }
                // Procesar sin esperas (sin clicks humanizados ya)
                pendingPosts.forEach((postElement, posinset) => {
                    if (!document.body.contains(postElement)) {
                        pendingPosts.delete(posinset);
                        return;
                    }
                    const postData = extraerDatosDePost(postElement);
                    if (esPostListo(postData) && !datosRecopilados.has(posinset)) {
                        const idUnico = generarIdUnico(postData);
                        if (!idsDePostsVistos.has(idUnico)) {
                            datosRecopilados.set(posinset, postData);
                            idsDePostsVistos.add(idUnico);
                            estadisticas.postsEncontrados++;
                        }
                    }
                });
                pendingPosts.clear();

                const tiempoTotal = (Date.now() - estadisticas.tiempoInicio) / 1000;
                const finalArray = Array.from(datosRecopilados.values()).sort((a, b) => a.posinset - b.posinset);

                console.log(`%c🎉 Extracción finalizada:`, 'color: #28a745; font-size: 18px; font-weight: bold;');
                console.log(`%c📊 Estadísticas:`, 'color: #17a2b8; font-size: 14px;');
                console.log(`   • Posts extraídos: ${finalArray.length}`);
                console.log(`   • Clicks "Ver más": ${estadisticas.clicksVerMas}`);
                console.log(`   • Scrolls realizados: ${estadisticas.scrollsRealizados}`);
                console.log(`   • Ciclos cola: ${estadisticas.ciclosCola}`);
                console.log(`   • Tiempo total: ${tiempoTotal.toFixed(1)}s`);

                if (finalArray.length > 0) {
                    const datosParaTabla = finalArray.map(({ timestamp, processingTime, ...resto }) => resto);
                    console.table(datosParaTabla, ['posinset', 'nombreAutor', 'contenidoPost', 'linkPost']);
                    console.log('%c📋 Datos JSON (ordenados por posinset):', 'color: #ffc107; font-size: 16px;');
                    console.log(JSON.stringify(finalArray, null, 2));
                }

                return {
                    posts: finalArray,
                    estadisticas: {
                        ...estadisticas,
                        tiempoTotal: tiempoTotal,
                        postsExtraidos: finalArray.length,
                        ordenAplicado: "posinset"
                    }
                };
            }

            function obtenerEstado() {
                return {
                    posts: Array.from(datosRecopilados.values()).sort((a, b) => a.posinset - b.posinset),
                    estadisticas: estadisticas,
                    activo: observer !== null,
                    colaPendiente: pendingPosts.size
                };
            }

            return {
                iniciar,
                finalizar,
                obtenerEstado,
                datosRecopilados: () => Array.from(datosRecopilados.values()),
                estadisticas: () => estadisticas
            };
        })();

        // Funciones globales de control
        function iniciarExtraccionHumanizada() {
            return humanizedFacebookScraper.iniciar();
        }

        function finalizarExtraccionHumanizada() {
            return humanizedFacebookScraper.finalizar();
        }

        function obtenerEstadoExtraccion() {
            return humanizedFacebookScraper.obtenerEstado();
        }
        """

        return script.replace("__MAX_POSTS_LIMIT__", str(max_posts_limit)).replace("__IS_TEST_MODE__", is_test_mode)

    def inject_script(self, page: Page) -> Dict[str, Any]:
        """
        Inyecta el script de extracción en la página usando page.evaluate()
        Esta solución evita problemas de CSP al ejecutar directamente en contexto aislado
        
        Args:
            page: Instancia de página de Playwright
            
        Returns:
            Dict con resultado de la inyección
        """
        try:
            # Ejecutar script directamente sin inyección DOM (evita CSP)
            page.evaluate(self.script_js)
            result = page.evaluate("iniciarExtraccionHumanizada()")
            return {"success": True, "result": result}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def start_extraction(self, page: Page) -> Dict[str, Any]:
        """
        Inicia la extracción de posts
        
        Args:
            page: Instancia de página de Playwright
            
        Returns:
            Dict con resultado del inicio
        """
        try:
            result = page.evaluate("iniciarExtraccionHumanizada()")
            return {"success": True, "result": result}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def stop_extraction(self, page: Page) -> Dict[str, Any]:
        """
        Detiene la extracción y obtiene los resultados
        
        Args:
            page: Instancia de página de Playwright
            
        Returns:
            Dict con posts extraídos y estadísticas
        """
        try:
            result = page.evaluate("finalizarExtraccionHumanizada()")
            
            if result and 'posts' in result:
                self.posts_extraidos = result['posts']
                # Actualizar IDs vistos
                for post in self.posts_extraidos:
                    if 'linkPost' in post and post['linkPost'] != 'No encontrado':
                        self.ids_vistos.add(post['linkPost'].split('?')[0])
                        
            return {"success": True, "result": result}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_extraction_status(self, page: Page) -> Dict[str, Any]:
        """
        Obtiene el estado actual de la extracción
        
        Args:
            page: Instancia de página de Playwright
            
        Returns:
            Dict con estado actual
        """
        try:
            status = page.evaluate("obtenerEstadoExtraccion()")
            return {"success": True, "status": status}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_extracted_posts(self) -> List[Dict[str, Any]]:
        """
        Retorna los posts extraídos localmente
        
        Returns:
            Lista de posts extraídos
        """
        return self.posts_extraidos

    def get_unique_posts_count(self) -> int:
        """
        Retorna el número de posts únicos extraídos
        
        Returns:
            Número de posts únicos
        """
        return len(self.ids_vistos)
    
    def ensure_there_is_not_old_posts_before_the_limit_post(
        self,
        limit_post: Dict[str, Any],
        list_of_posts: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Verifica que no hay posts antes del límite que no se deban haber procesado.
        Filtra posts para devolver solo los más recientes (anteriores al limit_post).
        
        Esta función actúa como un puente entre el extractor y el guardado, resolviendo
        el problema de condiciones de carrera donde se capturan posts más viejos después
        de encontrar el limit_post.
        
        Args:
            limit_post: Dict con información del post límite (content, author, etc.)
            list_of_posts: Lista de posts extraídos ordenados por posinset (más reciente primero)
            
        Returns:
            List[Dict]: Lista filtrada de posts válidos (solo los más recientes que el limit_post)
                       Puede devolver [] vacío si el limit_post es el único/más reciente
        """
        if not limit_post or not list_of_posts:
            return []
            
        print(f"🔍 Filtrando {len(list_of_posts)} posts contra limit_post...")
        
        # Buscar el índice del limit_post en la lista
        limit_index = -1
        for i, post in enumerate(list_of_posts):
            if self._check_limit_post_found([post], limit_post):
                limit_index = i
                print(f"🎯 Limit post encontrado en índice {limit_index} (posinset: {post.get('posinset', 'N/A')})")
                break
        
        if limit_index == -1:
            # No se encontró el limit_post, devolver todos los posts
            print(f"⚠️ Limit post NO encontrado en la lista - devolviendo todos los {len(list_of_posts)} posts")
            return list_of_posts
            
        if limit_index == 0:
            # El limit_post es el primer post (más reciente), no hay posts más nuevos
            print(f"🚫 Limit post es el más reciente - devolviendo lista vacía")
            return []
            
        # Devolver solo posts ANTES del limit_post (más recientes)
        filtered_posts = list_of_posts[:limit_index]
        
        print(f"✅ Filtro aplicado: {len(filtered_posts)} posts válidos de {len(list_of_posts)} totales")
        print(f"   - Posts descartados: {len(list_of_posts) - len(filtered_posts)} (incluyendo limit_post y posteriores)")
        
        # Log adicional para debug
        if filtered_posts:
            first_posinset = filtered_posts[0].get('posinset', 'N/A')
            last_posinset = filtered_posts[-1].get('posinset', 'N/A')
            limit_posinset = list_of_posts[limit_index].get('posinset', 'N/A')
            print(f"   - Rango válido: posinset {first_posinset} a {last_posinset}")
            print(f"   - Limit post: posinset {limit_posinset}")
        
        return filtered_posts

    def clear_extracted_data(self):
        """Limpia los datos extraídos localmente"""
        self.posts_extraidos.clear()
        self.ids_vistos.clear()

    # ==============================
    # MODO TEST: utilidades y ejecución
    # ==============================

    def _ensure_test_results_dir(self):
        """Crea el directorio de resultados de test si no existe"""
        if not self.test_results_dir.exists():
            self.test_results_dir.mkdir(parents=True, exist_ok=True)

    def save_test_results_to_json(self, group_info: Optional[Dict[str, Any]] = None) -> Optional[Path]:
        """Guarda los resultados actuales en un archivo JSON (solo modo test).
        
        Returns:
            Path | None: Ruta del archivo generado o None si no aplica.
        """
        if not self.is_test or not self.posts_extraidos:
            return None

        self._ensure_test_results_dir()
        ts = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        meta = {
            "timestamp_utc": ts,
            "is_test": self.is_test,
            "test_max_posts": self.test_max_posts,
            "posts_extraidos": len(self.posts_extraidos),
            "group_info": group_info or {}
        }
        payload = {
            "meta": meta,
            "posts": self.posts_extraidos
        }
        out_file = self.test_results_dir / f"posts_test_{ts}.json"
        with out_file.open("w", encoding="utf-8") as f:
            json.dump(payload, f, ensure_ascii=False, indent=2)
        return out_file

    def run_test_extraction(
        self,
        page: Page,
        wait_seconds: int = 60,
        poll_interval: float = 3.0,
        group_info: Optional[Dict[str, Any]] = None,
        limit_post: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Ejecuta el flujo completo de extracción en modo test.

        Pasos:
        1. Inyecta e inicia el script de extracción.
        2. Hace polling periódico del estado hasta que:
           - deje de estar activo (el script se autocompletó), o
           - alcance el límite configurado de posts, o
           - encuentre el limit_post (si se proporciona), o
           - exceda el tiempo máximo (wait_seconds).
        3. Fuerza finalización (stop_extraction) si seguía activo.
        4. Guarda los resultados en JSON.

        Args:
            page: Página de Playwright
            wait_seconds: Tiempo máximo de espera
            poll_interval: Intervalo de polling
            group_info: Información del grupo
            limit_post: Post límite para parar extracción (incluye group_id, content, author)
        """
        if not self.is_test:
            raise ValueError("run_test_extraction solo debe usarse en modo test (is_test=True).")

        inject_result = self.inject_script(page)
        if not inject_result.get("success"):
            return {"success": False, "stage": "inject", "error": inject_result.get("error")}

        start = time.time()
        last_status = None

        while True:
            try:
                status = self.get_extraction_status(page)
                if status.get("success"):
                    last_status = status["status"]
                    posts_len = len(last_status.get("posts", []))

                    # Verificar si se encontró el limit_post
                    limit_reached = False
                    if limit_post and last_status.get("posts"):
                        limit_reached = self._check_limit_post_found(last_status.get("posts", []), limit_post)
                        if limit_reached:
                            print(f"🎯 Limit post encontrado! Deteniendo extracción...")
                            break

                    if (not last_status.get("activo")) or posts_len >= self.test_max_posts:
                        break
                if time.time() - start > wait_seconds:
                    break
            except Exception as e:
                last_status = {"error": str(e)}
                break
            time.sleep(poll_interval)

        # Intentar finalizar y recoger datos definitivos
        try:
            final_result = self.stop_extraction(page)
            if final_result.get("success"):
                result_payload = final_result["result"]
            else:
                result_payload = final_result
        except Exception as e:
            result_payload = {"success": False, "error": str(e)}

        # Usar el puente de filtrado para asegurar que no hay posts viejos
        print(f"🔧 DEBUG: limit_post existe: {limit_post is not None}")
        print(f"🔧 DEBUG: result_payload success: {result_payload.get('success')}")
        print(f"🔧 DEBUG: posts count: {len(result_payload.get('posts', []))}")
        
        if limit_post and (result_payload.get("success") or result_payload.get("posts")):
            posts = result_payload["posts"]
            original_count = len(posts)
            
            print(f"🔄 Aplicando filtro anti-condición de carrera...")
            
            # Usar la función puente para filtrar posts correctamente
            filtered_posts = self.ensure_there_is_not_old_posts_before_the_limit_post(limit_post, posts)
            
            # Actualizar el payload con los posts filtrados
            result_payload["posts"] = filtered_posts
            result_payload["limit_post_found"] = len(filtered_posts) < original_count
            result_payload["original_posts_count"] = original_count
            result_payload["filtered_posts_count"] = len(filtered_posts)
            result_payload["race_condition_filtered"] = True
            
            print(f"🎯 Filtro completado: {len(filtered_posts)} posts válidos de {original_count} extraídos")

        # Guardar JSON si hay datos
        out_path = self.save_test_results_to_json(group_info=group_info)
        result_payload["test_json_path"] = str(out_path) if out_path else None
        result_payload["is_test"] = self.is_test

        return result_payload

    def _check_limit_post_found(self, extracted_posts: List[Dict[str, Any]], limit_post: Dict[str, Any]) -> bool:
        """
        Verifica si alguno de los posts extraídos coincide con el limit_post.

        Args:
            extracted_posts: Lista de posts extraídos del DOM
            limit_post: Post límite de la BD (incluye group_id, content, author)

        Returns:
            bool: True si se encontró el limit_post, False en caso contrario
        """
        if not extracted_posts or not limit_post:
            return False

        limit_content = limit_post.get('content', '') or limit_post.get('text', '')
        limit_author = limit_post.get('author', '')

        # Verificar cada post extraído
        for post in extracted_posts:
            post_content = post.get('contenidoPost', '')
            post_author = post.get('nombreAutor', '')

            # Calcular similitud usando algoritmo Jaccard simple
            if self._calculate_simple_jaccard(post_content, limit_content) >= 0.8:
                # Verificar también autor si está disponible
                if limit_author and post_author:
                    if self._calculate_simple_jaccard(post_author, limit_author) >= 0.9:
                        return True
                else:
                    # Si no hay autor, solo verificar contenido
                    return True

        return False

    def _calculate_simple_jaccard(self, text1: str, text2: str) -> float:
        """
        Calcula similitud Jaccard simple entre dos textos.

        Args:
            text1: Primer texto
            text2: Segundo texto

        Returns:
            float: Coeficiente Jaccard (0.0 a 1.0)
        """
        if not text1 or not text2:
            return 0.0

        # Normalizar textos
        import re

        def normalize(text):
            text = text.lower()
            text = re.sub(r'[^\w\s]', ' ', text)
            text = re.sub(r'\s+', ' ', text)
            return text.strip()

        norm1 = normalize(text1)
        norm2 = normalize(text2)

        # Convertir a conjuntos de palabras
        set1 = set(norm1.split())
        set2 = set(norm2.split())

        # Calcular Jaccard
        if len(set1.union(set2)) == 0:
            return 0.0

        return len(set1.intersection(set2)) / len(set1.union(set2))

    def wait_for_completion(self, page: Page, max_wait_minutes: int = 30) -> Dict[str, Any]:
        """
        Espera a que la extracción se complete o alcance el tiempo máximo
        
        Args:
            page: Instancia de página de Playwright
            max_wait_minutes: Tiempo máximo de espera en minutos
            
        Returns:
            Dict con resultado final
        """
        max_wait_seconds = max_wait_minutes * 60
        start_time = time.time()
        
        while True:
            current_time = time.time()
            elapsed = current_time - start_time
            
            if elapsed > max_wait_seconds:
                return self.stop_extraction(page)
            
            status_result = self.get_extraction_status(page)
            if status_result["success"] and not status_result["status"]["activo"]:
                # La extracción se completó automáticamente
                return status_result["status"]
            
            # Esperar antes de verificar nuevamente
            time.sleep(5)
    
    def wait_for_posts_to_load(self, page: Page, expected_posts: int = 10, timeout_seconds: int = 60) -> Dict[str, Any]:
        """
        Espera a que se cargue una cantidad específica de posts o se agote el timeout
        
        Args:
            page: Instancia de página de Playwright
            expected_posts: Número mínimo de posts esperados
            timeout_seconds: Timeout en segundos
            
        Returns:
            Dict con resultado de la espera
        """
        start_time = time.time()
        
        while True:
            elapsed = time.time() - start_time
            if elapsed > timeout_seconds:
                break
                
            # Verificar posts actuales
            try:
                posts_count = page.locator('div[aria-posinset]').count()
                if posts_count >= expected_posts:
                    return {
                        "success": True, 
                        "posts_found": posts_count,
                        "elapsed_time": elapsed
                    }
            except Exception as e:
                print(f"Error checking posts: {e}")
                
            time.sleep(2)
        
        return {
            "success": False, 
            "posts_found": page.locator('div[aria-posinset]').count(),
            "elapsed_time": elapsed,
            "message": "Timeout reached"
        }