#!/usr/bin/env python3
"""
Panel de visualización de base de datos con tkinter
Permite ver todas las tablas de la BD en pestañas separadas
Uso: python3 database_viewer.py
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import sys
import os
import json
from pathlib import Path


class DatabaseViewer:
    """Visor de base de datos con interfaz gráfica en tkinter"""
    
    def __init__(self):
        """Inicializar el visor de base de datos"""
        # Buscar la BD en el directorio padre
        self.db_path = Path(__file__).parent.parent / "results" / "groups.db"
        
        if not self.db_path.exists():
            print(f"❌ Error: Base de datos no encontrada en {self.db_path}")
            sys.exit(1)
        
        self.root = tk.Tk()
        self.root.title("📊 Visor de Base de Datos - Facebook Tests")
        self.root.geometry("1200x800")
        
        # Variables para tracking de selección
        self.selected_data = {}
        self.current_table = None
        # Variables para checkboxes en posts
        self.selected_posts = set()  # IDs de posts seleccionados
        self.post_checkboxes = {}    # Mapeo de ID a checkbox
        
        self.setup_ui()
        self.load_tables()
    
    def setup_ui(self):
        """Configurar la interfaz de usuario"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Título
        title_label = ttk.Label(main_frame, text="📊 Visor de Base de Datos", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # Notebook para pestañas
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Botones
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=(10, 0), sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="🔄 Actualizar", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cerrar", 
                  command=self.root.quit).pack(side=tk.RIGHT)
    
    def copy_selected_row(self, table_name):
        """Copiar los datos de la fila seleccionada al portapapeles"""
        if table_name not in self.selected_data or not self.selected_data[table_name]:
            messagebox.showwarning("Advertencia", "No hay ninguna fila seleccionada")
            return
        
        try:
            # Obtener datos de la fila seleccionada
            row_data = self.selected_data[table_name]
            
            # Crear texto formateado para copiar
            formatted_text = f"📋 Datos de fila - Tabla: {table_name}\n"
            formatted_text += "=" * 50 + "\n"
            
            for key, value in row_data.items():
                formatted_text += f"{key}: {value}\n"
            
            # Crear también formato JSON para datos estructurados
            json_data = json.dumps(row_data, indent=2, ensure_ascii=False)
            
            # Copiar al portapapeles (texto formateado + JSON)
            clipboard_content = formatted_text + "\n" + "📄 Formato JSON:\n" + json_data
            
            self.root.clipboard_clear()
            self.root.clipboard_append(clipboard_content)
            self.root.update()  # Actualizar el portapapeles
            
            # Mostrar confirmación
            messagebox.showinfo("✅ Copiado",
                              f"Datos de la fila copiados al portapapeles\n"
                              f"Tabla: {table_name}\n"
                              f"Campos: {len(row_data)}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error copiando datos: {e}")
    
    def delete_selected_posts(self):
        """Eliminar posts seleccionados de la base de datos"""
        if not self.selected_posts:
            messagebox.showwarning("Advertencia", "No hay posts seleccionados para eliminar")
            return
        
        # Confirmar eliminación
        count = len(self.selected_posts)
        confirm = messagebox.askyesno(
            "Confirmar eliminación",
            f"¿Estás seguro de que quieres eliminar {count} post(s) seleccionado(s)?\n\n"
            "Esta acción no se puede deshacer."
        )
        
        if not confirm:
            return
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Eliminar posts seleccionados
                placeholders = ','.join('?' * len(self.selected_posts))
                query = f"DELETE FROM posts WHERE id IN ({placeholders})"
                cursor.execute(query, list(self.selected_posts))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                # Limpiar selección
                self.selected_posts.clear()
                self.post_checkboxes.clear()
                
                # Actualizar la UI
                self.refresh_data()
                
                messagebox.showinfo(
                    "Eliminación completada",
                    f"Se eliminaron {deleted_count} post(s) correctamente."
                )
                
        except Exception as e:
            messagebox.showerror("Error", f"Error eliminando posts: {e}")
    
    def on_checkbox_click(self, event, tree, table_name):
        """Manejar clicks en la columna de checkbox"""
        region = tree.identify("region", event.x, event.y)
        if region == "cell":
            column = tree.identify("column", event.x, event.y)
            # Verificar si es la primera columna (checkbox)
            if column == '#1':  # Primera columna es el checkbox
                item = tree.identify("item", event.x, event.y)
                if item:
                    # Obtener valores de la fila
                    values = tree.item(item, 'values')
                    if len(values) > 1:  # Verificar que tiene suficientes columnas
                        # El ID está en la segunda columna (índice 1) porque la primera es el checkbox
                        try:
                            post_id = int(values[1])  # ID del post
                            current_state = values[0]  # Estado actual del checkbox
                            
                            # Alternar estado
                            new_state = '☑' if current_state == '☐' else '☐'
                            tree.set(item, '☑️', new_state)
                            
                            # Actualizar selección
                            if new_state == '☑':
                                self.selected_posts.add(post_id)
                                self.post_checkboxes[post_id]['selected'] = True
                            else:
                                self.selected_posts.discard(post_id)
                                self.post_checkboxes[post_id]['selected'] = False
                                
                        except (ValueError, IndexError):
                            pass  # Ignorar si no se puede obtener el ID
    
    def toggle_post_selection(self, post_id, var):
        """Alternar selección de un post específico (método legado)"""
        if var.get():
            self.selected_posts.add(post_id)
        else:
            self.selected_posts.discard(post_id)
    
    def toggle_all_posts(self, var, tree):
        """Seleccionar/deseleccionar todos los posts visibles"""
        select_all = var.get()
        
        # Actualizar todos los checkboxes visibles en el tree
        for child in tree.get_children():
            values = tree.item(child, 'values')
            if len(values) > 1:
                try:
                    post_id = int(values[1])  # ID del post
                    new_state = '☑' if select_all else '☐'
                    tree.set(child, '☑️', new_state)
                    
                    if select_all:
                        self.selected_posts.add(post_id)
                        if post_id in self.post_checkboxes:
                            self.post_checkboxes[post_id]['selected'] = True
                    else:
                        self.selected_posts.discard(post_id)
                        if post_id in self.post_checkboxes:
                            self.post_checkboxes[post_id]['selected'] = False
                except (ValueError, IndexError):
                    pass
    
    def on_row_select(self, event, table_name, tree, columns):
        """Manejar la selección de una fila en el treeview"""
        try:
            selection = tree.selection()
            if not selection:
                # Limpiar selección si no hay nada seleccionado
                if table_name in self.selected_data:
                    del self.selected_data[table_name]
                return
            
            # Obtener el item seleccionado
            item = selection[0]
            values = tree.item(item, 'values')
            
            # Crear diccionario con los datos de la fila
            row_data = {}
            for i, col in enumerate(columns):
                if i < len(values):
                    # Restaurar texto completo si fue truncado
                    value = values[i]
                    if isinstance(value, str) and value.endswith("..."):
                        # Intentar obtener el valor completo de la base de datos
                        try:
                            # Usar el ID (primera columna) para obtener datos completos
                            if columns[0].lower() in ['id'] and len(values) > 0:
                                full_data = self.get_full_row_data(table_name, columns[0], values[0])
                                if full_data and col in full_data:
                                    value = full_data[col]
                        except:
                            pass  # Mantener valor truncado si hay error
                    
                    row_data[col] = value
                else:
                    row_data[col] = ""
            
            # Guardar datos seleccionados
            self.selected_data[table_name] = row_data
            
        except Exception as e:
            print(f"Error en selección de fila: {e}")
    
    def get_full_row_data(self, table_name, id_column, id_value):
        """Obtener datos completos de una fila específica"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # Usar parámetros seguros para la consulta
                query = f"SELECT * FROM {table_name} WHERE {id_column} = ?"
                cursor.execute(query, (id_value,))
                row = cursor.fetchone()
                
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            print(f"Error obteniendo datos completos: {e}")
            return None

    def get_tables(self):
        """Obtener lista de tablas de la base de datos"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                return [table for table in tables if table != 'sqlite_sequence']
        except Exception as e:
            messagebox.showerror("Error", f"Error obteniendo tablas: {e}")
            return []
    
    def get_table_data(self, table_name):
        """Obtener datos de una tabla específica"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns_info = cursor.fetchall()
                columns = [col[1] for col in columns_info]
                
                cursor.execute(f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 1000")
                data = [dict(row) for row in cursor.fetchall()]
                
                return columns, data
                
        except Exception as e:
            messagebox.showerror("Error", f"Error obteniendo datos de {table_name}: {e}")
            return [], []
    
    def create_table_tab(self, table_name):
        """Crear pestaña para una tabla específica"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text=f"📋 {table_name}")
        
        tab_frame.columnconfigure(0, weight=1)
        tab_frame.rowconfigure(2, weight=1)
        
        # Info de la tabla
        info_frame = ttk.Frame(tab_frame)
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        columns, data = self.get_table_data(table_name)
        
        info_text = f"📊 Tabla: {table_name} | 📝 Registros: {len(data)} | 🏛️ Columnas: {len(columns)}"
        ttk.Label(info_frame, text=info_text, font=("Arial", 10)).pack(anchor=tk.W)
        
        # Frame para botones
        button_frame = ttk.Frame(tab_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=5, pady=(0, 5))
        
        # Botón copiar fila
        copy_button = ttk.Button(button_frame, text="📋 Copiar fila seleccionada",
                                command=lambda: self.copy_selected_row(table_name))
        copy_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Variables para checkboxes (definir antes de usar)
        select_all_var = None
        select_all_checkbox = None
        
        # Botones específicos para tabla de posts
        if table_name == "posts":
            delete_button = ttk.Button(button_frame, text="🗑️ Eliminar seleccionados",
                                     command=self.delete_selected_posts)
            delete_button.pack(side=tk.LEFT, padx=(10, 10))
            
            # Variables para checkboxes
            select_all_var = tk.BooleanVar()
            select_all_checkbox = ttk.Checkbutton(button_frame, text="Seleccionar todos",
                                                variable=select_all_var)
            select_all_checkbox.pack(side=tk.LEFT, padx=(10, 0))
        
        # Etiqueta de estado
        if table_name == "posts":
            status_label = ttk.Label(button_frame, text="Usa checkboxes para seleccionar posts a eliminar",
                                   foreground="blue")
        else:
            status_label = ttk.Label(button_frame, text="Selecciona una fila para copiar sus datos",
                                   foreground="gray")
        status_label.pack(side=tk.RIGHT)
        
        # Treeview para mostrar datos
        tree_frame = ttk.Frame(tab_frame)
        tree_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # Para posts, agregar columna de checkbox al inicio
        if table_name == "posts":
            # Limpiar checkboxes anteriores
            self.selected_posts.clear()
            self.post_checkboxes.clear()
            # Agregar columna de checkbox al inicio
            tree_columns = ['☑️'] + columns
        else:
            tree_columns = columns
            
        tree = ttk.Treeview(tree_frame, columns=tree_columns, show='headings')
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbars
        if table_name == "posts":
            v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
            v_scrollbar.grid(row=0, column=2, sticky=(tk.N, tk.S))
            tree.configure(yscrollcommand=v_scrollbar.set)
            
            h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
            h_scrollbar.grid(row=1, column=1, sticky=(tk.W, tk.E))
            tree.configure(xscrollcommand=h_scrollbar.set)
        else:
            v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
            v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
            tree.configure(yscrollcommand=v_scrollbar.set)
            
            h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
            h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
            tree.configure(xscrollcommand=h_scrollbar.set)
        
        # Configurar columnas
        for col in tree_columns:
            tree.heading(col, text=col)
            if col == '☑️':
                tree.column(col, width=50, minwidth=50, anchor=tk.CENTER)
            else:
                tree.column(col, width=150, minwidth=100)
        
        # Conectar eventos
        if table_name == "posts":
            # Para posts, manejar clicks en la columna de checkbox
            tree.bind('<Button-1>', lambda event: self.on_checkbox_click(event, tree, table_name))
            tree.bind('<<TreeviewSelect>>',
                     lambda event: self.on_row_select(event, table_name, tree, columns))
        else:
            # Para otras tablas, comportamiento normal
            tree.bind('<<TreeviewSelect>>',
                     lambda event: self.on_row_select(event, table_name, tree, columns))
        
        # Función para actualizar estado del botón
        def update_status():
            if table_name in self.selected_data and self.selected_data[table_name]:
                status_label.config(text="✅ Fila seleccionada - Lista para copiar", foreground="green")
                copy_button.config(state="normal")
            else:
                status_label.config(text="Selecciona una fila para copiar sus datos", foreground="gray")
                copy_button.config(state="normal")  # Mantener habilitado, manejamos el caso en el método
        
        # Actualizar estado inicial
        update_status()
        
        # Conectar evento para actualizar estado cuando cambie la selección (solo para no-posts)
        if table_name != "posts":
            tree.bind('<<TreeviewSelect>>',
                     lambda event: [self.on_row_select(event, table_name, tree, columns), update_status()])
        
        # Insertar datos
        for i, row_data in enumerate(data):
            if table_name == "posts":
                # Para posts, agregar columna de checkbox al inicio
                values = ['☐']  # Checkbox vacío por defecto
                for col in columns:
                    value = row_data.get(col, '')
                    if isinstance(value, str) and len(value) > 100:
                        value = value[:97] + "..."
                    values.append(value)
                
                item = tree.insert('', tk.END, values=values)
                
                # Guardar referencia para el toggle
                if 'id' in row_data and row_data['id'] is not None:
                    post_id = row_data['id']
                    # Asociar el item del tree con el post_id
                    tree.set(item, '☑️', '☐')
                    self.post_checkboxes[post_id] = {'item': item, 'selected': False}
            else:
                # Para otras tablas, comportamiento normal
                values = []
                for col in columns:
                    value = row_data.get(col, '')
                    if isinstance(value, str) and len(value) > 100:
                        value = value[:97] + "..."
                    values.append(value)
                tree.insert('', tk.END, values=values)
        
        # Actualizar el comando del checkbox "Seleccionar todos" para posts
        if table_name == "posts" and select_all_checkbox is not None:
            select_all_checkbox.configure(command=lambda: self.toggle_all_posts(select_all_var, tree))
    
    def load_tables(self):
        """Cargar todas las tablas en pestañas"""
        tables = self.get_tables()
        
        if not tables:
            messagebox.showwarning("Advertencia", "No se encontraron tablas en la base de datos")
            return
        
        for table in tables:
            self.create_table_tab(table)
    
    def refresh_data(self):
        """Actualizar todos los datos"""
        for tab in self.notebook.tabs():
            self.notebook.forget(tab)
        
        self.load_tables()
        messagebox.showinfo("Actualizado", "Datos actualizados correctamente")
    
    def run(self):
        """Ejecutar la aplicación"""
        self.root.mainloop()


def main():
    """Función principal"""
    print("🚀 Iniciando visor de base de datos...")
    
    try:
        app = DatabaseViewer()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
