# Testing purposes currently being evalueted
# Configuración de Filtros

## fake_authors.json

Este archivo contiene la lista de autores falsos que deben ser filtrados durante la extracción de posts de Facebook.

### Estructura del archivo:

```json
{
  "fake_authors": [
    "Enviar mensaje de WhatsApp",
    "Ver más",
    "Ampliar",
    // ... más autores falsos
  ],
  "description": "Lista de autores falsos comunes",
  "last_updated": "2025-09-08",
  "version": "1.0"
}
```

### Cómo agregar nuevos autores falsos:

1. Abrir `fake_authors.json`
2. Agregar el nuevo texto a la lista `fake_authors`
3. Actualizar `last_updated` con la fecha actual
4. Guardar el archivo

### Ejemplo de uso:

Si encuentras un post con autor "Contactar ahora", simplemente agrégalo a la lista:

```json
{
  "fake_authors": [
    "Enviar mensaje de WhatsApp",
    "Ver más", 
    "Ampliar",
    "Contactar ahora"  // ← Nuevo autor falso
  ]
}
```

### Fallback:

Si el archivo no existe o hay error al cargarlo, el sistema usará una lista básica hardcodeada como respaldo.

### Logging:

El sistema mostrará en consola:
- ✅ Cuántos autores falsos se cargaron
- 🚫 Cuando detecte un autor falso específico
- ⚠️ Si hay errores cargando el archivo
