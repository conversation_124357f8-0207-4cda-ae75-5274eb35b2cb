#!/usr/bin/env python3
# Testing purposes currently being evalueted
"""
Prototipo de funciones de comparación de posts para simple_observer
Enfoque: Algoritmos de comparación Jaccard para detección de límites y duplicados
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple


def calculate_jaccard(text1: str, text2: str, threshold: float = 0.8) -> float:
    """
    Calcula la similitud Jaccard entre dos textos.
    
    Args:
        text1: Primer texto a comparar
        text2: Segundo texto a comparar  
        threshold: Umbral de similitud (configurable, por defecto 0.8)
        
    Returns:
        float: Coeficiente de Jaccard (0.0 a 1.0)
    """
    # Normalizar textos: minúsculas, sin puntuación extra, espacios múltiples
    def normalize_text(text: str) -> str:
        # Convertir a minúsculas
        text = text.lower()
        # Remover caracteres especiales pero mantener espacios y letras
        text = re.sub(r'[^\w\s]', ' ', text)
        # Normalizar espacios múltiples
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    # Normalizar ambos textos
    norm_text1 = normalize_text(text1)
    norm_text2 = normalize_text(text2)
    
    # Crear conjuntos de palabras
    words1 = set(norm_text1.split())
    words2 = set(norm_text2.split())
    
    # Calcular intersección y unión
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    # Evitar división por cero
    if len(union) == 0:
        return 0.0
    
    # Calcular coeficiente de Jaccard
    jaccard_coefficient = len(intersection) / len(union)
    
    return jaccard_coefficient


def we_found_the_limit_post(content: str, author: Optional[str] = None, 
                           stored_posts: List[Dict[str, Any]] = None,
                           threshold: float = 0.8) -> bool:
    """
    Verifica si hemos encontrado el post límite (último post obtenido previamente).
    
    Args:
        content: Contenido del post actual
        author: Autor del post (opcional para mayor precisión)
        stored_posts: Lista de posts almacenados en la base de datos
        threshold: Umbral de similitud Jaccard (por defecto 0.8)
        
    Returns:
        bool: True si encontramos el post límite, False en caso contrario
    """
    if not stored_posts:
        return False
    
    # Buscar el post más reciente (índice 0 según la especificación)
    if len(stored_posts) == 0:
        return False
    
    # El post más reciente debería estar en el índice 0
    most_recent_post = stored_posts[0]
    stored_content = most_recent_post.get('text', '')
    stored_author = most_recent_post.get('author', '')
    
    # Calcular similitud de contenido
    content_similarity = calculate_jaccard(content, stored_content, threshold)
    
    # Si tenemos autor, verificar también similitud de autor
    if author and stored_author:
        author_similarity = calculate_jaccard(author, stored_author, threshold)
        # Requerir alta similitud tanto en contenido como en autor
        return content_similarity >= threshold and author_similarity >= 0.9
    else:
        # Solo verificar contenido si no tenemos información de autor
        return content_similarity >= threshold


def check_if_there_are_repeated_post_in_this_group_round(posts: List[Dict[str, Any]], 
                                                        threshold: float = 0.8) -> List[Dict[str, Any]]:
    """
    Revisa si en la ronda actual hay posts similares y los descarta.
    
    Args:
        posts: Lista de posts de la ronda actual
        threshold: Umbral de similitud Jaccard (por defecto 0.8)
        
    Returns:
        List[Dict[str, Any]]: Lista de posts sin duplicados
    """
    if len(posts) <= 1:
        return posts
    
    # Lista para posts únicos
    unique_posts = []
    # Lista para tracking de posts descartados
    discarded_posts = []
    
    for i, current_post in enumerate(posts):
        current_content = current_post.get('text', '')
        current_author = current_post.get('author', '')
        
        is_duplicate = False
        
        # Comparar con posts ya aceptados como únicos
        for unique_post in unique_posts:
            unique_content = unique_post.get('text', '')
            unique_author = unique_post.get('author', '')
            
            # Calcular similitud de contenido
            content_similarity = calculate_jaccard(current_content, unique_content, threshold)
            
            # Si la similitud supera el umbral, es un duplicado
            if content_similarity >= threshold:
                is_duplicate = True
                discarded_posts.append({
                    'original_index': i,
                    'post': current_post,
                    'similar_to_index': unique_post.get('index', 'unknown'),
                    'similarity_score': content_similarity,
                    'reason': 'content_similarity'
                })
                break
        
        # Si no es duplicado, agregarlo a la lista de únicos
        if not is_duplicate:
            unique_posts.append(current_post)
    
    # Log de resultados
    original_count = len(posts)
    final_count = len(unique_posts)
    discarded_count = len(discarded_posts)
    
    print(f"🔍 DETECCIÓN DE DUPLICADOS:")
    print(f"   📊 Posts originales: {original_count}")
    print(f"   ✅ Posts únicos: {final_count}")
    print(f"   🗑️ Posts descartados: {discarded_count}")
    
    if discarded_posts:
        print(f"   📋 Detalles de posts descartados:")
        for disc in discarded_posts:
            similarity = disc['similarity_score']
            print(f"      - Post {disc['original_index']} (similitud: {similarity:.2f})")
    
    return unique_posts


def load_test_posts_from_observations() -> List[Dict[str, Any]]:
    """
    Carga posts de prueba desde los archivos de observación existentes.

    Returns:
        List[Dict[str, Any]]: Lista de posts para pruebas
    """
    observations_dir = Path("results/observations")
    test_posts = []
    
    if not observations_dir.exists():
        print("❌ No se encontró directorio de observaciones")
        return []
    
    # Buscar el archivo de observación más reciente
    observation_files = list(observations_dir.glob("group_observation_*.json"))
    if not observation_files:
        print("❌ No se encontraron archivos de observación")
        return []
    
    # Usar el archivo más reciente
    latest_file = max(observation_files, key=lambda f: f.stat().st_mtime)
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extraer posts del archivo de observación
        content_extraction = data.get('content_extraction', {})
        complete_posts = content_extraction.get('complete_posts', {})
        posts = complete_posts.get('posts', [])
        
        print(f"📁 Cargados {len(posts)} posts desde {latest_file.name}")
        return posts
        
    except Exception as e:
        print(f"❌ Error cargando posts de prueba: {e}")
        return []


def test_jaccard_algorithm():
    """
    Prueba el algoritmo de Jaccard con datos reales de observación.
    """
    print("🧪 PRUEBA DEL ALGORITMO JACCARD")
    print("=" * 50)

    # Cargar posts de prueba
    test_posts = load_test_posts_from_observations()

    if len(test_posts) < 2:
        print("❌ Se necesitan al menos 2 posts para las pruebas")
        return

    print(f"📊 Posts disponibles para prueba: {len(test_posts)}")

    # PRUEBA 1: Comparación básica entre posts diferentes
    print(f"\n🔬 PRUEBA 1: Comparación entre posts diferentes")
    print("-" * 40)

    post1 = test_posts[0]
    post2 = test_posts[1] if len(test_posts) > 1 else test_posts[0]

    text1 = post1.get('text', '')
    text2 = post2.get('text', '')
    author1 = post1.get('author', '')
    author2 = post2.get('author', '')

    similarity = calculate_jaccard(text1, text2)

    print(f"Post 1 - Autor: {author1}")
    print(f"Post 1 - Texto: {text1[:100]}...")
    print(f"Post 2 - Autor: {author2}")
    print(f"Post 2 - Texto: {text2[:100]}...")
    print(f"Similitud Jaccard: {similarity:.3f}")

    # PRUEBA 2: Detección de límite
    print(f"\n🔬 PRUEBA 2: Detección de post límite")
    print("-" * 40)

    # Simular que el primer post es el límite almacenado
    stored_posts = [test_posts[0]]
    current_post_content = test_posts[0].get('text', '')
    current_post_author = test_posts[0].get('author', '')

    is_limit = we_found_the_limit_post(current_post_content, current_post_author, stored_posts)
    print(f"¿Es post límite? {is_limit}")

    # PRUEBA 3: Detección de duplicados en ronda
    print(f"\n🔬 PRUEBA 3: Detección de duplicados en ronda")
    print("-" * 40)

    # Crear posts con duplicados simulados para la prueba
    test_round_posts = test_posts[:3] if len(test_posts) >= 3 else test_posts

    # Agregar un duplicado modificado del primer post
    if len(test_posts) > 0:
        duplicate_post = test_posts[0].copy()
        duplicate_post['text'] = test_posts[0].get('text', '') + " (versión ligeramente modificada)"
        duplicate_post['index'] = 999  # Índice ficticio
        test_round_posts.append(duplicate_post)

    print(f"Posts antes de filtrar duplicados: {len(test_round_posts)}")
    unique_posts = check_if_there_are_repeated_post_in_this_group_round(test_round_posts)
    print(f"Posts después de filtrar duplicados: {len(unique_posts)}")


def test_edge_cases():
    """
    Prueba casos extremos y específicos del algoritmo.
    """
    print(f"\n🧪 PRUEBAS DE CASOS EXTREMOS")
    print("=" * 50)

    # CASO 1: Textos idénticos
    print(f"\n🔬 CASO 1: Textos idénticos")
    print("-" * 30)
    text = "Estamos contratando programadores en remoto"
    similarity = calculate_jaccard(text, text)
    print(f"Similitud de texto idéntico: {similarity:.3f}")

    # CASO 2: Textos muy similares (caso real de duplicados)
    print(f"\n🔬 CASO 2: Textos muy similares")
    print("-" * 30)
    text1 = "Estamos contratando programadores en remoto"
    text2 = "Estamos contratando personal programadores en remoto pago fijo"
    similarity = calculate_jaccard(text1, text2)
    print(f"Texto 1: {text1}")
    print(f"Texto 2: {text2}")
    print(f"Similitud: {similarity:.3f}")
    print(f"¿Es duplicado? {similarity >= 0.8}")

    # CASO 3: Textos completamente diferentes
    print(f"\n🔬 CASO 3: Textos completamente diferentes")
    print("-" * 30)
    text1 = "Estamos contratando programadores en remoto"
    text2 = "Vendo bicicleta usada en excelente estado"
    similarity = calculate_jaccard(text1, text2)
    print(f"Texto 1: {text1}")
    print(f"Texto 2: {text2}")
    print(f"Similitud: {similarity:.3f}")

    # CASO 4: Textos vacíos
    print(f"\n🔬 CASO 4: Textos vacíos")
    print("-" * 30)
    similarity1 = calculate_jaccard("", "")
    similarity2 = calculate_jaccard("", "texto no vacío")
    print(f"Vacío vs Vacío: {similarity1:.3f}")
    print(f"Vacío vs No vacío: {similarity2:.3f}")

    # CASO 5: Diferentes umbrales
    print(f"\n🔬 CASO 5: Prueba de diferentes umbrales")
    print("-" * 30)
    text1 = "Estamos contratando programadores"
    text2 = "Estamos contratando desarrolladores"

    for threshold in [0.5, 0.7, 0.8, 0.9]:
        similarity = calculate_jaccard(text1, text2, threshold)
        is_similar = similarity >= threshold
        print(f"Umbral {threshold}: Similitud {similarity:.3f} - ¿Similar? {is_similar}")


def test_real_world_scenarios():
    """
    Prueba escenarios del mundo real con posts reales.
    """
    print(f"\n🧪 ESCENARIOS DEL MUNDO REAL")
    print("=" * 50)

    # Cargar posts reales
    test_posts = load_test_posts_from_observations()

    if len(test_posts) < 5:
        print("❌ Se necesitan al menos 5 posts para pruebas completas")
        return

    # ESCENARIO 1: Simulación de llegada al límite
    print(f"\n🔬 ESCENARIO 1: Simulación de llegada al límite")
    print("-" * 40)

    # Simular base de datos con posts anteriores (los últimos 3 posts)
    stored_posts = test_posts[-3:]  # Los 3 posts más "antiguos"
    stored_posts.reverse()  # El más reciente primero (índice 0)

    print(f"Posts almacenados en BD: {len(stored_posts)}")
    print(f"Post más reciente en BD: {stored_posts[0].get('author', 'Sin autor')}")
    print(f"Texto: {stored_posts[0].get('text', '')[:100]}...")

    # Simular nueva extracción que encuentra el post límite
    new_posts = test_posts[:5]  # Nuevos posts extraídos

    for i, post in enumerate(new_posts):
        content = post.get('text', '')
        author = post.get('author', '')

        is_limit = we_found_the_limit_post(content, author, stored_posts)

        print(f"\nPost {i+1}: {author}")
        print(f"¿Es límite? {is_limit}")

        if is_limit:
            print(f"🎯 ¡LÍMITE ENCONTRADO! Deteniendo extracción en post {i+1}")
            break

    # ESCENARIO 2: Detección de spam/duplicados reales
    print(f"\n🔬 ESCENARIO 2: Detección de spam/duplicados")
    print("-" * 40)

    # Crear escenario con posts similares de spam
    spam_posts = [
        {
            'index': 0,
            'author': 'Spammer 1',
            'text': 'Estamos contratando programadores en remoto, pago fijo, excelente oportunidad'
        },
        {
            'index': 1,
            'author': 'Spammer 2',
            'text': 'Estamos contratando desarrolladores en remoto, pago fijo, gran oportunidad'
        },
        {
            'index': 2,
            'author': 'Usuario Real',
            'text': 'Busco ayuda con un problema de base de datos en MySQL'
        },
        {
            'index': 3,
            'author': 'Spammer 3',
            'text': 'Contratamos programadores remotos, pago fijo, excelente oportunidad laboral'
        }
    ]

    print(f"Posts originales: {len(spam_posts)}")
    for i, post in enumerate(spam_posts):
        print(f"  {i+1}. {post['author']}: {post['text'][:50]}...")

    # Probar con diferentes umbrales
    for threshold in [0.5, 0.6, 0.7]:
        print(f"\n--- Umbral {threshold} ---")
        unique_posts = check_if_there_are_repeated_post_in_this_group_round(spam_posts, threshold=threshold)

        print(f"Posts únicos después del filtro (umbral {threshold}):")
        for i, post in enumerate(unique_posts):
            print(f"  {i+1}. {post['author']}: {post['text'][:50]}...")


def analyze_similarity_matrix(posts: List[Dict[str, Any]]) -> None:
    """
    Analiza la matriz de similitud entre todos los posts para debugging.
    """
    print(f"\n🔬 MATRIZ DE SIMILITUD")
    print("-" * 40)

    n = len(posts)
    print(f"Analizando {n} posts:")

    # Mostrar headers
    print("\n" + " " * 15, end="")
    for i in range(n):
        print(f"Post{i+1:2d}", end="  ")
    print()

    # Calcular y mostrar matriz
    for i in range(n):
        author_i = posts[i].get('author', 'Sin autor')[:12]
        print(f"{author_i:>12s}  ", end="")

        for j in range(n):
            if i == j:
                print("  1.00", end="  ")
            else:
                text_i = posts[i].get('text', '')
                text_j = posts[j].get('text', '')
                similarity = calculate_jaccard(text_i, text_j)
                print(f"{similarity:6.2f}", end="  ")
        print()

    # Encontrar pares más similares
    print(f"\n🔍 PARES MÁS SIMILARES:")
    max_similarity = 0
    most_similar_pair = None

    for i in range(n):
        for j in range(i+1, n):
            text_i = posts[i].get('text', '')
            text_j = posts[j].get('text', '')
            similarity = calculate_jaccard(text_i, text_j)

            if similarity > max_similarity:
                max_similarity = similarity
                most_similar_pair = (i, j, similarity)

    if most_similar_pair:
        i, j, sim = most_similar_pair
        print(f"Posts {i+1} y {j+1}: {sim:.3f}")
        print(f"  Post {i+1}: {posts[i].get('text', '')[:60]}...")
        print(f"  Post {j+1}: {posts[j].get('text', '')[:60]}...")


if __name__ == "__main__":
    test_jaccard_algorithm()
    test_edge_cases()
    test_real_world_scenarios()

    # Análisis adicional de similitud
    print(f"\n" + "="*60)
    print("🔬 ANÁLISIS DETALLADO DE SIMILITUD")
    print("="*60)

    # Crear posts de spam para análisis detallado
    spam_posts = [
        {
            'index': 0,
            'author': 'Spammer 1',
            'text': 'Estamos contratando programadores en remoto, pago fijo, excelente oportunidad'
        },
        {
            'index': 1,
            'author': 'Spammer 2',
            'text': 'Estamos contratando desarrolladores en remoto, pago fijo, gran oportunidad'
        },
        {
            'index': 2,
            'author': 'Usuario Real',
            'text': 'Busco ayuda con un problema de base de datos en MySQL'
        },
        {
            'index': 3,
            'author': 'Spammer 3',
            'text': 'Contratamos programadores remotos, pago fijo, excelente oportunidad laboral'
        }
    ]

    analyze_similarity_matrix(spam_posts)
