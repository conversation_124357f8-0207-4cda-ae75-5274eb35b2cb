# Testing purposes currently being evalueted
"""
Simple Observer - Observador formal para extracción de posts de Facebook
Clase refactorizada desde proto_simple_observer.py
"""

import json
import random
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from playwright.sync_api import Page

from database.post_repository import PostRepository
from post_operations import PostExtractor
from group_utils import extract_group_info_from_url


class ManualStudyMetrics:
    """Estructura de métricas en memoria para modo manual (no persistente)"""
    def __init__(self):
        self.per_posts = []  # List[Dict]
        self.start_time = time.time()

    def add(self, **data):
        """Agregar métricas de un post"""
        self.per_posts.append(data)

    def summary(self):
        """Generar resumen estadístico"""
        times = [p['elapsed_total'] for p in self.per_posts if p.get('elapsed_total') is not None]
        exp_rate = sum(1 for p in self.per_posts if p.get('had_see_more')) / len(self.per_posts) if self.per_posts else 0
        return {
            'count': len(self.per_posts),
            'avg_time': round(sum(times)/len(times), 2) if times else 0,
            'p80': (lambda st: st[int(len(st)*0.8)] if st else 0)(sorted(times)),
            'expansion_rate': exp_rate
        }


class SimpleObserver:
    """
    Clase para observar y extraer posts de grupos de Facebook
    """
    
    def __init__(self):
        """
        Inicializa el observador simple
        """
        self.post_extractor = PostExtractor()
        self.post_repository = PostRepository()
    
    def observe_group_with_page(self, page: Page, group_url: str, manual: bool = False) -> Dict[str, Any]:
        """
        Observa un grupo específico con la página ya autenticada
        
        Args:
            page: Página de Playwright autenticada
            group_url: URL del grupo a observar
            
        Returns:
            Dict con resultados de la observación
        """
        print(f"🎯 INICIANDO OBSERVACIÓN EXPERIMENTAL")
        print(f"🔗 Grupo: {group_url}")
        print(f"=" * 60)
        
        results = {
            'group_url': group_url,
            'timestamp': datetime.now().isoformat(),
            'observations': []
        }
        
        try:
            # 1. Navegar al grupo
            print(f"🌐 Navegando al grupo...")
            page.goto(group_url, wait_until="domcontentloaded")
            print(f"✅ DOMContentLoaded alcanzado")
            
            # Pausa de 3 segundos para permitir carga adicional
            print(f"⏳ Pausando 3 segundos para carga adicional...")
            time.sleep(3)
            
            # Verificar que el grupo cargó correctamente
            self._verify_group_page_loaded(page, results)
            if not results.get('success', True):
                return results
            
            print(f"✅ Navegación completada")
            print(f"📍 URL actual: {page.url}")

            # 2. Extraer información del grupo
            group_info: Dict[str, Any] = self._extract_group_information(page, group_url)
            results['group_info'] = group_info

            # 3. Obtener posts almacenados para comparación
            stored_posts: List[Dict[str, Any]] = self._get_stored_posts(group_info.get('group_id'))

            # 4. MOSTRAR POST LÍMITE (PRIMERA INFORMACIÓN VISIBLE)
            print(f"\n" + "=" * 60)
            print(f"🎯 POST LÍMITE CONFIGURADO")
            print(f"=" * 60)

            if stored_posts:
                limit_post = stored_posts[0]
                author = limit_post.get('author', 'Sin autor')
                text = limit_post.get('text', 'Sin contenido')

                print(f"👤 Autor: {author}")
                print(f"📝 Contenido: {text[:50]}...")
                print(f"🛑 El scroll PARARÁ cuando encuentre este post")
            else:
                print(f"ℹ️ No hay post límite - extracción completa sin límite")

            print(f"=" * 60)

            # 5. Ejecutar scroll inteligente con extracción progresiva
            if manual:
                # MODO MANUAL: Control humano paso a paso, sin guardado BD
                posts_result: Dict[str, Any] = self._execute_manual_scroll_study(page, stored_posts)
            else:
                # MODO AUTOMÁTICO: Scroll inteligente estándar
                posts_result: Dict[str, Any] = self._execute_intelligent_scroll_extraction(page, stored_posts, group_info.get('group_id'))
            results['posts_extraction'] = posts_result

            # 6. Guardar posts en base de datos (SOLO si NO es modo manual)
            if not manual and posts_result.get('success') and group_info.get('group_id'):
                save_result: Dict[str, Any] = self._save_posts_to_database(
                    posts_result['posts'],
                    group_info['group_id']
                )
                results['database_save'] = save_result
            elif manual:
                print(f"📝 MODO MANUAL: Guardado en BD omitido deliberadamente")
                results['database_save'] = {'success': True, 'message': 'Omitido en modo manual'}

            results['success'] = True
            results['message'] = "✅ Observación completada exitosamente"
            
        except Exception as e:
            print(f"❌ Error en observación: {e}")
            results['success'] = False
            results['error'] = str(e)
            results['message'] = f"❌ Error en observación: {e}"
        
        return results
    
    def _verify_group_page_loaded(self, page: Page, results: Dict[str, Any]) -> None:
        """
        Verifica que la página del grupo se haya cargado correctamente
        """
        try:
            from utils.page_detectors import page_detectors
            max_attempts = 6  # 30 segundos total
            
            for attempt in range(max_attempts):
                group_detection = page_detectors.is_group_page(page)
                
                if group_detection['is_group_page']:
                    print(f"✅ Grupo detectado correctamente (intento {attempt+1})")
                    print(f"📊 Artículos detectados: {group_detection['details'].get('articles_count', 0)}")
                    return
                
                if attempt < max_attempts - 1:
                    print(f"⏳ Intento {attempt+1}/{max_attempts} - esperando 5s...")
                    time.sleep(5)
            
            # Si no se detecta grupo después de todos los intentos
            results['success'] = False
            results['message'] = "❌ Error: No se pudo detectar grupo válido después de 30s"
            
        except Exception as e:
            print(f"⚠️ Error verificando página de grupo: {e}")
            # Continuar sin verificación estricta
    
    def _extract_group_information(self, page: Page, group_url: str) -> Dict[str, Any]:
        """
        Extrae información del grupo desde la página y URL
        """
        try:
            # Extraer información desde la URL
            url_info = extract_group_info_from_url(group_url)
            
            # Extraer información adicional desde la página usando PostExtractor
            self.post_extractor.extract_group_info_from_page(page)

            # Obtener ID del grupo desde la base de datos
            from database.database_manager import DatabaseManager
            db_manager = DatabaseManager()
            group_id = self.post_extractor.get_group_id_for_database(db_manager, user_id=1)

            # Combinar información
            group_info = {
                'group_url': group_url,
                'group_slug': url_info.get('slug'),
                'group_id': group_id,
                'group_name': self.post_extractor.group_name,
                'current_url': page.url
            }
            
            print(f"📊 Información del grupo extraída:")
            print(f"   🏷️ Slug: {group_info['group_slug']}")
            print(f"   🆔 ID BD: {group_info['group_id']}")
            print(f"   📝 Nombre: {group_info['group_name']}")
            
            return group_info
            
        except Exception as e:
            print(f"❌ Error extrayendo información del grupo: {e}")
            return {
                'group_url': group_url,
                'error': str(e)
            }
    
    def _get_stored_posts(self, group_id: Optional[int]) -> List[Dict[str, Any]]:
        """
        Obtiene posts almacenados del grupo para comparación
        """
        if not group_id:
            print("⚠️ No hay group_id, no se pueden obtener posts almacenados")
            return []
        
        try:
            # Obtener SOLO el post más reciente como límite de referencia
            stored_posts_raw = self.post_repository.get_recent_posts_by_group(group_id, limit=1)
            print(f"📚 Posts límite obtenidos: {len(stored_posts_raw)}")

            # Mapear campos de BD a formato esperado por el sistema
            stored_posts = []
            for post in stored_posts_raw:
                mapped_post = {
                    'id': post.get('id'),
                    'author': post.get('author', ''),
                    'text': post.get('content', ''),  # Mapear 'content' a 'text'
                    'timestamp': post.get('timestamp', ''),
                    'link': post.get('author_link', ''),
                    'reactions': post.get('reactions', 0),
                    'comments': post.get('comments', 0)
                }
                stored_posts.append(mapped_post)

            if stored_posts:
                limit_post = stored_posts[0]
                author = limit_post.get('author', 'Sin autor')[:30]
                text = limit_post.get('text', 'Sin contenido')[:50]
                print(f"🎯 POST LÍMITE: {author} - {text}...")
            else:
                print(f"ℹ️ No hay posts previos - extracción sin límite")

            return stored_posts
        except Exception as e:
            print(f"❌ Error obteniendo posts almacenados: {e}")
            return []
    
    def _execute_intelligent_scroll_extraction(self, page: Page, stored_posts: List[Dict[str, Any]], group_id: Optional[int]) -> Dict[str, Any]:
        """
        Ejecuta scroll inteligente con extracción progresiva
        Basado en la lógica del proto_simple_observer que funcionaba al 100%
        """
        try:
            print(f"🚀 INICIANDO SCROLL INTELIGENTE CON EXTRACCIÓN PROGRESIVA")
            print(f"📜 SCROLL INTELIGENTE CON DETECCIÓN GLIMMER")
            print(f"=" * 60)

            # Cargar configuración de scrolls
            max_scrolls = self._load_scroll_configuration()

            # Preparar variables de control
            scroll_count = 0
            accumulated_posts = []  # Lista acumulativa de posts extraídos
            limit_found_during_scroll = False  # Flag para parar cuando se encuentre límite

            # Medición de tiempos
            cycle_start_time = time.time()
            scroll_times = []
            print(f"⏱️ [TIMING] Iniciando medición de tiempos del ciclo completo")

            print(f"\n📜 INICIANDO SCROLL INTELIGENTE")
            print(f"🎯 Objetivo: {max_scrolls} scrolls máximo")
            print(f"=" * 50)

            # BUCLE PRINCIPAL DE SCROLL INTELIGENTE
            while scroll_count < max_scrolls and not limit_found_during_scroll:
                scroll_count += 1
                scroll_start_time = time.time()

                print(f"\n📜 SCROLL {scroll_count}/{max_scrolls}")
                print(f"-" * 30)
                print(f"⏱️ [TIMING] Iniciando scroll {scroll_count} - {time.strftime('%H:%M:%S')}")

                # 1. Gestión de overlays
                overlay_closed = self._detect_and_close_overlays(page)
                if overlay_closed:
                    print(f"   ✅ Overlays cerrados")

                # 2. Ejecutar scroll con verificación de contenido nuevo
                print(f"   📜 Ejecutando scroll...")
                posts_before_scroll = page.locator('[data-ad-rendering-role="story_message"]').count()
                page.evaluate("window.scrollBy(0, 800)")
                time.sleep(2)  # Pausa después del scroll
                posts_after_scroll = page.locator('[data-ad-rendering-role="story_message"]').count()

                print(f"   📊 Posts en DOM: {posts_before_scroll} → {posts_after_scroll}")

                # Si no se cargaron posts nuevos en el DOM, el scroll no está funcionando
                if posts_after_scroll <= posts_before_scroll and scroll_count > 2:
                    print(f"   ⚠️ Scroll no cargó contenido nuevo en DOM")
                    # Intentar scroll más agresivo
                    page.evaluate("window.scrollBy(0, 1500)")
                    time.sleep(3)
                    posts_after_aggressive = page.locator('[data-ad-rendering-role="story_message"]').count()
                    print(f"   🔄 Scroll agresivo: {posts_after_scroll} → {posts_after_aggressive}")

                    if posts_after_aggressive <= posts_after_scroll:
                        print(f"   🔚 No hay más contenido para cargar - finalizando")
                        break

                # 3. Detectar y esperar glimmers
                glimmer_count = self._detect_glimmer_elements(page)
                print(f"   🔍 Elementos glimmer detectados: {glimmer_count}")
                if glimmer_count > 0:
                    print(f"   ⏳ Esperando que terminen de cargar los glimmers...")
                    self._wait_for_glimmers_to_disappear(page)

                # 4. EXTRACCIÓN INMEDIATA usando PostExtractor
                print(f"   🚀 [EXTRACCIÓN] Extracción 1 a 1 con expansión individual...")

                current_posts_result = self.post_extractor.extract_posts_one_by_one_with_expansion(
                    page=page,
                    stored_posts=stored_posts,
                    accumulated_posts=accumulated_posts,  # ✅ Acumulación progresiva
                    max_posts=10
                )

                # 5. Procesar resultados
                current_posts = current_posts_result.get('new_posts', [])
                limit_found = current_posts_result.get('limit_found', False)
                total_processed = current_posts_result.get('total_processed', 0)

                if current_posts:
                    print(f"   ✅ Posts nuevos extraídos: {len(current_posts)}")
                    print(f"   📊 Total posts acumulados: {len(accumulated_posts)}")

                    # Verificar si se encontró el límite
                    if limit_found:
                        print(f"   🎯 ¡LÍMITE ENCONTRADO! Parando scroll inmediatamente")
                        print(f"   🛑 EXTRACCIÓN COMPLETADA - Se encontró el post límite de referencia")
                        limit_found_during_scroll = True
                        break  # Salir inmediatamente del bucle
                else:
                    print(f"   📭 No se extrajeron posts en este scroll")

                # 6. Medición de tiempo del scroll
                scroll_end_time = time.time()
                scroll_duration = scroll_end_time - scroll_start_time
                scroll_times.append(scroll_duration)
                print(f"⏱️ [TIMING] Scroll {scroll_count} completado en {scroll_duration:.2f}s")

                # 7. Verificar si hay más contenido para cargar
                if not current_posts:
                    print(f"   ⚠️ No se extrajeron posts nuevos en este scroll")

                    # Si llevamos varios scrolls sin posts nuevos, verificar si llegamos al final
                    if scroll_count >= 3:  # Después de 3 scrolls sin contenido nuevo
                        print(f"   🔍 Verificando si hay más contenido disponible...")

                        # Contar posts totales antes y después de scroll adicional
                        posts_before = page.locator('[data-ad-rendering-role="story_message"]').count()
                        page.evaluate("window.scrollBy(0, 1500)")  # Scroll más grande
                        time.sleep(3)  # Esperar más tiempo
                        posts_after = page.locator('[data-ad-rendering-role="story_message"]').count()

                        if posts_after <= posts_before:
                            print(f"   🔚 Confirmado: No hay más contenido para cargar ({posts_before} → {posts_after} posts)")
                            break
                        else:
                            print(f"   ✅ Contenido adicional detectado ({posts_before} → {posts_after} posts)")

                # 8. Detectar si estamos en un bucle de contenido repetido
                if scroll_count >= 5 and len(accumulated_posts) == 0:
                    print(f"   ⚠️ 5 scrolls sin posts nuevos - posible problema de carga")
                    print(f"   🔍 Intentando scroll más agresivo...")
                    page.evaluate("window.scrollBy(0, 2000)")  # Scroll más grande
                    time.sleep(5)  # Esperar más tiempo

            # Medición final de tiempos
            cycle_end_time = time.time()
            total_cycle_time = cycle_end_time - cycle_start_time

            print(f"\n⏱️ [TIMING] RESUMEN DE TIEMPOS:")
            print(f"   🕐 Tiempo total del ciclo: {total_cycle_time:.2f}s ({total_cycle_time/60:.1f} min)")
            print(f"   📜 Total de scrolls realizados: {len(scroll_times)}")
            if scroll_times:
                avg_scroll_time = sum(scroll_times) / len(scroll_times)
                min_scroll_time = min(scroll_times)
                max_scroll_time = max(scroll_times)
                print(f"   ⏱️ Tiempo promedio por scroll: {avg_scroll_time:.2f}s")
                print(f"   ⚡ Scroll más rápido: {min_scroll_time:.2f}s")
                print(f"   🐌 Scroll más lento: {max_scroll_time:.2f}s")

            # Resultado final
            print(f"\n📊 RESUMEN DE EXTRACCIÓN:")
            print(f"   🎯 Límite encontrado: {limit_found_during_scroll}")
            if limit_found_during_scroll:
                print(f"   ✅ ÉXITO: Se encontró el post límite - extracción de posts NUEVOS completada")
            else:
                print(f"   ℹ️ No se encontró límite - se extrajeron todos los posts disponibles")
            print(f"   📝 Posts nuevos extraídos: {len(accumulated_posts)}")
            print(f"   📜 Scrolls realizados: {scroll_count}/{max_scrolls}")

            return {
                'success': True,
                'posts': accumulated_posts,
                'total_count': len(accumulated_posts),
                'limit_found': limit_found_during_scroll,
                'total_scrolls': scroll_count,
                'total_time': total_cycle_time,
                'extraction_method': 'IntelligentScrollExtraction'
            }

        except Exception as e:
            print(f"❌ Error en scroll inteligente: {e}")
            return {
                'success': False,
                'error': str(e),
                'posts': []
            }
    
    def _save_posts_to_database(self, posts: List[Dict[str, Any]], group_id: int) -> Dict[str, Any]:
        """
        Guarda posts en la base de datos
        """
        try:
            if not posts:
                return {
                    'success': False,
                    'message': 'No hay posts para guardar'
                }
            
            print(f"💾 Guardando {len(posts)} posts en base de datos...")
            
            # Transformar posts al formato esperado por PostRepository (Dict, no tupla)
            posts_for_db = []
            for post in posts:
                post_data = {
                    'author': post.get('author', ''),
                    'author_link': post.get('authorLink', ''),
                    'text': post.get('text', ''),
                    'timestamp': post.get('timestamp', datetime.now().isoformat()),
                    'reactions': post.get('reactions', 0),
                    'comments': post.get('comments', 0),
                    'shares': post.get('shares', 0),
                    'link': post.get('link', ''),
                    'images': post.get('images', []),
                    'metadata': json.dumps(post.get('metadata', {}))
                }
                posts_for_db.append(post_data)
            
            # Guardar usando PostRepository
            save_result = self.post_repository.save_all(posts_for_db, group_id, user_id=1)
            
            print(f"📊 RESULTADO DEL GUARDADO:")
            print(f"   ✅ Posts guardados: {save_result['saved_count']}")
            print(f"   ⏭️ Posts omitidos: {save_result['skipped_count']}")
            print(f"   🔄 Duplicados: {save_result['duplicate_count']}")
            print(f"   ❌ Inválidos: {save_result['invalid_count']}")
            
            return save_result
            
        except Exception as e:
            print(f"❌ Error guardando en base de datos: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _load_scroll_configuration(self) -> int:
        """
        Carga configuración de scrolls desde archivo JSON
        Basado en la lógica del proto_simple_observer
        """
        try:
            config_file = Path(__file__).parent / "config" / "scroll_config.json"

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    scroll_config = json.load(f)
                    max_scrolls = scroll_config.get('scroll_per_group', 10)
                    print(f"📜 Configuración cargada desde archivo: {max_scrolls} scrolls por grupo")
                    return max_scrolls
            else:
                print(f"⚠️ Archivo de configuración no encontrado, usando valor por defecto: 10 scrolls")
                return 10

        except Exception as e:
            print(f"⚠️ Error cargando configuración: {e}")
            print(f"✅ Usando valor por defecto: 10 scrolls")
            return 10

    def _detect_and_close_overlays(self, page: Page) -> bool:
        """
        Detecta y cierra overlays/modales que bloquean la vista
        Basado en la lógica del proto_simple_observer
        """
        try:
            # Detectores de vista individual/overlay
            overlay_indicators = [
                '[role="dialog"]',
                '[aria-modal="true"]',
                '[data-pagelet*="PermalinkPost"]',
                '.x1n2onr6',
                '[data-testid="modal-backdrop"]'
            ]

            # Verificar URL para detectar vista individual
            current_url = page.url
            url_has_overlay = any(pattern in current_url for pattern in ['/photo/', '/posts/', '/watch/', '/permalink/'])

            # Verificar si hay overlay realmente visible
            overlay_detected = False
            visible_overlays = 0

            for selector in overlay_indicators:
                try:
                    if self._is_element_truly_visible(page, selector):
                        overlay_detected = True
                        visible_overlays += 1
                except Exception:
                    continue

            if overlay_detected or url_has_overlay:
                # Cierre agresivo: múltiples intentos de Escape
                for attempt in range(3):
                    page.keyboard.press("Escape")
                    time.sleep(0.5)

                    # Verificar si se cerró
                    still_visible = False
                    for selector in overlay_indicators:
                        try:
                            if self._is_element_truly_visible(page, selector):
                                still_visible = True
                                break
                        except Exception:
                            continue

                    if not still_visible:
                        return True

                return False
            else:
                return False

        except Exception as e:
            print(f"   ❌ Error detectando overlays: {e}")
            return False

    def _is_element_truly_visible(self, page: Page, selector: str) -> bool:
        """
        Verifica si un elemento está realmente visible (no solo presente en DOM)
        """
        try:
            js_check = f"""
            () => {{
                const elements = document.querySelectorAll('{selector}');
                for (let element of elements) {{
                    if (!element) continue;

                    const style = window.getComputedStyle(element);
                    const rect = element.getBoundingClientRect();

                    const isVisible = (
                        style.display !== 'none' &&
                        style.visibility !== 'hidden' &&
                        style.opacity !== '0' &&
                        rect.width > 0 &&
                        rect.height > 0 &&
                        rect.top >= 0 &&
                        rect.left >= 0
                    );

                    if (isVisible) return true;
                }}
                return false;
            }}
            """
            return page.evaluate(js_check)
        except Exception:
            return False

    def _detect_glimmer_elements(self, page: Page) -> int:
        """
        Detecta elementos glimmer (placeholders de carga)
        Basado en la lógica del proto_simple_observer
        """
        glimmer_selectors = [
            '[style*="--glimmer-stagger-time"]',
            '[class*="glimmer"]',
            '[data-testid*="glimmer"]',
            '.placeholder',
            '[aria-label*="Loading"]',
            '[aria-label*="Cargando"]'
        ]

        total_glimmers = 0
        for selector in glimmer_selectors:
            try:
                count = page.locator(selector).count()
                total_glimmers += count
            except Exception:
                continue

        return total_glimmers

    def _wait_for_glimmers_to_disappear(self, page: Page, max_wait_seconds: int = 15) -> bool:
        """
        Espera a que desaparezcan los elementos glimmer
        Mejorado con detección más inteligente
        """
        try:
            start_time = time.time()
            initial_glimmer_count = self._detect_glimmer_elements(page)

            if initial_glimmer_count == 0:
                print(f"   ✅ No hay glimmers iniciales")
                return True

            print(f"   ⏳ Esperando {initial_glimmer_count} glimmers...")
            stable_count = 0  # Contador para verificar estabilidad
            last_count = initial_glimmer_count

            while time.time() - start_time < max_wait_seconds:
                current_count = self._detect_glimmer_elements(page)

                if current_count == 0:
                    print(f"   ✅ Glimmers desaparecieron en {time.time() - start_time:.1f}s")
                    return True

                # Verificar si el conteo se estabilizó (no está cambiando)
                if current_count == last_count:
                    stable_count += 1
                    if stable_count >= 6:  # 3 segundos estable
                        print(f"   ⚠️ Glimmers estables en {current_count} - continuando")
                        return True
                else:
                    stable_count = 0  # Reset si cambió

                last_count = current_count
                time.sleep(0.5)  # Verificar cada 500ms

            final_count = self._detect_glimmer_elements(page)
            print(f"   ⚠️ Timeout esperando glimmers: {initial_glimmer_count} → {final_count} después de {max_wait_seconds}s")
            return False

        except Exception as e:
            print(f"   ❌ Error esperando glimmers: {e}")
            return False

    def _is_nested_story(self, story_element):
        """
        Verifica si un story_message está anidado dentro de otro (es hijo/compartido)
        Basado en proto_simple_observer
        """
        try:
            # Buscar ancestros que sean story_message
            # Si tiene ancestros story_message, es un post anidado (hijo)
            parent_stories = story_element.locator('xpath=ancestor::*[@data-ad-rendering-role="story_message"]')
            is_nested = parent_stories.count() > 0

            if is_nested:
                print(f"   🔍 Historia anidada detectada (tiene {parent_stories.count()} ancestros)")

            return is_nested
        except Exception as e:
            print(f"   ⚠️ Error verificando anidamiento: {e}")
            return False

    def _extract_author_robust(self, post_container):
        """
        Extrae autor usando estrategias múltiples del proto_simple_observer
        Evita OCR de imágenes y texto falso
        """
        try:
            author_name = 'Autor desconocido'

            # Estrategia 1: aria-label en enlaces (más confiable)
            try:
                link_with_aria = post_container.locator('a[aria-label]').first
                if link_with_aria.count() > 0:
                    aria_label = link_with_aria.get_attribute('aria-label')
                    if aria_label and len(aria_label) > 2 and 'http' not in aria_label:
                        # Verificar que no sea texto de imagen
                        if not self._is_likely_image_text(aria_label):
                            author_name = aria_label
                            return author_name.strip()
            except:
                pass

            # Estrategia 2: SVG con aria-label
            if author_name == 'Autor desconocido':
                try:
                    svg_with_aria = post_container.locator('svg[aria-label]').first
                    if svg_with_aria.count() > 0:
                        aria_label = svg_with_aria.get_attribute('aria-label')
                        if aria_label and len(aria_label) > 2 and 'http' not in aria_label:
                            if not self._is_likely_image_text(aria_label):
                                author_name = aria_label
                                return author_name.strip()
                except:
                    pass

            # Estrategia 3: Atributo title
            if author_name == 'Autor desconocido':
                try:
                    element_with_title = post_container.locator('[title]').first
                    if element_with_title.count() > 0:
                        title = element_with_title.get_attribute('title')
                        if title and len(title) > 2 and 'http' not in title:
                            if not self._is_likely_image_text(title):
                                author_name = title
                                return author_name.strip()
                except:
                    pass

            # Estrategia 4: Selectores tradicionales como fallback
            if author_name == 'Autor desconocido':
                try:
                    fallback_selectors = [
                        'b > span', 'strong > span', 'h3 a',
                        '[data-ad-rendering-role="profile_name"] a',
                        'h3 span', '[role="link"]', 'strong a'
                    ]

                    for selector in fallback_selectors:
                        element = post_container.locator(selector).first
                        if element.count() > 0:
                            text = element.text_content()
                            if text and len(text.strip()) > 2:
                                if not self._is_likely_image_text(text.strip()):
                                    author_name = text.strip()
                                    return author_name
                except:
                    pass

            return author_name

        except Exception as e:
            return 'Autor desconocido'

    def _is_likely_image_text(self, text):
        """
        Detecta si el texto es probablemente OCR de imagen
        """
        if not text:
            return False

        text_lower = text.lower()

        # Patrones comunes de OCR de imagen
        image_patterns = [
            'puede ser una imagen',
            'puede ser un',
            'puede ser una',
            'imagen de',
            'no hay texto alternativo',
            'alt text',
            'image may contain',
            'may contain',
            'photo',
            'imagen'
        ]

        for pattern in image_patterns:
            if pattern in text_lower:
                return True

        # Si el texto es muy corto y contiene caracteres raros
        if len(text) < 10 and any(char in text for char in ['·', '•', '…', '⋅']):
            return True

        return False

    def _execute_manual_scroll_study(self, page: Page, stored_posts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Modo manual de estudio: scroll controlado por Enter, sin guardado BD
        Mide tiempos reales entre autorizaciones y extracción del siguiente post
        """
        print(f"\n🧪 MODO MANUAL DE ESTUDIO ACTIVADO")
        print(f"=" * 60)
        print(f"📋 INSTRUCCIONES:")
        print(f"   ENTER = realizar scroll + extracción")
        print(f"   s = salir del estudio")
        print(f"   Cada post será validado individualmente")
        print(f"=" * 60)

        metrics = ManualStudyMetrics()
        seen_keys = set()
        iteration = 0

        while True:
            try:
                # Control humano: esperar autorización
                user_input = input(f"\n⏸️ Esperando acción (ENTER=continuar, s=salir): ").strip().lower()
                if user_input == 's':
                    print(f"🛑 Estudio manual finalizado por usuario")
                    break

                iteration += 1
                print(f"\n📍 ITERACIÓN {iteration}")
                print(f"-" * 30)

                # Medir tiempo total desde autorización
                t_start = time.time()

                # Contar posts DOM antes del scroll
                before_dom = page.locator('[data-ad-rendering-role="story_message"]').count()
                print(f"   📊 Posts en DOM antes: {before_dom}")

                # Ejecutar scroll SOLO después de autorización
                print(f"   📜 Ejecutando scroll...")
                page.evaluate("window.scrollBy(0, 800)")
                time.sleep(3.0)  # TIMING MEJORADO: Esperar más tiempo para estabilización DOM

                # Gestión de glimmers
                glimmers = self._detect_glimmer_elements(page)
                if glimmers > 0:
                    print(f"   🔍 Detectados {glimmers} glimmers - esperando...")
                    self._wait_for_glimmers_to_disappear(page, max_wait_seconds=6)

                # Buscar candidato no procesado (SOLO POSTS PADRE, NO ANIDADOS)
                all_posts = page.locator('[data-ad-rendering-role="story_message"]').all()

                # FILTRAR: Solo posts padre (no historias anidadas/compartidas)
                parent_posts = []
                for post in all_posts:
                    if not self._is_nested_story(post):
                        parent_posts.append(post)
                    else:
                        print(f"   ⏭️ Saltando historia anidada (compartida)")

                print(f"   📊 Total posts: {len(all_posts)}, Posts padre: {len(parent_posts)}")

                candidate = None
                for idx, el in enumerate(parent_posts):
                    try:
                        # Extracción rápida para generar key única
                        raw_text = (el.text_content() or "")[:100]

                        # EXTRACCIÓN ROBUSTA DE AUTOR (basada en proto_simple_observer)
                        author_guess = self._extract_author_robust(el)

                        # Key única basada en autor + texto
                        key = f"{author_guess}::{raw_text}"
                        if key and key not in seen_keys:
                            candidate = el
                            seen_keys.add(key)
                            print(f"   ✅ Candidato padre encontrado: {author_guess[:20]}...")
                            break
                    except Exception as e:
                        print(f"   ⚠️ Error procesando elemento {idx}: {e}")
                        continue

                if not candidate:
                    print(f"   ❌ No se encontró nuevo post visible - continuar...")
                    continue

                # Expandir 'Ver más' si existe
                had_see_more = False
                expansion_time = 0

                for btn in candidate.locator('div[role="button"]').all():
                    try:
                        txt = (btn.text_content() or "").lower()
                        if 'ver más' in txt or 'see more' in txt:
                            had_see_more = True
                            print(f"   🚀 Expandiendo 'Ver más'...")
                            t_exp_s = time.time()
                            btn.click()
                            time.sleep(0.8)
                            t_exp_e = time.time()
                            expansion_time = t_exp_e - t_exp_s
                            print(f"   ✅ Expansión completada en {expansion_time:.2f}s")
                            break
                    except Exception as e:
                        print(f"   ⚠️ Error expandiendo: {e}")
                        continue

                if not had_see_more:
                    print(f"   📝 Post sin 'Ver más'")

                # Extracción completa del post
                post_data = self.post_extractor.extract_complete_post_data(candidate, iteration-1, page)

                if not post_data:
                    print(f"   ❌ Falló extracción completa - siguiente")
                    continue

                # Validaciones clave
                autor = post_data.get('author', '')
                texto = post_data.get('text', '')

                # Validar autor correcto (evitar OCR de imagen)
                autor_ok = len(autor) > 2 and not self.post_extractor._is_fake_author(autor)
                texto_ok = 'ver más' not in texto.lower() and 'see more' not in texto.lower()

                # Detectar posible OCR falso
                suspect_ocr = (autor.strip() and texto.startswith(autor) and len(texto) > 40)

                # Tiempo total
                elapsed_total = time.time() - t_start

                # Guardar métricas
                metrics.add(
                    iteration=iteration,
                    author=autor[:60],
                    author_ok=autor_ok,
                    text_len=len(texto),
                    had_see_more=had_see_more,
                    elapsed_total=elapsed_total,
                    expansion_time=expansion_time,
                    suspect_ocr=suspect_ocr
                )

                # Imprimir bloque consolidado
                print(f"   📊 RESULTADOS:")
                print(f"   👤 Autor: {autor[:25]} {'✅' if autor_ok else '❌'}")
                print(f"   📝 Texto: {len(texto)} chars {'✅' if texto_ok else '❌'}")
                print(f"   🔍 Ver más: {'✅' if had_see_more else '❌'}")
                print(f"   ⏱️ Tiempo total: {elapsed_total:.2f}s {'✅' if 5<=elapsed_total<=8 else '⚠️'}")
                print(f"   🖼️ OCR sospechoso: {'⚠️' if suspect_ocr else 'OK'}")

                # Verificar límite si hay stored_posts
                if stored_posts:
                    from post_comparison import we_found_the_limit_post
                    limit_found = we_found_the_limit_post(
                        content=texto,
                        author=autor,
                        stored_posts=stored_posts,
                        threshold=0.8
                    )
                    if limit_found:
                        print(f"   🎯 ¡LÍMITE ENCONTRADO! Finalizando estudio")
                        break

            except KeyboardInterrupt:
                print(f"\n🛑 Estudio interrumpido por Ctrl+C")
                break
            except Exception as e:
                print(f"   ❌ Error en iteración {iteration}: {e}")
                continue

        # Generar resumen final
        summary = metrics.summary()
        print(f"\n" + "=" * 60)
        print(f"📊 RESUMEN FINAL DEL ESTUDIO MANUAL")
        print(f"=" * 60)
        print(f"📊 Total capturados: {summary['count']}")
        print(f"⏱️ Tiempo promedio: {summary['avg_time']}s")
        print(f"⏱️ P80: {summary['p80']}s")
        print(f"🚀 Expansion rate: {summary['expansion_rate']:.1%}")
        print(f"=" * 60)

        # NOTA: Exportación a archivo deshabilitada deliberadamente en modo manual (política interna actual)

        return {
            'success': summary['count'] > 0,
            'manual': True,
            'posts_count': summary['count'],
            'average_time': summary['avg_time'],
            'p80_time': summary['p80'],
            'expansion_rate': summary['expansion_rate'],
            'posts': []  # No retornar posts reales para evitar guardado accidental
        }


# Función de compatibilidad para mantener la API existente
def observe_group_with_page(page: Page, group_url: str = "https://www.facebook.com/groups/118581375315200/?sorting_setting=CHRONOLOGICAL") -> Dict[str, Any]:
    """
    Función de compatibilidad - usa la clase SimpleObserver
    
    Args:
        page: Página de Playwright autenticada
        group_url: URL del grupo a observar
        
    Returns:
        Dict con resultados de la observación
    """
    observer = SimpleObserver()
    return observer.observe_group_with_page(page, group_url)
