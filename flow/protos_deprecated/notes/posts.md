# Testing purposes currently being evalueted
# Aprobado por el líder

# Catálogo Integral de Métodos Relacionados con la Extracción de Posts de Facebook

Este documento cataloga TODOS los métodos (públicos, privados, helpers y funciones de compatibilidad) involucrados directa o indirectamente en el flujo de extracción de posts, partiendo del flujo principal:
[FacebookFlowOrchestrator.main()](flow/flow.py:319) → [FacebookFlowOrchestrator (instancia)](flow/flow.py:19) → [SimpleObserver.observe_group_with_page()](flow/simple_observer.py:31) → [SimpleObserver.\_execute_intelligent_scroll_extraction()](flow/simple_observer.py:225) → [PostExtractor.extract_posts_one_by_one_with_expansion()](flow/post_operations.py:96) → [we_found_the_limit_post()](flow/post_comparison.py:232) (o variante utilitaria [we_found_the_limit_post()](flow/utils/post_utils.py:61))

Convenciones:

- Tipo de Método:
  - Orquestador: Controla un flujo completo llamando a múltiples métodos subordinados.
  - Operacional / Helper: Ejecuta una operación concreta.
  - Compatibilidad: Mantiene API antigua o capa wrapper.
- Relaciones: Se listan llamadas a otros métodos (internos/externos) relevantes.
- Detección de Límite: Uso de [we_found_the_limit_post()](flow/post_comparison.py:232) o su variante utilitaria [we_found_the_limit_post()](flow/utils/post_utils.py:61).
- Similitud / Duplicados: Uso de funciones Jaccard ([calculate_jaccard()](flow/post_comparison.py:226) / [calculate_jaccard()](flow/utils/post_utils.py:10)) y filtrado.

====================================================================

## 1. Archivo: flow/flow.py

Clase principal de orquestación global (login, tracking, test puntual de extracción).

### [FacebookFlowOrchestrator.**init**()](flow/flow.py:22)

Tipo: Orquestador (Inicialización)
Descripción: Configura rutas, modo headless y crea instancias principales: login, tracker, repositorio.
Parámetros:

- config_path (Optional[str]): Ruta config; si es None usa [get_config_str()](flow/utils/path_utils.py:?) (no importado aquí con línea disponible en este catálogo).
- headless (bool): Modo navegador.
  Retorna: None
  Relaciones: Instancia [FacebookLoginManager](flow/login_system.py:?), [GroupsTracker](flow/groups_tracker.py:?), [DatabaseManager](flow/database/database_manager.py:?), no directa a extracción de posts pero habilita el contexto Playwright.

### [FacebookFlowOrchestrator.execute_login_flow()](flow/flow.py:43)

Tipo: Orquestador
Descripción: Ejecuta login y guarda referencias (page, context, browser) para etapas posteriores.
Parámetros: None
Retorno: Dict[str, Any] con 'success', referencias de Playwright.
Relaciones: Llama [FacebookLoginManager.login()](flow/login_system.py:?).

### [FacebookFlowOrchestrator.execute_humanization()](flow/flow.py:63)

Tipo: Orquestador
Descripción: Ejecuta sesión de humanización previa (opcional) antes de navegar a grupos.
Parámetros:

- skip (bool): Salta el proceso si True.
  Retorno: Dict resultado.
  Relaciones: Instancia [PageDetectors](flow/utils/page_detectors.py:?) y [IntelligentHumanizer.start_home_session()](flow/utils/intelligent_humanizer.py:?).

### [FacebookFlowOrchestrator.execute_groups_tracking()](flow/flow.py:100)

Tipo: Orquestador
Descripción: Trackea página de grupos y muestra/resumen + integración a BD.
Parámetros:

- skip (bool)
  Retorno: Dict
  Relaciones: Usa [GroupsTracker.track_groups_page()](flow/groups_tracker.py:?), [self.\_integrate_groups_to_repository()](flow/flow.py:154).

### [FacebookFlowOrchestrator.\_integrate_groups_to_repository()](flow/flow.py:154)

Tipo: Helper interno
Descripción: Transforma links recolectados en estructura de grupo y los inserta en BD.
Parámetros: groups_links (list)
Retorno: Dict con estadísticas de integración.
Relaciones: Usa [extract_group_slug_from_url()](flow/group_utils.py:?), [generate_group_name_from_slug()](flow/group_utils.py:?), [extract_group_info_from_url()](flow/group_utils.py:?).

### [FacebookFlowOrchestrator.execute_queue_processing()](flow/flow.py:236)

Tipo: Orquestador
Descripción: Procesa una cola de grupos (potencialmente para extracción futura sistemática).
Parámetros: skip (bool)
Retorno: Dict estadísticas
Relaciones: Instancia [QueueProcessor](flow/queue_processor.py:?).

### [FacebookFlowOrchestrator.early_exit_for_debug_purposes()](flow/flow.py:274)

Tipo: Orquestador de debug puntual
Descripción: Pausa flujo para inspección manual del estado (browser abierto).
Parámetros: exit_point (str), results (Dict opcional)
Retorno: bool (True = salir flujo).
Relaciones: No llama extracción, herramienta de inspección.

### [FacebookFlowOrchestrator.cleanup()](flow/flow.py:309)

Tipo: Helper limpieza
Descripción: Cierra navegador si existe.
Retorno: None

### [main()](flow/flow.py:319)

Tipo: Orquestador global CLI
Descripción: Punto de entrada: parsea argumentos, ejecuta login/humanización/tracking y TEST de extracción directa:

- Crea instancia [SimpleObserver](flow/simple_observer.py:19)
- Llama [SimpleObserver.observe_group_with_page()](flow/simple_observer.py:31)
  Parámetros CLI: --headless, --config, --skip-humanization
  Retorno: None
  Relaciones Clave Extracción: Inicia el flujo central de posts.

====================================================================

## 2. Archivo: flow/simple_observer.py

Refactor principal de observación de un solo grupo con scroll inteligente y extracción incremental.

### [SimpleObserver.**init**()](flow/simple_observer.py:24)

Tipo: Helper inicialización
Descripción: Prepara instancias de [PostExtractor](flow/post_operations.py:16) y [PostRepository](flow/database/post_repository.py:?).

### [SimpleObserver.observe_group_with_page()](flow/simple_observer.py:31)

Tipo: Orquestador (NIVEL GRUPO)
Descripción: Flujo completo por grupo:

1. Navegar al grupo
2. Verificar carga ([SimpleObserver.\_verify_group_page_loaded()](flow/simple_observer.py:118))
3. Extraer info del grupo ([SimpleObserver.\_extract_group_information()](flow/simple_observer.py:146))
4. Obtener post límite ([SimpleObserver.\_get_stored_posts()](flow/simple_observer.py:185))
5. Ejecutar scroll inteligente + extracción progresiva ([SimpleObserver.\_execute_intelligent_scroll_extraction()](flow/simple_observer.py:225))
6. Guardar posts ([SimpleObserver.\_save_posts_to_database()](flow/simple_observer.py:399))
   Parámetros: page (Page), group_url (str)
   Retorno: Dict con resultados completos
   Relaciones: Invoca casi todos los helpers internos y [PostExtractor.extract_posts_one_by_one_with_expansion()](flow/post_operations.py:96).

### [SimpleObserver.\_verify_group_page_loaded()](flow/simple_observer.py:118)

Tipo: Helper validación
Descripción: Detecta si la página actual corresponde a un grupo mediante heurísticas (usa [page_detectors.is_group_page()](flow/utils/page_detectors.py:?)).
Parámetros: page, results (dict acumulador)
Retorno: None (mutación de results)

### [SimpleObserver.\_extract_group_information()](flow/simple_observer.py:146)

Tipo: Helper info grupo
Descripción: Combina info parseada de URL ([extract_group_info_from_url()](flow/group_utils.py:?)) + metadatos obtenidos vía [PostExtractor.extract_group_info_from_page()](flow/post_operations.py:30) y obtiene ID BD con [PostExtractor.get_group_id_for_database()](flow/post_operations.py:56).
Retorno: Dict group_info

### [SimpleObserver.\_get_stored_posts()](flow/simple_observer.py:185)

Tipo: Helper acceso BD
Descripción: Obtiene el post más reciente para usarlo como límite. Mapea campos BD → formato interno.
Retorno: List[Dict] (0 o 1 elemento según disponibilidad)
Relaciones: [PostRepository.get_recent_posts_by_group()](flow/database/post_repository.py:?).

### [SimpleObserver.\_execute_intelligent_scroll_extraction()](flow/simple_observer.py:225)

Tipo: Orquestador (NIVEL SCROLL)
Descripción: Bucle de scroll controlado con:

- Configuración dinámica ([SimpleObserver.\_load_scroll_configuration()](flow/simple_observer.py:447))
- Detección/cierre overlays ([SimpleObserver.\_detect_and_close_overlays()](flow/simple_observer.py:470))
- Detección glimmers ([SimpleObserver.\_detect_glimmer_elements()](flow/simple_observer.py:561), [SimpleObserver.\_wait_for_glimmers_to_disappear()](flow/simple_observer.py:585))
- Extracción incremental por llamada a [PostExtractor.extract_posts_one_by_one_with_expansion()](flow/post_operations.py:96) acumulando en memoria y detectando límite.
  Parámetros: page, stored_posts, group_id
  Retorno: Dict con métricas: posts, total_count, limit_found, total_scrolls, total_time
  Relaciones Clave: PostExtractor (extracción micro), detección de límite delegada dentro de PostExtractor mediante [we_found_the_limit_post()](flow/post_comparison.py:232).

### [SimpleObserver.\_save_posts_to_database()](flow/simple_observer.py:399)

Tipo: Helper persistencia
Descripción: Transforma posts a formato BD y ejecuta guardado masivo.
Relaciones: [PostRepository.save_all()](flow/database/post_repository.py:?).

### [SimpleObserver.\_load_scroll_configuration()](flow/simple_observer.py:447)

Tipo: Helper configuración
Descripción: Lee scroll_config.json para determinar scrolls máximos.

### [SimpleObserver.\_detect_and_close_overlays()](flow/simple_observer.py:470)

Tipo: Helper interfaz
Descripción: Detecta modales/overlays y los cierra con secuencia de Escape.

### [SimpleObserver.\_is_element_truly_visible()](flow/simple_observer.py:528)

Tipo: Helper DOM JS
Descripción: Ejecuta JS para verificar visibilidad real (display, opacity, bounding rect).

### [SimpleObserver.\_detect_glimmer_elements()](flow/simple_observer.py:561)

Tipo: Helper placeholders
Descripción: Cuenta placeholders de carga (glimmers) por selectores heurísticos.

### [SimpleObserver.\_wait_for_glimmers_to_disappear()](flow/simple_observer.py:585)

Tipo: Helper sincronización
Descripción: Espera desaparición o estabilización de glimmers (ventana 500 ms polling).

### [observe_group_with_page()](flow/simple_observer.py:630)

Tipo: Compatibilidad
Descripción: Función wrapper externa para mantener API previa. Instancia [SimpleObserver](flow/simple_observer.py:19) y delega en [SimpleObserver.observe_group_with_page()](flow/simple_observer.py:31).

====================================================================

## 3. Archivo: flow/post_operations.py

Núcleo de extracción granular (nivel post). Contiene lógica completa de expansión, filtrado, deduplicación y estructuración.

### Clase: [PostExtractor](flow/post_operations.py:16)

#### [PostExtractor.**init**()](flow/post_operations.py:22)

Tipo: Helper inicialización
Descripción: Ajusta umbral duplicados y caches.

#### [PostExtractor.extract_group_info_from_page()](flow/post_operations.py:30)

Tipo: Helper grupo
Descripción: Usa URL actual para derivar slug e info. Guarda en self.current_group_info.
Relaciones: [extract_group_info_from_url()](flow/group_utils.py:?).

#### [PostExtractor.get_group_id_for_database()](flow/post_operations.py:56)

Tipo: Helper BD
Descripción: Consulta DatabaseManager para recuperar el ID del grupo existente y setea self.group_id_for_database / self.group_name.

#### [PostExtractor.extract_posts_one_by_one_with_expansion()](flow/post_operations.py:96)

Tipo: Orquestador (NIVEL POST MICRO-CICLO)
Descripción Detallada:

- Recorre elementos DOM padre filtrando anidados ([PostExtractor.\_is_nested_story()](flow/post_operations.py:626))
- Expande cada post si tiene botón 'Ver más'
- Detección temprana duplicados (rápida) mediante:
  - [PostExtractor.\_extract_author_quick()](flow/post_operations.py:695)
  - [PostExtractor.\_extract_text_quick()](flow/post_operations.py:714)
  - [PostExtractor.\_is_duplicate_quick()](flow/post_operations.py:724)
- Extracción completa: [PostExtractor.extract_parent_post_with_nested_content()](flow/post_operations.py:250)
- Verificación duplicado final: [PostExtractor.\_is_duplicate_final()](flow/post_operations.py:739)
- Detección de límite: [we_found_the_limit_post()](flow/post_comparison.py:232) (import compat)
- Agregado a acumuladores.
  Parámetros: page, stored_posts, accumulated_posts (mutado), max_posts
  Retorno: Dict {new_posts, limit_found, total_processed}

#### [PostExtractor.extract_parent_post_with_nested_content()](flow/post_operations.py:250)

Tipo: Helper composición
Descripción: Extrae post padre (llama [PostExtractor.extract_complete_post_data()](flow/post_operations.py:302)), filtra autores falsos ([PostExtractor.\_is_fake_author()](flow/post_operations.py:645)), concatena contenido anidado (hijos story_message).
Retorno: Dict o None.

#### [PostExtractor.extract_complete_post_data()](flow/post_operations.py:302)

Tipo: Helper extracción completa
Descripción: Construye objeto post con link, autor, texto, assets, métricas sociales, ID generado ([PostExtractor.\_generate_post_id()](flow/post_operations.py:776)).
Relaciones: [PostExtractor.extract_post_link()](flow/post_operations.py:473), [PostExtractor.extract_author_data()](flow/post_operations.py:375), [PostExtractor.extract_post_text()](flow/post_operations.py:463), [PostExtractor.extract_post_images()](flow/post_operations.py:508), [PostExtractor.extract_post_videos()](flow/post_operations.py:528), [PostExtractor.extract_post_reactions()](flow/post_operations.py:548), [PostExtractor.extract_post_comments()](flow/post_operations.py:574), [PostExtractor.extract_post_shares()](flow/post_operations.py:600), [PostExtractor.\_wait_for_post_completion()](flow/post_operations.py:749).

#### [PostExtractor.extract_author_data()](flow/post_operations.py:375)

Tipo: Helper autor
Descripción: Multi-estrategia (aria-label, svg, title, fallback selectores, normalización de link).

#### [PostExtractor.extract_post_text()](flow/post_operations.py:463)

Tipo: Helper texto

#### [PostExtractor.extract_post_link()](flow/post_operations.py:473)

Tipo: Helper link
Descripción: Regex sobre inner_html para patrones /posts/, story.php, permalink, fbid, /photo/, /video/.

#### [PostExtractor.extract_post_images()](flow/post_operations.py:508)

Tipo: Helper media (img)

#### [PostExtractor.extract_post_videos()](flow/post_operations.py:528)

Tipo: Helper media (video)

#### [PostExtractor.extract_post_reactions()](flow/post_operations.py:548)

Tipo: Helper reacciones
Descripción: Cuenta primer número en aria-label variantes (ES/EN).

#### [PostExtractor.extract_post_comments()](flow/post_operations.py:574)

Tipo: Helper comentarios

#### [PostExtractor.extract_post_shares()](flow/post_operations.py:600)

Tipo: Helper compartidos

#### [PostExtractor.\_is_nested_story()](flow/post_operations.py:626)

Tipo: Helper estructural
Descripción: Determina si el elemento es hijo (anidado) para filtrar doble conteo.

#### [PostExtractor.\_is_fake_author()](flow/post_operations.py:645)

Tipo: Helper validación
Descripción: Detecta autores “falsos” (botones traducidos, CTA). Usa cache cargada por [PostExtractor.\_load_fake_authors_list()](flow/post_operations.py:668).

#### [PostExtractor.\_load_fake_authors_list()](flow/post_operations.py:668)

Tipo: Helper configuración
Descripción: Carga JSON o fallback hardcodeado.

#### [PostExtractor.\_extract_author_quick()](flow/post_operations.py:695)

Tipo: Helper fast-path
Descripción: Extrae autor mínimo para verificación temprana duplicados.

#### [PostExtractor.\_extract_text_quick()](flow/post_operations.py:714)

Tipo: Helper fast-path
Descripción: Extrae primeros 100 chars.

#### [PostExtractor.\_is_duplicate_quick()](flow/post_operations.py:724)

Tipo: Helper duplicado rápido
Descripción: Compara (autor, text[:100]) exactos.

#### [PostExtractor.\_is_duplicate_final()](flow/post_operations.py:739)

Tipo: Helper duplicado profundo
Descripción: Segunda capa tras extracción completa.

#### [PostExtractor.\_wait_for_post_completion()](flow/post_operations.py:749)

Tipo: Helper sincronización
Descripción: Polling hasta que post tenga autor + texto mínimo.

#### [PostExtractor.\_generate_post_id()](flow/post_operations.py:776)

Tipo: Helper ID
Descripción: Hash basado en autor + primeras 50 chars + timestamp.

#### [PostExtractor.verify_post_structure()](flow/post_operations.py:790)

Tipo: Helper validación
Descripción: Verifica campos mínimos (author/text).

#### [PostExtractor.check_post_quality()](flow/post_operations.py:808)

Tipo: Helper scoring
Descripción: Calcula score y etiqueta calidad (Alta/Media/Baja) según presencia de campos.

#### [PostExtractor.validate_extraction_batch()](flow/post_operations.py:849)

Tipo: Helper evaluación lote
Descripción: Aplica verificación y scoring a lote; calcula success_rate y calidad promedio.

### Clase: [PostOperations](flow/post_operations.py:881)

Subclase para compatibilidad histórica.

#### [PostOperations.**init**()](flow/post_operations.py:887)

Tipo: Compatibilidad
Descripción: Inicializa y prepara buffer extracted_posts.

#### [PostOperations.extract_posts_from_group()](flow/post_operations.py:892)

Tipo: Compatibilidad (stub)
Descripción: Placeholder (TODO). Retorna lista vacía.

#### [PostOperations.get_extraction_stats()](flow/post_operations.py:913)

Tipo: Compatibilidad
Descripción: Devuelve estadísticas superficiales (conteo y timestamp).

#### [PostOperations.process_posts_with_duplicates()](flow/post_operations.py:925)

Tipo: Helper compatibilidad
Descripción: Elimina duplicados exactos (autor + primeras 100 chars texto).

====================================================================

## 4. Archivo: flow/post_comparison.py

Comparación formal y detección de límite vía Jaccard.

### Clase: [PostComparison](flow/post_comparison.py:12)

#### [PostComparison.**init**()](flow/post_comparison.py:17)

Tipo: Helper init
Descripción: Define threshold por defecto (Jaccard).

#### [PostComparison.calculate_jaccard()](flow/post_comparison.py:26)

Tipo: Helper similitud
Descripción: Limpia textos, tokeniza y calcula coeficiente.

#### [PostComparison.we_found_the_limit_post()](flow/post_comparison.py:72)

Tipo: Helper límite
Descripción: Compara post actual contra el más reciente almacenado; requiere similitud de contenido y (si disponible) autor.

#### [PostComparison.check_if_there_are_repeated_post_in_this_group_round()](flow/post_comparison.py:114)

Tipo: Helper deduplicación ronda
Descripción: Filtra posts similares dentro de la misma extracción (Jaccard).

#### [PostComparison.load_test_posts_from_observations()](flow/post_comparison.py:185)

Tipo: Helper pruebas
Descripción: Carga posts de archivos observación (para test offline).

### Funciones de compatibilidad (wrappers):

- [calculate_jaccard()](flow/post_comparison.py:226)
- [we_found_the_limit_post()](flow/post_comparison.py:232)
- [check_if_there_are_repeated_post_in_this_group_round()](flow/post_comparison.py:240)
- [load_test_posts_from_observations()](flow/post_comparison.py:247)

====================================================================

## 5. Archivo: flow/utils/post_utils.py

Utilidades alternativas (posible legado o uso en otros contextos paralelos).

### [calculate_jaccard()](flow/utils/post_utils.py:10)

Tipo: Helper similitud
(Estructuralmente similar a versión formal en PostComparison)

### [we_found_the_limit_post()](flow/utils/post_utils.py:61)

Tipo: Helper límite (alternativa)
Descripción: Igual propósito: detener extracción al reencontrar post ya almacenado.

### [validate_post_content()](flow/utils/post_utils.py:101)

Tipo: Helper validación básica
Descripción: Verifica mínimo de palabras.

### [check_if_there_are_repeated_posts_in_group_round()](flow/utils/post_utils.py:120)

Tipo: Helper deduplicación alternativa
Descripción: Similar a versión formal con estadísticas empaquetadas.

### [normalize_post_data()](flow/utils/post_utils.py:191)

Tipo: Helper normalización
Descripción: Unifica campos (content/text) y limpia autor, agrega defaults.

====================================================================

## 6. MATRIZ DE RELACIONES (Resumen Jerárquico)

1. Orquestación Global:

   - [main()](flow/flow.py:319) → [FacebookFlowOrchestrator.execute_login_flow()](flow/flow.py:43) → (tras login) [SimpleObserver.observe_group_with_page()](flow/simple_observer.py:31)

2. Observación de Grupo:

   - [SimpleObserver.observe_group_with_page()](flow/simple_observer.py:31)
     - [SimpleObserver.\_verify_group_page_loaded()](flow/simple_observer.py:118)
     - [SimpleObserver.\_extract_group_information()](flow/simple_observer.py:146)
       - [PostExtractor.extract_group_info_from_page()](flow/post_operations.py:30)
       - [PostExtractor.get_group_id_for_database()](flow/post_operations.py:56)
     - [SimpleObserver.\_get_stored_posts()](flow/simple_observer.py:185)
     - [SimpleObserver.\_execute_intelligent_scroll_extraction()](flow/simple_observer.py:225)
       - [SimpleObserver.\_load_scroll_configuration()](flow/simple_observer.py:447)
       - Loop Scroll:
         - [SimpleObserver.\_detect_and_close_overlays()](flow/simple_observer.py:470)
         - [SimpleObserver.\_detect_glimmer_elements()](flow/simple_observer.py:561)
         - [SimpleObserver.\_wait_for_glimmers_to_disappear()](flow/simple_observer.py:585) (condicional)
         - [PostExtractor.extract_posts_one_by_one_with_expansion()](flow/post_operations.py:96)
           - [PostExtractor.\_is_nested_story()](flow/post_operations.py:626)
           - [PostExtractor.\_extract_author_quick()](flow/post_operations.py:695)
           - [PostExtractor.\_extract_text_quick()](flow/post_operations.py:714)
           - [PostExtractor.\_is_duplicate_quick()](flow/post_operations.py:724)
           - [PostExtractor.extract_parent_post_with_nested_content()](flow/post_operations.py:250)
             - [PostExtractor.extract_complete_post_data()](flow/post_operations.py:302)
               - [PostExtractor.\_wait_for_post_completion()](flow/post_operations.py:749)
               - [PostExtractor.extract_post_link()](flow/post_operations.py:473)
               - [PostExtractor.extract_author_data()](flow/post_operations.py:375)
               - [PostExtractor.extract_post_text()](flow/post_operations.py:463)
               - [PostExtractor.extract_post_images()](flow/post_operations.py:508)
               - [PostExtractor.extract_post_videos()](flow/post_operations.py:528)
               - [PostExtractor.extract_post_reactions()](flow/post_operations.py:548)
               - [PostExtractor.extract_post_comments()](flow/post_operations.py:574)
               - [PostExtractor.extract_post_shares()](flow/post_operations.py:600)
               - [PostExtractor.\_generate_post_id()](flow/post_operations.py:776)
             - [PostExtractor.\_is_fake_author()](flow/post_operations.py:645)
               - [PostExtractor.\_load_fake_authors_list()](flow/post_operations.py:668)
           - [PostExtractor.\_is_duplicate_final()](flow/post_operations.py:739)
           - [we_found_the_limit_post()](flow/post_comparison.py:232) (detención límite)
       - Métricas tiempo y corte bucles
     - [SimpleObserver.\_save_posts_to_database()](flow/simple_observer.py:399)

3. Detección de Límite y Duplicados:
   - Primario: [PostComparison.we_found_the_limit_post()](flow/post_comparison.py:72)
   - Alternativo utilitario: [we_found_the_limit_post()](flow/utils/post_utils.py:61)
   - Deduplicación intra-lote:
     - Formal: [PostComparison.check_if_there_are_repeated_post_in_this_group_round()](flow/post_comparison.py:114)
     - Alternativa: [check_if_there_are_repeated_posts_in_group_round()](flow/utils/post_utils.py:120)
   - Micro nivel (exact match heurístico): [PostExtractor.\_is_duplicate_quick()](flow/post_operations.py:724), [PostExtractor.\_is_duplicate_final()](flow/post_operations.py:739)

====================================================================

## 7. CLASIFICACIÓN POR TIPO

Orquestadores Principales:

- [main()](flow/flow.py:319)
- [FacebookFlowOrchestrator.execute_login_flow()](flow/flow.py:43)
- [FacebookFlowOrchestrator.execute_humanization()](flow/flow.py:63)
- [FacebookFlowOrchestrator.execute_groups_tracking()](flow/flow.py:100)
- [FacebookFlowOrchestrator.execute_queue_processing()](flow/flow.py:236)
- [SimpleObserver.observe_group_with_page()](flow/simple_observer.py:31)
- [SimpleObserver.\_execute_intelligent_scroll_extraction()](flow/simple_observer.py:225)
- [PostExtractor.extract_posts_one_by_one_with_expansion()](flow/post_operations.py:96)

Orquestadores de Soporte / Debug:

- [FacebookFlowOrchestrator.early_exit_for_debug_purposes()](flow/flow.py:274)

Compatibilidad / Wrappers:

- [observe_group_with_page()](flow/simple_observer.py:630)
- [PostOperations.extract_posts_from_group()](flow/post_operations.py:892)
- [calculate_jaccard()](flow/post_comparison.py:226)
- [we_found_the_limit_post()](flow/post_comparison.py:232)
- [check_if_there_are_repeated_post_in_this_group_round()](flow/post_comparison.py:240)
- [load_test_posts_from_observations()](flow/post_comparison.py:247)

Helpers Críticos Extracción (DOM / Parsing):

- [PostExtractor.extract_complete_post_data()](flow/post_operations.py:302)
- [PostExtractor.extract_parent_post_with_nested_content()](flow/post_operations.py:250)
- [PostExtractor.extract_author_data()](flow/post_operations.py:375)
- [PostExtractor.extract_post_text()](flow/post_operations.py:463)
- [PostExtractor.extract_post_link()](flow/post_operations.py:473)
- [PostExtractor.extract_post_images()](flow/post_operations.py:508)
- [PostExtractor.extract_post_videos()](flow/post_operations.py:528)
- [PostExtractor.extract_post_reactions()](flow/post_operations.py:548)
- [PostExtractor.extract_post_comments()](flow/post_operations.py:574)
- [PostExtractor.extract_post_shares()](flow/post_operations.py:600)

Detección / Calidad / Duplicados:

- [PostExtractor.\_is_nested_story()](flow/post_operations.py:626)
- [PostExtractor.\_is_fake_author()](flow/post_operations.py:645)
- [PostExtractor.\_extract_author_quick()](flow/post_operations.py:695)
- [PostExtractor.\_extract_text_quick()](flow/post_operations.py:714)
- [PostExtractor.\_is_duplicate_quick()](flow/post_operations.py:724)
- [PostExtractor.\_is_duplicate_final()](flow/post_operations.py:739)
- [PostExtractor.verify_post_structure()](flow/post_operations.py:790)
- [PostExtractor.check_post_quality()](flow/post_operations.py:808)
- [PostExtractor.validate_extraction_batch()](flow/post_operations.py:849)
- [PostComparison.we_found_the_limit_post()](flow/post_comparison.py:72)
- [PostComparison.check_if_there_are_repeated_post_in_this_group_round()](flow/post_comparison.py:114)
- [calculate_jaccard()](flow/post_comparison.py:226)
- [we_found_the_limit_post()](flow/utils/post_utils.py:61)
- [check_if_there_are_repeated_posts_in_group_round()](flow/utils/post_utils.py:120)
- [validate_post_content()](flow/utils/post_utils.py:101)

Sincronización / Carga / UI:

- [PostExtractor.\_wait_for_post_completion()](flow/post_operations.py:749)
- [SimpleObserver.\_detect_and_close_overlays()](flow/simple_observer.py:470)
- [SimpleObserver.\_is_element_truly_visible()](flow/simple_observer.py:528)
- [SimpleObserver.\_detect_glimmer_elements()](flow/simple_observer.py:561)
- [SimpleObserver.\_wait_for_glimmers_to_disappear()](flow/simple_observer.py:585)

Configuración / Carga de Listas:

- [SimpleObserver.\_load_scroll_configuration()](flow/simple_observer.py:447)
- [PostExtractor.\_load_fake_authors_list()](flow/post_operations.py:668)

Persistencia / BD:

- [SimpleObserver.\_get_stored_posts()](flow/simple_observer.py:185)
- [SimpleObserver.\_save_posts_to_database()](flow/simple_observer.py:399)
- [PostExtractor.get_group_id_for_database()](flow/post_operations.py:56)

Normalización / Transformación:

- [normalize_post_data()](flow/utils/post_utils.py:191)

====================================================================

## 8. NOTAS DE IMPLEMENTACIÓN Y OBSERVACIONES

- Existen DOS implementaciones de funciones clave (calculate_jaccard y we_found_the_limit_post) en módulos distintos para compatibilidad / evolución. Se recomienda consolidación futura para evitar divergencias lógicas.
- La detección de límite actúa antes de agregar el post a acumuladores en [PostExtractor.extract_posts_one_by_one_with_expansion()](flow/post_operations.py:96), minimizando ruido.
- La arquitectura separa claramente:
  - Bucle de scroll (macro) → [SimpleObserver.\_execute_intelligent_scroll_extraction()](flow/simple_observer.py:225)
  - Micro extracción (unidad post) → [PostExtractor.extract_posts_one_by_one_with_expansion()](flow/post_operations.py:96)
  - Estructuración de datos (DOM → dict) → [PostExtractor.extract_complete_post_data()](flow/post_operations.py:302)
  - Detección de límite / duplicados semánticos → [PostComparison](flow/post_comparison.py:12) / utilidades
  - Persistencia → [SimpleObserver.\_save_posts_to_database()](flow/simple_observer.py:399)

====================================================================

## 9. RECOMENDACIONES FUTURAS (NO EJECUTADAS)

1. Unificar lógica Jaccard en un solo namespace (evitar duplicidad entre post_comparison y utils.post_utils).
2. Añadir capa estratégica de retry cuando [PostExtractor.\_wait_for_post_completion()](flow/post_operations.py:749) falle por timeout.
3. Reporte estructurado de motivos de descarte (autor falso / duplicado rápido / duplicado final / límite).

====================================================================
Fin del catálogo técnico integral.
