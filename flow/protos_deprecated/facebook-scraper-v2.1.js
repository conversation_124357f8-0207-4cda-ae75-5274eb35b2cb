// Testing purposes currently being evalueted
// Facebook Post Scraper v2.0 - Improved Version (IIFE)
// Mantiene todas las funcionalidades criticas del original sin emojis
(function() {
    'use strict';

    // Mini-framework de selectores personalizados
    class CustomSelectors {
        // Buscar elementos que contengan texto específico
        static findByText(container, selector, text) {
            const elements = container.querySelectorAll(selector);
            const results = [];

            for (const el of elements) {
                const content = el.textContent || el.getAttribute('aria-label') || '';
                if (content.toLowerCase().includes(text.toLowerCase())) {
                    results.push(el);
                }
            }

            return results;
        }

        // Buscar primer elemento que contenga texto específico
        static findFirstByText(container, selector, text) {
            const results = this.findByText(container, selector, text);
            return results.length > 0 ? results[0] : null;
        }

        // Simular :contains() - buscar por texto contenido
        static contains(container, selector, text) {
            return this.findByText(container, selector, text);
        }

        // Simular :has-text() - buscar por texto contenido (alias)
        static hasText(container, selector, text) {
            return this.findByText(container, selector, text);
        }

        // Buscar con múltiples textos posibles
        static findByAnyText(container, selector, texts) {
            const elements = container.querySelectorAll(selector);
            const results = [];

            for (const el of elements) {
                const content = (el.textContent || el.getAttribute('aria-label') || '').toLowerCase();
                for (const text of texts) {
                    if (content.includes(text.toLowerCase())) {
                        results.push(el);
                        break;
                    }
                }
            }

            return results;
        }

        // Buscar primer elemento con cualquiera de los textos
        static findFirstByAnyText(container, selector, texts) {
            const results = this.findByAnyText(container, selector, texts);
            return results.length > 0 ? results[0] : null;
        }
    }

    // Utilidades para esperar elementos y estabilidad
    class WaitUtils {
        static async waitStable(element, quietMs = 180, maxMs = 1000) {
            return new Promise(resolve => {
                let lastMutation = performance.now();
                const obs = new MutationObserver(() => {
                    lastMutation = performance.now();
                });
                obs.observe(element, { childList: true, subtree: true, characterData: true });
                const start = performance.now();
                const tick = () => {
                    const now = performance.now();
                    const quietTime = now - lastMutation;
                    const totalTime = now - start;
                    
                    if (quietTime >= quietMs || totalTime >= maxMs) {
                        obs.disconnect();
                        resolve({
                            stable: quietTime >= quietMs,
                            waitedMs: Math.round(totalTime),
                            quietMs: Math.round(quietTime)
                        });
                    } else {
                        setTimeout(tick, 50);
                    }
                };
                tick();
            });
        }

        static async waitForImages(container, maxWaitMs = 5000) {
            const start = performance.now();
            let iterations = 0;

            const getImageInfo = (container) => {
                const imgs = container.querySelectorAll('img');
                let unloaded = 0;
                imgs.forEach(img => {
                    if (!img.complete || img.naturalWidth === 0) unloaded++;
                });
                return { total: imgs.length, unloaded };
            };

            const imgInfoStart = getImageInfo(container);

            while (performance.now() - start < maxWaitMs) {
                const imgInfo = getImageInfo(container);
                if (imgInfo.unloaded === 0) break;

                await new Promise(resolve => setTimeout(resolve, 200));
                iterations++;
            }

            const imgInfoEnd = getImageInfo(container);
            const waited = Math.round(performance.now() - start);

            return {
                waited_ms: waited,
                iterations,
                images_initial_unloaded: imgInfoStart.unloaded,
                images_final_unloaded: imgInfoEnd.unloaded,
                images_total: imgInfoEnd.total,
                incomplete: imgInfoEnd.unloaded > 0
            };
        }

        // Esperar a que el post tenga información completa
        static async waitForPostCompletion(postContainer, maxWaitMs = 3000) {
            const start = performance.now();
            let lastElementCount = 0;
            let stableCount = 0;

            while (performance.now() - start < maxWaitMs) {
                // Contar elementos importantes del post
                const currentElements = postContainer.querySelectorAll(
                    'img, video, a, span, div[role="button"], [aria-label]'
                ).length;

                if (currentElements === lastElementCount) {
                    stableCount++;
                    if (stableCount >= 3) break; // 3 iteraciones estables
                } else {
                    stableCount = 0;
                    lastElementCount = currentElements;
                }

                await new Promise(resolve => setTimeout(resolve, 100));
            }

            return {
                waited_ms: Math.round(performance.now() - start),
                final_element_count: lastElementCount,
                stable: stableCount >= 3
            };
        }

        // Observar un post específico hasta que esté completo
        static async observePostUntilComplete(postContainer, maxWaitMs = 5000) {
            return new Promise((resolve) => {
                const start = performance.now();
                const requiredElements = {
                    author: false,
                    content: false,
                    interactions: false,
                    media: false
                };

                let checkInterval;
                let mutationObserver;

                const checkCompleteness = () => {
                    // Verificar autor usando lógica mejorada (atributos inmutables)
                    let authorFound = false;

                    // Buscar por aria-label en enlaces
                    const linkWithAriaLabel = postContainer.querySelector('a[aria-label]');
                    if (linkWithAriaLabel) {
                        const ariaLabel = linkWithAriaLabel.getAttribute('aria-label');
                        if (ariaLabel && ariaLabel.length > 2 && !ariaLabel.includes('http')) {
                            authorFound = true;
                        }
                    }

                    // Buscar por aria-label en SVG
                    if (!authorFound) {
                        const svgWithAriaLabel = postContainer.querySelector('svg[aria-label]');
                        if (svgWithAriaLabel) {
                            const ariaLabel = svgWithAriaLabel.getAttribute('aria-label');
                            if (ariaLabel && ariaLabel.length > 2 && !ariaLabel.includes('http')) {
                                authorFound = true;
                            }
                        }
                    }

                    // Buscar en atributo title
                    if (!authorFound) {
                        const elementWithTitle = postContainer.querySelector('[title]');
                        if (elementWithTitle) {
                            const title = elementWithTitle.getAttribute('title');
                            if (title && title.length > 2 && !title.includes('http')) {
                                authorFound = true;
                            }
                        }
                    }

                    // Fallback a selectores tradicionales
                    if (!authorFound) {
                        const authorSelectors = [
                            'b > span', 'strong > span', 'h3 a', '[data-ad-rendering-role="profile_name"] a',
                            'b span', 'strong span', 'h3 span', '[role="link"]', 'a[role="link"]'
                        ];

                        for (const selector of authorSelectors) {
                            const authorEl = postContainer.querySelector(selector);
                            if (authorEl && authorEl.textContent.trim()) {
                                authorFound = true;
                                break;
                            }
                        }
                    }

                    requiredElements.author = authorFound;

                    // Verificar contenido del post (más flexible)
                    const contentEl = postContainer.querySelector('[data-ad-rendering-role="story_message"]');
                    requiredElements.content = !!contentEl;

                    // Verificar elementos de interacción (más flexible)
                    const interactionEls = postContainer.querySelectorAll(
                        'div[role="button"], [aria-label], span[dir="auto"], div[tabindex]'
                    );
                    requiredElements.interactions = interactionEls.length > 2; // Al menos algunos elementos

                    // Verificar media (más permisivo)
                    const images = postContainer.querySelectorAll('img');
                    let allImagesLoaded = true;
                    if (images.length > 0) {
                        images.forEach(img => {
                            if (!img.complete || img.naturalWidth === 0) {
                                allImagesLoaded = false;
                            }
                        });
                    }
                    requiredElements.media = allImagesLoaded;

                    // Criterio más flexible: al menos autor Y contenido
                    const isComplete = requiredElements.author && requiredElements.content;

                    const elapsed = performance.now() - start;

                    // Debug detallado cada segundo
                    if (Math.floor(elapsed / 1000) !== Math.floor((elapsed - 200) / 1000)) {
                        console.log(`[DEBUG] Post ${elapsed.toFixed(0)}ms - Autor: ${requiredElements.author}, Contenido: ${requiredElements.content}, Interacciones: ${requiredElements.interactions}, Media: ${requiredElements.media}`);
                        if (authorFound) {
                            // Buscar el elemento autor para debug
                            const debugAuthorEl = postContainer.querySelector('a[aria-label], svg[aria-label], [title]');
                            if (debugAuthorEl) {
                                const debugText = debugAuthorEl.getAttribute('aria-label') || debugAuthorEl.getAttribute('title') || debugAuthorEl.textContent.trim();
                                console.log(`[DEBUG] Autor encontrado: "${debugText}"`);
                            }
                        } else {
                            console.log(`[DEBUG] Elementos b: ${postContainer.querySelectorAll('b').length}, span: ${postContainer.querySelectorAll('span').length}`);
                        }
                    }

                    if (isComplete || elapsed > maxWaitMs) {
                        clearInterval(checkInterval);
                        if (mutationObserver) mutationObserver.disconnect();

                        // Obtener texto del autor para el resultado
                        let authorText = 'No encontrado';
                        if (authorFound) {
                            const resultAuthorEl = postContainer.querySelector('a[aria-label], svg[aria-label], [title]');
                            if (resultAuthorEl) {
                                authorText = resultAuthorEl.getAttribute('aria-label') || resultAuthorEl.getAttribute('title') || resultAuthorEl.textContent.trim();
                            }
                        }

                        resolve({
                            complete: isComplete,
                            waited_ms: Math.round(elapsed),
                            elements_found: requiredElements,
                            timeout: elapsed > maxWaitMs,
                            author_text: authorText
                        });
                    }
                };

                // Observer para detectar cambios en el post
                mutationObserver = new MutationObserver(() => {
                    checkCompleteness();
                });

                mutationObserver.observe(postContainer, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });

                // Verificación periódica adicional
                checkInterval = setInterval(checkCompleteness, 200);

                // Verificación inicial
                checkCompleteness();
            });
        }
    }

    // Clase principal del scraper con todas las funcionalidades criticas
    class FacebookPostCapture {
        constructor(options = {}) {
            this.debug = options.debug || false;
            this.lastPostTextPattern = null;
            this._lastPostPatternNormalized = null;
            this._lastPostPatternTokens = [];
            this._heuristicRegex = null;
            this.lastPostFound = false;
            this.reachedLastPost = false;
            this.preventFurtherCaptures = false;
            this.readyForExport = false;
            
            this.capturedPosts = new Set(); // Para evitar duplicados
            this.allPosts = []; // Array para almacenar todos los posts
            this.isScrollBlocked = false;
            this.observer = null;

            // Buffer de posts pendientes de verificacion
            this.pendingPosts = [];
            this.isProcessingPending = false;



            // Monitoreo de ventana y contenido
            this.windowMetrics = {
                lastHeight: window.innerHeight,
                lastScrollHeight: document.documentElement.scrollHeight,
                lastPostCount: 0,
                measurements: []
            };

            // Sistema de logging centralizado
            this.logs = {
                window: [],
                posts: [],
                navigation: [],
                patterns: [],
                errors: [],
                general: []
            };

            // Sistema de análisis temporal con Jaccard
            this.temporalAnalysis = {
                capturedPosts: [], // Array ordenado por tiempo de captura
                jaccardThreshold: 0.8, // Umbral de similitud para considerar duplicado
                completionDetected: false,
                lastMatchIndex: -1, // Índice del último post que coincidió con histórico
                historicalPosts: this.loadHistoricalPosts(), // Posts de sesiones anteriores
                targetText: null, // Texto específico a buscar (ej: último post de BD)
                targetAuthor: null, // Autor específico a buscar
                targetMatchFound: false // Si se encontró el texto objetivo
            };

            this.init();
        }

        init() {
            this.setupPostObserver();
            this.setupScrollBlocker();
            this.setupInteractionBlockers();
            this.initWindowMonitoring();
            this.log('general', 'Facebook Post Capture iniciado');
        }

        // Sistema de logging centralizado
        log(category, message, data = null) {
            const timestamp = new Date().toISOString();
            const logEntry = {
                timestamp,
                message,
                data
            };

            // Agregar a la categoría correspondiente
            if (this.logs[category]) {
                this.logs[category].push(logEntry);

                // Mantener solo los últimos 100 logs por categoría
                if (this.logs[category].length > 100) {
                    this.logs[category].shift();
                }
            }

            // También mostrar en consola
            console.log(`[${category.toUpperCase()}] ${message}`, data || '');
        }

        // Exportar todos los logs
        exportLogs() {
            const exportData = {
                timestamp: new Date().toISOString(),
                totalLogs: Object.values(this.logs).reduce((sum, arr) => sum + arr.length, 0),
                categories: {},
                summary: {}
            };

            // Copiar logs por categoría
            for (const [category, logs] of Object.entries(this.logs)) {
                exportData.categories[category] = [...logs];
                exportData.summary[category] = logs.length;
            }

            // Estadísticas adicionales
            exportData.windowMetrics = {
                currentHeight: window.innerHeight,
                currentScrollHeight: document.documentElement.scrollHeight,
                measurements: [...this.windowMetrics.measurements]
            };

            exportData.captureStats = {
                totalCaptured: this.capturedPosts.size,
                pendingPosts: this.pendingPosts.length,
                isScrollBlocked: this.isScrollBlocked
            };

            return exportData;
        }

        // Mostrar logs en consola de manera organizada
        showLogs(category = null) {
            if (category && this.logs[category]) {
                console.group(`=== LOGS: ${category.toUpperCase()} ===`);
                this.logs[category].forEach((log, index) => {
                    console.log(`${index + 1}. [${log.timestamp}] ${log.message}`, log.data || '');
                });
                console.groupEnd();
            } else {
                console.group('=== TODOS LOS LOGS ===');
                for (const [cat, logs] of Object.entries(this.logs)) {
                    if (logs.length > 0) {
                        console.group(`${cat.toUpperCase()} (${logs.length})`);
                        logs.slice(-5).forEach(log => {
                            console.log(`[${log.timestamp}] ${log.message}`, log.data || '');
                        });
                        console.groupEnd();
                    }
                }
                console.groupEnd();
            }
        }

        // Configurar observer para detectar posts
        setupPostObserver() {
            this.observer = new MutationObserver((mutations) => {
                if (this.preventFurtherCaptures || this.reachedLastPost) {
                    return;
                }

                mutations.forEach((mutation) => {
                    if (this.preventFurtherCaptures || this.reachedLastPost) {
                        return;
                    }

                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.scanForPosts(node);
                        }
                    });
                });
            });

            this.observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Escanear posts existentes
            this.scanForPosts(document.body);
        }

        // Escanear posts en un contenedor
        scanForPosts(container) {
            const storyPosts = container.querySelectorAll('[data-ad-rendering-role="story_message"]');

            for (const storyElement of storyPosts) {
                if (this.preventFurtherCaptures || this.reachedLastPost) {
                    break;
                }

                const postContainer = this.findPostContainer(storyElement);
                if (postContainer && !this.isPostCaptured(postContainer)) {
                    this.addToPendingBuffer(postContainer, storyElement);
                }
            }

            // Procesar buffer
            this.processPendingPosts();
        }

        // Encontrar contenedor del post
        findPostContainer(storyElement) {
            console.log(`[DEBUG] Buscando contenedor para story element:`, storyElement);

            // Estrategia 1: Buscar contenedor específico de Facebook
            let container = storyElement.closest('[data-pagelet*="FeedUnit"], [data-testid*="story"], div[role="article"]');
            if (container) {
                console.log(`[DEBUG] Contenedor encontrado por estrategia 1: ${container.tagName} con ${container.children.length} hijos`);
                return container;
            }

            // Estrategia 2: Buscar hacia arriba hasta encontrar contenedor completo
            container = storyElement;
            for (let i = 0; i < 15; i++) {
                container = container.parentElement;
                if (!container) break;

                // Verificar si este contenedor tiene elementos completos de un post
                const hasAuthor = container.querySelector('b > span, strong > span, h3 a, [role="link"]');
                const hasContent = container.querySelector('[data-ad-rendering-role="story_message"]');
                const hasInteractions = container.querySelectorAll('[role="button"]').length >= 2;

                if (i < 5) { // Solo log para los primeros niveles
                    console.log(`[DEBUG] Nivel ${i}: autor=${!!hasAuthor}, contenido=${!!hasContent}, interacciones=${hasInteractions}, elementos=${container.children.length}`);
                }

                if (hasAuthor && hasContent && hasInteractions) {
                    console.log(`[DEBUG] Contenedor completo encontrado en nivel ${i}: ${container.tagName}`);
                    return container;
                }
            }

            // Estrategia 3: Buscar contenedor por tamaño y contenido
            container = storyElement;
            for (let i = 0; i < 10; i++) {
                container = container.parentElement;
                if (!container) break;

                const rect = container.getBoundingClientRect();
                const hasMinSize = rect.height > 100 && rect.width > 300;
                const hasMultipleElements = container.querySelectorAll('*').length > 20;

                if (hasMinSize && hasMultipleElements) {
                    console.log(`[DEBUG] Contenedor por tamaño encontrado en nivel ${i}: ${container.tagName} (${rect.width}x${rect.height})`);
                    return container;
                }
            }

            // Fallback: contenedor más cercano
            console.log(`[DEBUG] Usando fallback: parentElement`);
            return storyElement.closest('div') || storyElement.parentElement;
        }

        // Verificar si un post ya fue capturado
        isPostCaptured(postContainer) {
            const postId = this.generatePostId(postContainer);
            return this.capturedPosts.has(postId);
        }

        // Generar ID unico para un post usando atributos inmutables
        generatePostId(postContainer) {
            // Extraer autor usando lógica mejorada
            let authorText = 'unknown_author';

            // Buscar por aria-label en enlaces
            const linkWithAriaLabel = postContainer.querySelector('a[aria-label]');
            if (linkWithAriaLabel) {
                const ariaLabel = linkWithAriaLabel.getAttribute('aria-label');
                if (ariaLabel && ariaLabel.length > 2 && !ariaLabel.includes('http')) {
                    authorText = ariaLabel;
                }
            }

            // Buscar por aria-label en SVG si no se encontró
            if (authorText === 'unknown_author') {
                const svgWithAriaLabel = postContainer.querySelector('svg[aria-label]');
                if (svgWithAriaLabel) {
                    const ariaLabel = svgWithAriaLabel.getAttribute('aria-label');
                    if (ariaLabel && ariaLabel.length > 2 && !ariaLabel.includes('http')) {
                        authorText = ariaLabel;
                    }
                }
            }

            // Buscar en atributo title si no se encontró
            if (authorText === 'unknown_author') {
                const elementWithTitle = postContainer.querySelector('[title]');
                if (elementWithTitle) {
                    const title = elementWithTitle.getAttribute('title');
                    if (title && title.length > 2 && !title.includes('http')) {
                        authorText = title;
                    }
                }
            }

            // Fallback a selectores tradicionales
            if (authorText === 'unknown_author') {
                const authorEl = postContainer.querySelector(
                    'b > span, h3 a, [data-ad-rendering-role="profile_name"] a, h3 span, [role="link"], strong > span'
                );
                if (authorEl) {
                    authorText = authorEl.textContent.trim();
                }
            }

            // Obtener link usando HTML string para evitar activar navegación
            let postLink = 'no_link';
            try {
                const htmlString = postContainer.innerHTML;
                const linkPatterns = [
                    /href="([^"]*\/posts\/[^"]*)"/,
                    /href="([^"]*story\.php[^"]*)"/,
                    /href="([^"]*permalink[^"]*)"/,
                    /href="([^"]*fbid[^"]*)"/
                ];

                for (const pattern of linkPatterns) {
                    const match = htmlString.match(pattern);
                    if (match) {
                        postLink = match[1];
                        break;
                    }
                }
                console.log(`[DEBUG] Link extraído por HTML string: ${postLink.substring(0, 50)}...`);
            } catch (error) {
                console.log(`[DEBUG] Error extrayendo link por HTML string:`, error);
            }

            const textEl = postContainer.querySelector('[data-ad-rendering-role="story_message"]');

            // Usar solo contenido estable del post (sin timestamp)
            const content = [
                authorText,
                postLink,
                textEl ? textEl.textContent.trim().substring(0, 200) : 'no_text'
            ].join('|');

            return this.hashString(content);
        }

        // Hash simple para generar ID
        hashString(str) {
            let hash = 0;
            if (str.length === 0) return hash.toString();
            
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32bit integer
            }
            
            return Math.abs(hash).toString(36);
        }

        // Agregar post al buffer pendiente
        addToPendingBuffer(postContainer, storyElement) {
            const postId = this.generatePostId(postContainer);

            if (this.capturedPosts.has(postId)) {
                return; // Ya capturado
            }

            this.pendingPosts.push({
                id: postId,
                container: postContainer,
                element: storyElement,
                timestamp: Date.now()
            });
        }

        // Procesar posts pendientes con verificacion de limites
        async processPendingPosts() {
            if (this.isProcessingPending || this.pendingPosts.length === 0) {
                return;
            }

            this.isProcessingPending = true;

            try {
                const postsToProcess = [...this.pendingPosts];
                this.pendingPosts = [];

                for (const pendingPost of postsToProcess) {
                    if (this.preventFurtherCaptures || this.reachedLastPost) {
                        break;
                    }

                    // Pequeña pausa entre capturas (reducida)
                    await new Promise(resolve => setTimeout(resolve, 100));

                    // Verificacion del patron limite ANTES de capturar
                    if (this.lastPostTextPattern && !this.lastPostFound) {
                        const currentText = pendingPost.element.textContent || '';
                        const matchResult = this._matchesLastPostPattern(currentText);
                        if (matchResult.matched) {
                            console.log("LIMITE DETECTADO - Deteniendo captura");
                            this._handleLastPostDetected(currentText);
                            break;
                        }
                    }

                    // Capturar el post
                    await this.capturePost(pendingPost.container, pendingPost.element);
                }
            } finally {
                this.isProcessingPending = false;
            }
        }

        // Configurar patron de ultimo post
        setLastPostTextPattern(pattern) {
            if (!pattern) {
                this.lastPostTextPattern = null;
                this._lastPostPatternNormalized = null;
                this._lastPostPatternTokens = [];
                this._heuristicRegex = null;
                this.lastPostFound = false;
                this.reachedLastPost = false;
                console.log("lastPostTextPattern limpiado");
                return;
            }

            if (pattern instanceof RegExp) {
                this.lastPostTextPattern = pattern;
                this._lastPostPatternNormalized = null;
                this._lastPostPatternTokens = [];
                this._heuristicRegex = null;
            } else {
                this.lastPostTextPattern = pattern;
                const patternRaw = pattern.normalize("NFD").replace(/\p{Diacritic}/gu, '').toLowerCase();
                this._lastPostPatternNormalized = patternRaw.replace(/[^a-z0-9]+/g, ' ').trim().replace(/\s+/g, ' ');
                this._lastPostPatternTokens = this._lastPostPatternNormalized.split(' ').filter(t => t.length >= 3);

                if (this._lastPostPatternTokens.length > 0) {
                    const escapedTokens = this._lastPostPatternTokens.map(t => t.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'));
                    this._heuristicRegex = new RegExp(escapedTokens.join('.*'), 'i');
                }
            }

            this.lastPostFound = false;
            this.reachedLastPost = false;
            console.log("lastPostTextPattern establecido:", pattern.toString());
        }

        // Verificar si un texto coincide con el patron de ultimo post
        _matchesLastPostPattern(postText) {
            if (!this.lastPostTextPattern) {
                return { matched: false, strategy: null, confidence: 0 };
            }

            const matchInfo = {
                matched: false,
                strategy: null,
                confidence: 0,
                postSnippet: postText.substring(0, 100),
                normalizedPost: '',
                matchingTokens: [],
                missingTokens: [],
                totalTokens: 0
            };

            // Modo RegExp
            if (this.lastPostTextPattern instanceof RegExp) {
                const direct = this.lastPostTextPattern.test(postText);
                const normalized = postText.normalize("NFD").replace(/\p{Diacritic}/gu, '').toLowerCase();
                matchInfo.normalizedPost = normalized;
                matchInfo.matched = direct || this.lastPostTextPattern.test(normalized);
                matchInfo.strategy = matchInfo.matched ? 'regex_match' : 'no_match';
                matchInfo.confidence = matchInfo.matched ? 100 : 0;

                if (this.debug) {
                    console.log("[DEBUG][match-regex] direct:", direct, "normalized:", normalized, "pattern:", this.lastPostTextPattern);
                }
                return matchInfo;
            }

            // Modo string: multiples estrategias
            const original = postText || '';
            const normAggressive = original.normalize("NFD").replace(/\p{Diacritic}/gu, '').toLowerCase()
                .replace(/[^a-z0-9]+/g, ' ').trim().replace(/\s+/g, ' ');

            matchInfo.normalizedPost = normAggressive;
            matchInfo.totalTokens = this._lastPostPatternTokens.length;

            // 1. Substring exacto en texto normalizado
            if (normAggressive.includes(this._lastPostPatternNormalized)) {
                matchInfo.matched = true;
                matchInfo.strategy = 'substring_exact';
                matchInfo.confidence = 100;
                matchInfo.matchingTokens = [...this._lastPostPatternTokens];
                return matchInfo;
            }

            // 2. Tokens parciales (70% o mas)
            if (this._lastPostPatternTokens.length > 0) {
                const storyTokensSet = new Set(normAggressive.split(' '));
                const matchingTokens = this._lastPostPatternTokens.filter(t => storyTokensSet.has(t));
                const matchPercentage = matchingTokens.length / this._lastPostPatternTokens.length;

                matchInfo.matchingTokens = matchingTokens;
                matchInfo.missingTokens = this._lastPostPatternTokens.filter(t => !storyTokensSet.has(t));

                if (matchPercentage >= 0.7) {
                    matchInfo.matched = true;
                    matchInfo.strategy = `partial_tokens_${Math.round(matchPercentage * 100)}%`;
                    matchInfo.confidence = Math.round(matchPercentage * 100);

                    if (this.debug) {
                        console.log("[DEBUG] Partial match:", matchingTokens, "de", this._lastPostPatternTokens, `(${Math.round(matchPercentage * 100)}%)`);
                    }
                }
            }

            return matchInfo;
        }

        // Manejar deteccion de ultimo post
        _handleLastPostDetected(postText) {
            const matchInfo = this._matchesLastPostPattern(postText);

            this.lastPostFound = true;
            this.reachedLastPost = true;
            this.preventFurtherCaptures = true;

            console.log("============ POST LIMITE DETECTADO ============");
            console.log("MOTIVO DE DETENCION:");
            console.log(`   Estrategia: ${matchInfo.strategy || 'unknown'}`);
            console.log(`   Confianza: ${matchInfo.confidence}%`);
            console.log(`   Tokens coincidentes (${matchInfo.matchingTokens.length}/${matchInfo.totalTokens}):`, matchInfo.matchingTokens);

            if (matchInfo.missingTokens.length > 0) {
                console.log(`   Tokens faltantes (${matchInfo.missingTokens.length}):`, matchInfo.missingTokens);
            }

            console.log("CONTENIDO DEL POST QUE ACTIVO LA DETECCION:");
            console.log(`   Snippet original: "${matchInfo.postSnippet}"`);
            console.log(`   Normalizado: "${matchInfo.normalizedPost}"`);

            console.log("PATRON CONFIGURADO:");
            if (this.lastPostTextPattern instanceof RegExp) {
                console.log(`   RegExp: ${this.lastPostTextPattern.toString()}`);
            } else {
                console.log(`   Texto: "${this.lastPostTextPattern}"`);
                console.log(`   Tokens del patron: [${this._lastPostPatternTokens.join(', ')}]`);
            }

            console.log("ESTADISTICAS:");
            console.log(`   Posts capturados hasta ahora: ${this.allPosts.length}`);
            console.log(`   Timestamp: ${new Date().toISOString()}`);
            console.log("============================================");

            // Desconectar observer
            if (this.observer) {
                try {
                    this.observer.disconnect();
                    console.log("Observer desconectado tras detectar post limite.");
                } catch (e) {
                    console.debug("Observer disconnect error", e);
                }
            }

            // PROTOCOLO DE RACE CONDITION
            this._executeRaceConditionProtocol(matchInfo);
        }

        // PROTOCOLO DE RACE CONDITION - Secuencia específica del plan
        async _executeRaceConditionProtocol(matchInfo) {
            console.log("🛑 **POST FRONTERA UBICADO** [BLOQUEANDO]");

            // 1. BLOQUEAR SCROLL COMPLETAMENTE
            this.blockScroll();
            console.log("🚫 Scroll bloqueado completamente");

            // 2. ESTABILIZACIÓN (2-3 segundos)
            console.log("⏳ **LUEGO SUBIENDO**");
            await new Promise(resolve => setTimeout(resolve, 2500));

            // 3. SCROLL HUMANIZADO AL INICIO (POSICIÓN SEGURA)
            console.log("⬆️ Moviendo a posición segura con scroll humanizado...");
            await this.scrollToTop();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 4. PROCESAMIENTO
            console.log("⚙️ **wait... processing posts**");

            // Marcar que el procesamiento está listo
            this.readyForExport = true;

            console.log("✅ **ok, posts listos**");
            console.log(`📊 Posts capturados antes de purga: ${this.allPosts.length}`);

            // Evento custom
            document.dispatchEvent(new CustomEvent('lastPostFound', {
                detail: {
                    detected_at: new Date().toISOString(),
                    match_info: matchInfo,
                    total_posts_captured: this.allPosts.length,
                    ready_for_export: true
                }
            }));
        }

        // Configurar bloqueo de scroll (simplificado)
        setupScrollBlocker() {
            // Solo necesitamos guardar el overflow original del body
            this.originalOverflow = document.body.style.overflow;
        }

        // Bloquear scroll (simplificado como en versión anterior)
        blockScroll() {
            if (this.isScrollBlocked) {
                return;
            }

            console.log("🚫 Scroll bloqueado para captura");
            this.isScrollBlocked = true;

            // Guardar estado original solo del body
            this.originalOverflow = document.body.style.overflow;

            // Bloquear scroll principal únicamente
            document.body.style.overflow = 'hidden';
        }



        // Verificar si estamos en vista individual
        isInIndividualView() {
            // Verificar URL
            const urlIndicators = ['/posts/', '/photo/', '/video/', 'story.php', 'permalink'];
            const currentUrl = window.location.href;
            const urlMatch = urlIndicators.some(indicator => currentUrl.includes(indicator));

            // Verificar elementos DOM de vista individual
            const modalDialog = document.querySelector('[role="dialog"]');
            const closeButton = document.querySelector('[aria-label*="Cerrar"], [aria-label*="Close"]');
            const individualPost = document.querySelector('[data-pagelet*="PermalinkPost"]');

            const domMatch = !!(modalDialog || closeButton || individualPost);

            return urlMatch || domMatch;
        }

        // Simular tecla Escape para cerrar vista individual
        simulateEscape() {
            const inIndividualView = this.isInIndividualView();

            if (inIndividualView) {
                this.log('navigation', 'VISTA INDIVIDUAL DETECTADA - Simulando Escape para cerrar', {url: window.location.href});
            } else {
                this.log('navigation', 'SIMULANDO ESCAPE PREVENTIVO - Por si se abrió vista individual');
            }

            // Crear evento de tecla Escape
            const escapeEvent = new KeyboardEvent('keydown', {
                key: 'Escape',
                code: 'Escape',
                keyCode: 27,
                which: 27,
                bubbles: true,
                cancelable: true
            });

            // Disparar evento en document
            document.dispatchEvent(escapeEvent);

            // También disparar keyup para completar la secuencia
            const escapeUpEvent = new KeyboardEvent('keyup', {
                key: 'Escape',
                code: 'Escape',
                keyCode: 27,
                which: 27,
                bubbles: true,
                cancelable: true
            });

            document.dispatchEvent(escapeUpEvent);

            // Verificar si se cerró después de un momento
            setTimeout(() => {
                const stillInView = this.isInIndividualView();
                if (stillInView) {
                    this.log('navigation', 'ESCAPE NO FUNCIONÓ - Vista individual sigue abierta', {url: window.location.href});
                } else {
                    this.log('navigation', 'ESCAPE EXITOSO - Regresado al feed');
                }
            }, 200);
        }

        // Inicializar monitoreo de ventana y contenido (reducido)
        initWindowMonitoring() {
            // Monitoreo cada 5 segundos (reducido)
            setInterval(() => {
                this.measureWindowGrowth();
            }, 5000);

            this.log('general', 'Monitoreo de ventana iniciado');
        }

        // Calcular porcentaje de scroll de forma segura
        calculateScrollPercentage(scrollTop, scrollHeight, windowHeight) {
            // Verificar valores válidos
            if (!scrollTop || !scrollHeight || !windowHeight ||
                scrollTop < 0 || scrollHeight <= 0 || windowHeight <= 0) {
                return 0;
            }

            const scrollableHeight = scrollHeight - windowHeight;

            if (scrollableHeight <= 0) {
                // No hay contenido scrolleable
                return 0;
            }

            const percentage = Math.round((scrollTop / scrollableHeight) * 100);

            // Asegurar que esté en rango válido
            return Math.max(0, Math.min(100, percentage));
        }

        // Medir crecimiento de ventana y contenido
        measureWindowGrowth() {
            const currentHeight = window.innerHeight;
            const currentScrollHeight = document.documentElement.scrollHeight;
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Contar posts visibles
            const visiblePosts = this.countVisiblePosts();
            const totalPosts = document.querySelectorAll('[data-ad-rendering-role="story_message"]').length;

            // Detectar cambios significativos
            const heightChanged = Math.abs(currentScrollHeight - this.windowMetrics.lastScrollHeight) > 100;
            const postsChanged = visiblePosts !== this.windowMetrics.lastPostCount;

            if (heightChanged || postsChanged) {
                const measurement = {
                    timestamp: new Date().toISOString(),
                    windowHeight: currentHeight,
                    scrollHeight: currentScrollHeight,
                    scrollTop: currentScrollTop,
                    visiblePosts: visiblePosts,
                    totalPosts: totalPosts,
                    heightGrowth: currentScrollHeight - this.windowMetrics.lastScrollHeight,
                    postGrowth: visiblePosts - this.windowMetrics.lastPostCount,
                    scrollPercentage: this.calculateScrollPercentage(currentScrollTop, currentScrollHeight, currentHeight)
                };

                this.log('window', `Altura: ${currentScrollHeight} (+${measurement.heightGrowth}) | Posts: ${visiblePosts} (+${measurement.postGrowth}) | Scroll: ${measurement.scrollPercentage}%`, measurement);

                // Guardar medición
                this.windowMetrics.measurements.push(measurement);

                // Mantener solo las últimas 20 mediciones
                if (this.windowMetrics.measurements.length > 20) {
                    this.windowMetrics.measurements.shift();
                }

                // Actualizar métricas
                this.windowMetrics.lastScrollHeight = currentScrollHeight;
                this.windowMetrics.lastPostCount = visiblePosts;

                // Detectar patrones
                this.analyzeGrowthPatterns();
            }
        }

        // Contar posts visibles en viewport
        countVisiblePosts() {
            const posts = document.querySelectorAll('[data-ad-rendering-role="story_message"]');
            let visibleCount = 0;

            posts.forEach(post => {
                const rect = post.getBoundingClientRect();
                const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
                if (isVisible) visibleCount++;
            });

            return visibleCount;
        }

        // Analizar patrones de crecimiento
        analyzeGrowthPatterns() {
            const recent = this.windowMetrics.measurements.slice(-5); // Últimas 5 mediciones

            if (recent.length >= 3) {
                const avgHeightGrowth = recent.reduce((sum, m) => sum + m.heightGrowth, 0) / recent.length;
                const avgPostGrowth = recent.reduce((sum, m) => sum + m.postGrowth, 0) / recent.length;

                if (avgHeightGrowth > 1000) {
                    this.log('patterns', `Crecimiento rápido detectado: +${Math.round(avgHeightGrowth)}px promedio`, {avgHeightGrowth, recent});
                }

                if (avgPostGrowth > 2) {
                    this.log('patterns', `Muchos posts nuevos: +${avgPostGrowth.toFixed(1)} posts promedio`, {avgPostGrowth, recent});
                }

                // Detectar si estamos cerca del final
                const lastMeasurement = recent[recent.length - 1];
                if (lastMeasurement.scrollPercentage > 90) {
                    this.log('patterns', `Cerca del final del feed: ${lastMeasurement.scrollPercentage}%`, lastMeasurement);
                }
            }
        }

        // Cargar posts históricos de localStorage
        loadHistoricalPosts() {
            try {
                const stored = localStorage.getItem('fbScraper_historical_posts');
                return stored ? JSON.parse(stored) : [];
            } catch (error) {
                this.log('errors', 'Error cargando posts históricos', error);
                return [];
            }
        }

        // Guardar posts históricos en localStorage
        saveHistoricalPosts() {
            try {
                // Mantener solo los últimos 100 posts históricos
                const toSave = this.temporalAnalysis.capturedPosts.slice(-100);
                localStorage.setItem('fbScraper_historical_posts', JSON.stringify(toSave));
            } catch (error) {
                this.log('errors', 'Error guardando posts históricos', error);
            }
        }

        // Configurar texto objetivo para búsqueda específica
        setTargetText(text, author = null) {
            this.temporalAnalysis.targetText = text;
            this.temporalAnalysis.targetAuthor = author;
            this.temporalAnalysis.targetMatchFound = false;

            // IMPORTANTE: También configurar como patrón de límite para purga
            this.setLastPostTextPattern(text);

            this.log('posts', 'Texto objetivo configurado (también como límite para purga)', {
                texto: text.substring(0, 50) + '...',
                autor: author
            });
        }

        // Calcular similitud de Jaccard entre dos posts
        calculateJaccardSimilarity(post1, post2) {
            // Crear sets de palabras para cada post
            const getWords = (post) => {
                const text = `${post.author} ${post.text}`.toLowerCase();
                return new Set(text
                    .replace(/[^\w\s]/g, '') // Eliminar puntuación
                    .split(/\s+/)
                    .filter(word => word.length > 1));
            };

            const words1 = getWords(post1);
            const words2 = getWords(post2);

            // Calcular intersección y unión
            const intersection = new Set([...words1].filter(word => words2.has(word)));
            const union = new Set([...words1, ...words2]);

            // Jaccard = |intersección| / |unión|
            return union.size > 0 ? intersection.size / union.size : 0;
        }

        // Calcular similitud de Jaccard entre post y texto objetivo (mejorado para textos cortos)
        calculateJaccardWithTarget(post) {
            if (!this.temporalAnalysis.targetText) {
                return 0;
            }

            // Para textos objetivo cortos, usar estrategia diferente
            const targetWords = this.temporalAnalysis.targetText.toLowerCase()
                .replace(/[^\w\s]/g, '') // Eliminar puntuación
                .split(/\s+/)
                .filter(word => word.length > 1);

            const postText = `${post.author} ${post.text}`.toLowerCase();
            const postWords = new Set(postText
                .replace(/[^\w\s]/g, '') // Eliminar puntuación
                .split(/\s+/)
                .filter(word => word.length > 1));

            if (targetWords.length <= 3) {
                // Para textos cortos: verificar si TODAS las palabras del target están en el post
                const foundWords = targetWords.filter(word => postWords.has(word));
                const containmentRatio = foundWords.length / targetWords.length;

                // DEBUG DETALLADO
                this.log('posts', `DEBUG DETALLADO - Búsqueda palabra por palabra:`, {
                    targetWords: targetWords,
                    postWordsSet: Array.from(postWords),
                    verificaciones: targetWords.map(word => ({
                        palabra: word,
                        encontrada: postWords.has(word),
                        enTexto: postText.includes(word)
                    })),
                    foundWords: foundWords,
                    containmentRatio: containmentRatio
                });

                return containmentRatio; // 1.0 si todas las palabras están presentes
            } else {
                // Para textos largos: usar Jaccard tradicional
                const targetPost = {
                    author: this.temporalAnalysis.targetAuthor || '',
                    text: this.temporalAnalysis.targetText
                };
                return this.calculateJaccardSimilarity(post, targetPost);
            }
        }

        // Verificar si el post coincide con el texto objetivo
        checkTargetMatch(postData) {
            // Debug: verificar estado del análisis temporal
            this.log('posts', `Estado análisis temporal`, {
                tieneTextoObjetivo: !!this.temporalAnalysis.targetText,
                textoObjetivo: this.temporalAnalysis.targetText,
                yaEncontrado: this.temporalAnalysis.targetMatchFound,
                autorPost: postData.author
            });

            if (!this.temporalAnalysis.targetText || this.temporalAnalysis.targetMatchFound) {
                this.log('posts', `Saltando verificación - Sin texto objetivo o ya encontrado`);
                return false;
            }

            const similarity = this.calculateJaccardWithTarget(postData);

            // Determinar umbral dinámico según longitud del texto objetivo
            const targetWords = this.temporalAnalysis.targetText.toLowerCase()
                .replace(/[^\w\s]/g, '') // Eliminar puntuación
                .split(/\s+/)
                .filter(word => word.length > 1);
            const dynamicThreshold = targetWords.length <= 3 ? 1.0 : this.temporalAnalysis.jaccardThreshold;

            // Debug: mostrar similitud calculada
            this.log('posts', `Verificando texto objetivo - Similitud: ${similarity.toFixed(3)} vs Umbral: ${dynamicThreshold}`, {
                autor: postData.author,
                textoPost: postData.text.substring(0, 30) + '...',
                textoObjetivo: this.temporalAnalysis.targetText.substring(0, 30) + '...',
                palabrasObjetivo: targetWords.length,
                umbralDinamico: dynamicThreshold
            });

            // Verificar también coincidencia de autor si se especificó
            let authorMatch = true;
            if (this.temporalAnalysis.targetAuthor) {
                authorMatch = postData.author.toLowerCase().includes(this.temporalAnalysis.targetAuthor.toLowerCase()) ||
                             this.temporalAnalysis.targetAuthor.toLowerCase().includes(postData.author.toLowerCase());

                this.log('posts', `Verificando autor - Match: ${authorMatch}`, {
                    autorPost: postData.author,
                    autorObjetivo: this.temporalAnalysis.targetAuthor
                });
            }

            if (similarity >= dynamicThreshold && authorMatch) {
                this.temporalAnalysis.targetMatchFound = true;
                this.temporalAnalysis.lastMatchIndex = this.temporalAnalysis.capturedPosts.length;

                this.log('posts', `TEXTO OBJETIVO ENCONTRADO (Similitud: ${similarity.toFixed(3)}, Umbral: ${dynamicThreshold})`, {
                    autor: postData.author,
                    textoEncontrado: postData.text.substring(0, 50) + '...',
                    textoObjetivo: this.temporalAnalysis.targetText.substring(0, 50) + '...',
                    indice: this.temporalAnalysis.lastMatchIndex
                });

                return true;
            }

            return false;
        }

        // Agregar post al análisis temporal
        addToTemporalAnalysis(postData) {
            if (this.temporalAnalysis.completionDetected) {
                this.log('posts', 'Captura completada - ignorando post adicional');
                return false;
            }

            // Agregar timestamp de captura
            postData.captureTimestamp = Date.now();
            postData.captureIndex = this.temporalAnalysis.capturedPosts.length;

            // PRIORIDAD 1: Verificar coincidencia con texto objetivo específico (ANTES de duplicados)
            const targetMatch = this.checkTargetMatch(postData);
            if (targetMatch) {
                // Agregar el post que coincide con el objetivo
                this.temporalAnalysis.capturedPosts.push(postData);

                // Detectar finalización inmediata o continuar capturando
                this.detectCompletion();
                return true;
            }

            // PRIORIDAD 2: Verificar duplicados con Jaccard en posts actuales
            for (let i = 0; i < this.temporalAnalysis.capturedPosts.length; i++) {
                const existingPost = this.temporalAnalysis.capturedPosts[i];
                const similarity = this.calculateJaccardSimilarity(postData, existingPost);

                if (similarity >= this.temporalAnalysis.jaccardThreshold) {
                    this.log('posts', `Duplicado detectado (Jaccard: ${similarity.toFixed(3)}) - ignorando`, {
                        nuevo: postData.author,
                        existente: existingPost.author,
                        indice: i
                    });
                    return false;
                }
            }

            // PRIORIDAD 2: Verificar coincidencia con posts históricos
            for (let i = 0; i < this.temporalAnalysis.historicalPosts.length; i++) {
                const historicalPost = this.temporalAnalysis.historicalPosts[i];
                const similarity = this.calculateJaccardSimilarity(postData, historicalPost);

                if (similarity >= this.temporalAnalysis.jaccardThreshold) {
                    this.log('posts', `COINCIDENCIA HISTÓRICA detectada (Jaccard: ${similarity.toFixed(3)})`, {
                        autor: postData.author,
                        indiceHistorico: i,
                        postsCapturados: this.temporalAnalysis.capturedPosts.length
                    });

                    // Marcar índice de coincidencia
                    this.temporalAnalysis.lastMatchIndex = this.temporalAnalysis.capturedPosts.length;

                    // Agregar el post coincidente
                    this.temporalAnalysis.capturedPosts.push(postData);

                    // Detectar finalización
                    this.detectCompletion();
                    return true;
                }
            }

            // Post genuinamente nuevo
            this.temporalAnalysis.capturedPosts.push(postData);
            this.log('posts', `Post agregado al análisis temporal (${this.temporalAnalysis.capturedPosts.length})`, {
                autor: postData.author,
                texto: postData.text.substring(0, 50) + '...'
            });

            return true;
        }

        // Detectar finalización de captura
        detectCompletion() {
            if (this.temporalAnalysis.lastMatchIndex === -1) {
                return; // No hay coincidencia
            }

            // Si hemos capturado posts después de la coincidencia, verificar si debemos parar
            const postsAfterMatch = this.temporalAnalysis.capturedPosts.length - this.temporalAnalysis.lastMatchIndex - 1;

            // Diferentes criterios según el tipo de coincidencia
            let shouldComplete = false;
            let reason = '';

            if (this.temporalAnalysis.targetMatchFound) {
                // Si encontramos el texto objetivo, capturar al menos 2 posts más
                if (postsAfterMatch >= 2) {
                    shouldComplete = true;
                    reason = `texto objetivo encontrado + ${postsAfterMatch} posts adicionales`;
                }
            } else {
                // Si es coincidencia histórica, capturar al menos 3 posts más
                if (postsAfterMatch >= 3) {
                    shouldComplete = true;
                    reason = `coincidencia histórica + ${postsAfterMatch} posts adicionales`;
                }
            }

            if (shouldComplete) {
                this.log('posts', `FINALIZACIÓN DETECTADA - ${reason}`);
                this.temporalAnalysis.completionDetected = true;

                // Apagar observador
                if (this.observer) {
                    this.observer.disconnect();
                    this.log('general', 'Observer desconectado - captura completada');
                }

                // Guardar posts históricos
                this.saveHistoricalPosts();

                return true;
            }

            return false;
        }

        // Obtener posts más recientes (después de coincidencia)
        getRecentPosts() {
            if (this.temporalAnalysis.lastMatchIndex === -1) {
                return this.temporalAnalysis.capturedPosts; // Todos los posts si no hay coincidencia
            }

            // Solo posts después de la coincidencia
            return this.temporalAnalysis.capturedPosts.slice(this.temporalAnalysis.lastMatchIndex + 1);
        }

        // Purgar array y obtener resultados finales
        getTemporalResults() {
            const results = {
                totalCaptured: this.temporalAnalysis.capturedPosts.length,
                matchIndex: this.temporalAnalysis.lastMatchIndex,
                recentPosts: this.getRecentPosts(),
                completionDetected: this.temporalAnalysis.completionDetected,
                jaccardThreshold: this.temporalAnalysis.jaccardThreshold
            };

            // Purgar array
            this.temporalAnalysis.capturedPosts = [];
            this.temporalAnalysis.lastMatchIndex = -1;
            this.temporalAnalysis.completionDetected = false;

            return results;
        }

        // Desbloquear scroll (simplificado)
        unblockScroll() {
            if (!this.isScrollBlocked) {
                return;
            }

            console.log("✅ Scroll desbloqueado");
            this.isScrollBlocked = false;

            // Restaurar scroll principal únicamente
            document.body.style.overflow = this.originalOverflow || '';
        }

        // Sistema de scroll humanizado con diferentes patrones
        async humanizedScroll(type = 'gentle', targetY = null) {
            const currentY = window.pageYOffset || document.documentElement.scrollTop;

            // Configuraciones por tipo de scroll
            const configs = {
                // Scroll inicial suave hacia arriba
                'initial_up': {
                    distance: -80 - Math.random() * 40, // -80 a -120px
                    duration: 800 + Math.random() * 400, // 800-1200ms
                    easing: 'easeOutQuad'
                },
                // Scroll hacia arriba para posición segura
                'safe_position': {
                    targetY: Math.random() * 50, // 0-50px desde arriba
                    duration: 1200 + Math.random() * 800, // 1200-2000ms
                    easing: 'easeInOutCubic'
                },
                // Scroll suave general
                'gentle': {
                    distance: targetY !== null ? (targetY - currentY) : (-50 - Math.random() * 30),
                    duration: 600 + Math.random() * 300,
                    easing: 'easeOutQuad'
                },
                // Scroll más natural para navegación
                'natural': {
                    distance: targetY !== null ? (targetY - currentY) : (-100 - Math.random() * 100),
                    duration: 1000 + Math.random() * 500,
                    easing: 'easeInOutQuart'
                }
            };

            const config = configs[type] || configs['gentle'];
            const finalTargetY = config.targetY !== undefined ? config.targetY : (currentY + config.distance);
            const totalDistance = finalTargetY - currentY;
            const duration = config.duration;

            if (Math.abs(totalDistance) < 5) {
                // Distancia muy pequeña, no hacer scroll
                return;
            }

            this.log('scroll', `Scroll humanizado (${type}): ${currentY} → ${finalTargetY} (${totalDistance}px en ${duration}ms)`);

            return new Promise(resolve => {
                const startTime = performance.now();
                const startY = currentY;

                const easing = {
                    easeOutQuad: t => t * (2 - t),
                    easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
                    easeInOutQuart: t => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t
                };

                const easingFunc = easing[config.easing] || easing.easeOutQuad;

                function animate(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Aplicar easing
                    const easedProgress = easingFunc(progress);

                    // Calcular posición actual con pequeñas variaciones humanas
                    const humanVariation = Math.sin(elapsed * 0.01) * 2; // Pequeña oscilación
                    const currentPosition = startY + (totalDistance * easedProgress) + humanVariation;

                    window.scrollTo(0, currentPosition);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // Asegurar posición final exacta
                        window.scrollTo(0, finalTargetY);
                        resolve();
                    }
                }

                requestAnimationFrame(animate);
            });
        }

        // Métodos de scroll humanizado específicos
        async scrollToTop() {
            this.log('scroll', 'Scroll humanizado al inicio');
            await this.humanizedScroll('safe_position');
        }

        async scrollUp(distance = null) {
            this.log('scroll', 'Scroll humanizado hacia arriba');
            const customDistance = distance || (-80 - Math.random() * 40);
            await this.humanizedScroll('gentle', window.pageYOffset + customDistance);
        }

        async scrollDown(distance = null) {
            this.log('scroll', 'Scroll humanizado hacia abajo');
            const customDistance = distance || (100 + Math.random() * 50);
            await this.humanizedScroll('natural', window.pageYOffset + customDistance);
        }

        async scrollToElement(element) {
            if (!element) return;

            const rect = element.getBoundingClientRect();
            const targetY = window.pageYOffset + rect.top - (window.innerHeight / 2);

            this.log('scroll', 'Scroll humanizado a elemento');
            await this.humanizedScroll('natural', targetY);
        }

        // Configurar bloqueadores de interaccion
        setupInteractionBlockers() {
            // Bloquear modales y popups
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    const modals = document.querySelectorAll('[role="dialog"], [aria-modal="true"]');
                    modals.forEach(modal => {
                        const closeBtn = modal.querySelector('[aria-label*="Cerrar"], [aria-label*="Close"]');
                        if (closeBtn) closeBtn.click();
                    });
                }
            });
        }

        // Capturar post (simplificado como versión anterior)
        async capturePost(postContainer, storyElement) {
            const postId = this.generatePostId(postContainer);

            if (this.capturedPosts.has(postId)) {
                return;
            }

            try {
                console.log(`📝 Capturando post: ${postId}`);
                // Bloquear scroll temporalmente
                this.blockScroll();

                // 1. Expandir contenido truncado (ver más)
                await this.expandSeeMore(postContainer);

                // 2. Esperar estabilidad del DOM (como en versión anterior)
                const preHTML = storyElement.innerHTML;
                await new Promise(r => setTimeout(r, 120));
                const midHTML = storyElement.innerHTML;

                if (preHTML !== midHTML) {
                    // Hubo mutación rápida: esperar un poco más para estabilizar
                    await new Promise(r => setTimeout(r, 300));
                }

                // 3. Extraer datos del post
                const postData = this.extractPostData(postContainer, storyElement);

                if (postData) {
                    // Agregar a capturedPosts y allPosts
                    this.capturedPosts.add(postId);
                    this.allPosts.push(postData);

                    console.log("📝 Post capturado:", postData);

                    // Evento personalizado para notificar
                    document.dispatchEvent(new CustomEvent('postCaptured', {
                        detail: postData
                    }));

                    // Callback Python si existe
                    if (window.pyPostCaptured) {
                        try {
                            window.pyPostCaptured(postData);
                        } catch (e) {
                            console.debug("pyPostCaptured error", e);
                        }
                    }
                }

                // ESCAPE FALLBACK: Cerrar cualquier vista individual que se haya abierto
                this.simulateEscape();

                // Desbloquear scroll después de un momento
                setTimeout(() => this.unblockScroll(), 2000);

            } catch (error) {
                console.error("❌ Error capturando post:", error);
                this.simulateEscape(); // También en caso de error
                this.unblockScroll();
            }
        }

        // Expandir "Ver mas"
        async expandSeeMore(container) {
            const seeMoreButtons = container.querySelectorAll('div[role="button"]');

            for (const btn of seeMoreButtons) {
                const text = btn.textContent.toLowerCase();
                if (text.includes('ver más') || text.includes('see more') || text.includes('ver mas')) {
                    try {
                        btn.click();
                        await new Promise(resolve => setTimeout(resolve, 500));

                        if (this.debug) {
                            console.log('[DEBUG] "Ver más" expandido');
                        }
                    } catch (e) {
                        if (this.debug) {
                            console.log('[DEBUG] Error expandiendo "Ver más":', e);
                        }
                    }
                    break;
                }
            }
        }

        // Extraer datos del post con selectores mejorados
        extractPostData(postContainer, storyElement) {
            try {
                console.log(`[DEBUG] Iniciando extracción de datos del post`);

                // Obtener link del post usando HTML string (evitar navegación)
                let postLink = '';
                try {
                    const htmlString = postContainer.innerHTML;
                    const linkPatterns = [
                        /href="([^"]*\/posts\/[^"]*)"/,
                        /href="([^"]*story\.php[^"]*)"/,
                        /href="([^"]*permalink[^"]*)"/,
                        /href="([^"]*fbid[^"]*)"/,
                        /href="([^"]*\/photo\/[^"]*)"/,
                        /href="([^"]*\/video\/[^"]*)"/
                    ];

                    for (const pattern of linkPatterns) {
                        const match = htmlString.match(pattern);
                        if (match) {
                            postLink = match[1];
                            console.log(`[DEBUG] Link del post encontrado: ${postLink.substring(0, 50)}...`);
                            break;
                        }
                    }

                    if (!postLink) {
                        console.log(`[DEBUG] No se encontró link del post en HTML string`);
                    }
                } catch (error) {
                    console.log(`[DEBUG] Error extrayendo link del post:`, error);
                }

                const textEl = storyElement;

                // Extraer autor usando lógica mejorada (basada en atributos inmutables)
                let authorName = 'Autor desconocido';

                // Estrategia 1: Usar lógica del compañero - aria-label en enlaces
                const linkWithAriaLabel = postContainer.querySelector('a[aria-label]');
                if (linkWithAriaLabel) {
                    const ariaLabel = linkWithAriaLabel.getAttribute('aria-label');
                    if (ariaLabel && ariaLabel.length > 2 && !ariaLabel.includes('http')) {
                        authorName = ariaLabel;
                    }
                }

                // Estrategia 2: SVG con aria-label
                if (authorName === 'Autor desconocido') {
                    const svgWithAriaLabel = postContainer.querySelector('svg[aria-label]');
                    if (svgWithAriaLabel) {
                        const ariaLabel = svgWithAriaLabel.getAttribute('aria-label');
                        if (ariaLabel && ariaLabel.length > 2 && !ariaLabel.includes('http')) {
                            authorName = ariaLabel;
                        }
                    }
                }

                // Estrategia 3: Atributo title
                if (authorName === 'Autor desconocido') {
                    const elementWithTitle = postContainer.querySelector('[title]');
                    if (elementWithTitle) {
                        const title = elementWithTitle.getAttribute('title');
                        if (title && title.length > 2 && !title.includes('http')) {
                            authorName = title;
                        }
                    }
                }

                // Estrategia 4: Método original como fallback
                if (authorName === 'Autor desconocido') {
                    const fallbackAuthorEl = postContainer.querySelector(
                        'b > span, strong > span, h3 a, [data-ad-rendering-role="profile_name"] a, ' +
                        'h3 span, [role="link"], strong a'
                    );
                    if (fallbackAuthorEl) {
                        authorName = fallbackAuthorEl.textContent.trim();
                    } else {
                        // Buscar en otros lugares comunes
                        const altAuthor = postContainer.querySelector(
                            'strong, [role="link"] span, h3 span, b'
                        );
                        if (altAuthor) {
                            authorName = altAuthor.textContent.trim();
                        }
                    }
                }

                // Obtener link del autor
                let authorLink = '';
                const authorLinkEl = postContainer.querySelector('a[href*="/user/"], a[href*="/profile.php"], a[aria-label]');
                if (authorLinkEl) {
                    authorLink = authorLinkEl.href || '';
                }

                console.log(`[DEBUG] Creando objeto postData con autor: ${authorName}`);

                const postData = {
                    id: this.generatePostId(postContainer),
                    author: authorName,
                    authorLink: authorLink,
                    text: textEl ? textEl.textContent.trim() : '',
                    link: postLink,
                    timestamp: new Date().toISOString(),
                    images: this.extractImages(postContainer),
                    videos: this.extractVideos(postContainer),
                    reactions: this.extractReactions(postContainer),
                    comments: this.extractComments(postContainer),
                    shares: this.extractShares(postContainer)
                };

                return postData;
            } catch (error) {
                console.error('Error extrayendo datos del post:', error);
                return null;
            }
        }

        // Extraer imagenes
        extractImages(container) {
            const images = [];
            const imgElements = container.querySelectorAll('img[src*="scontent"]');

            imgElements.forEach(img => {
                if (img.src && !img.src.includes('emoji')) {
                    images.push({
                        src: img.src,
                        alt: img.alt || ''
                    });
                }
            });

            return images;
        }

        // Extraer videos
        extractVideos(container) {
            const videos = [];
            const videoElements = container.querySelectorAll('video');

            videoElements.forEach(video => {
                if (video.src) {
                    videos.push({
                        src: video.src,
                        poster: video.poster || ''
                    });
                }
            });

            return videos;
        }

        // Extraer reacciones usando nuestro framework personalizado
        extractReactions(container) {
            // Usar nuestro framework para buscar por texto
            const reactionTexts = ['reacciones', 'reactions', 'Me gusta', 'like', 'likes'];

            // Buscar en spans con texto de reacciones
            let el = CustomSelectors.findFirstByAnyText(container, 'span[dir="auto"]', reactionTexts);
            if (el) {
                const match = el.textContent.match(/(\d+)/);
                if (match) return parseInt(match[1], 10);
            }

            // Buscar en botones con aria-label
            el = CustomSelectors.findFirstByAnyText(container, 'div[role="button"]', reactionTexts);
            if (el) {
                const text = el.textContent || el.getAttribute('aria-label') || '';
                const match = text.match(/(\d+)/);
                if (match) return parseInt(match[1], 10);
            }

            // Buscar en elementos con aria-label específicos
            const ariaSelectors = [
                '[aria-label*="reacciones"]', '[aria-label*="reactions"]',
                '[aria-label*="Me gusta"]', '[aria-label*="like"]'
            ];

            for (const selector of ariaSelectors) {
                el = container.querySelector(selector);
                if (el) {
                    const text = el.textContent || el.getAttribute('aria-label') || '';
                    const match = text.match(/(\d+)/);
                    if (match) return parseInt(match[1], 10);
                }
            }

            // Buscar números cerca de iconos de reacción
            const reactionIcons = container.querySelectorAll('svg, img[alt*="like"], img[alt*="reacción"]');
            for (const icon of reactionIcons) {
                const parent = icon.closest('div, span');
                if (parent) {
                    const text = parent.textContent;
                    const match = text.match(/(\d+)/);
                    if (match) return parseInt(match[1], 10);
                }
            }

            return 0;
        }

        // Extraer comentarios usando nuestro framework personalizado
        extractComments(container) {
            // Usar nuestro framework para buscar por texto
            const commentTexts = ['comentarios', 'comments', 'Comentar', 'Comment', 'comentario'];

            // Buscar en spans con texto de comentarios
            let el = CustomSelectors.findFirstByAnyText(container, 'span', commentTexts);
            if (el) {
                const match = el.textContent.match(/(\d+)/);
                if (match) return parseInt(match[1], 10);
            }

            // Buscar en botones con texto de comentarios
            el = CustomSelectors.findFirstByAnyText(container, 'div[role="button"]', commentTexts);
            if (el) {
                const text = el.textContent || el.getAttribute('aria-label') || '';
                const match = text.match(/(\d+)/);
                if (match) return parseInt(match[1], 10);
            }

            // Buscar en elementos con aria-label específicos
            const ariaSelectors = [
                '[aria-label*="comentarios"]', '[aria-label*="comments"]',
                '[aria-label*="Comentar"]', '[aria-label*="Comment"]'
            ];

            for (const selector of ariaSelectors) {
                el = container.querySelector(selector);
                if (el) {
                    const text = el.textContent || el.getAttribute('aria-label') || '';
                    const match = text.match(/(\d+)/);
                    if (match) return parseInt(match[1], 10);
                }
            }

            return 0;
        }

        // Extraer compartidos usando nuestro framework personalizado
        extractShares(container) {
            // Usar nuestro framework para buscar por texto
            const shareTexts = ['compartir', 'share', 'Compartir', 'Share', 'compartido', 'shared'];

            // Buscar en spans con texto de compartidos
            let el = CustomSelectors.findFirstByAnyText(container, 'span', shareTexts);
            if (el) {
                const match = el.textContent.match(/(\d+)/);
                if (match) return parseInt(match[1], 10);
            }

            // Buscar en botones con texto de compartidos
            el = CustomSelectors.findFirstByAnyText(container, 'div[role="button"]', shareTexts);
            if (el) {
                const text = el.textContent || el.getAttribute('aria-label') || '';
                const match = text.match(/(\d+)/);
                if (match) return parseInt(match[1], 10);
            }

            // Buscar en elementos con aria-label específicos
            const ariaSelectors = [
                '[aria-label*="compartir"]', '[aria-label*="share"]',
                '[aria-label*="Compartir"]', '[aria-label*="Share"]'
            ];

            for (const selector of ariaSelectors) {
                el = container.querySelector(selector);
                if (el) {
                    const text = el.textContent || el.getAttribute('aria-label') || '';
                    const match = text.match(/(\d+)/);
                    if (match) return parseInt(match[1], 10);
                }
            }

            return 0;
        }

        // Guardar en localStorage
        saveToStorage() {
            try {
                const data = {
                    posts: this.allPosts,
                    capturedIds: Array.from(this.capturedPosts),
                    lastUpdate: new Date().toISOString(),
                    totalPosts: this.allPosts.length
                };

                localStorage.setItem('fb_scraper_data', JSON.stringify(data));
            } catch (error) {
                console.error('Error guardando en localStorage:', error);
            }
        }

        // Cargar desde localStorage
        loadFromStorage() {
            try {
                const data = localStorage.getItem('fb_scraper_data');
                if (data) {
                    const parsed = JSON.parse(data);
                    this.allPosts = parsed.posts || [];
                    this.capturedPosts = new Set(parsed.capturedIds || []);
                    console.log(`Datos cargados: ${this.allPosts.length} posts`);
                }
            } catch (error) {
                console.error('Error cargando desde localStorage:', error);
            }
        }

        // Obtener todos los posts con purga automática
        getAllPosts() {
            let postsToReturn;

            // Si hay análisis temporal activo, usar esos posts
            if (this.temporalAnalysis.capturedPosts.length > 0) {
                postsToReturn = [...this.temporalAnalysis.capturedPosts];
            } else {
                postsToReturn = [...this.allPosts];
            }

            // PURGA: Si se detectó frontera, eliminar posts después del límite
            if (this.lastPostFound && this.lastPostTextPattern) {
                const originalCount = postsToReturn.length;
                let limitIndex = -1;

                // Buscar el índice del post límite
                for (let i = 0; i < postsToReturn.length; i++) {
                    const post = postsToReturn[i];
                    const matchResult = this._matchesLastPostPattern(post.text || '');
                    if (matchResult.matched) {
                        limitIndex = i;
                        break;
                    }
                }

                if (limitIndex >= 0) {
                    // Purgar posts desde el límite hacia adelante (incluyendo el post límite)
                    postsToReturn = postsToReturn.slice(0, limitIndex);
                    console.log(`🗑️ Posts purgados en getPosts(): ${originalCount - postsToReturn.length} eliminados`);
                }
            }

            return postsToReturn;
        }

        // Obtener estadisticas
        getStats() {
            return {
                totalPosts: this.allPosts.length,
                capturedIds: this.capturedPosts.size,
                lastPostFound: this.lastPostFound,
                reachedLastPost: this.reachedLastPost,
                isScrollBlocked: this.isScrollBlocked,
                pendingPosts: this.pendingPosts.length
            };
        }

        // Exportar datos con purga automática
        exportData() {
            let postsToExport = [...this.allPosts];
            let purgeInfo = null;

            // PURGA: Si se detectó frontera, eliminar posts después del límite
            if (this.lastPostFound && this.lastPostTextPattern) {
                console.log("🔍 Aplicando purga de posts después del límite...");

                const originalCount = postsToExport.length;
                let limitIndex = -1;

                // Buscar el índice del post límite
                for (let i = 0; i < postsToExport.length; i++) {
                    const post = postsToExport[i];
                    const matchResult = this._matchesLastPostPattern(post.text || '');
                    if (matchResult.matched) {
                        limitIndex = i;
                        console.log(`📍 Post límite encontrado en índice ${i}: "${post.text.slice(0, 50)}..."`);
                        break;
                    }
                }

                if (limitIndex >= 0) {
                    // Purgar posts desde el límite hacia adelante (incluyendo el post límite)
                    const purgedPosts = postsToExport.splice(limitIndex);
                    purgeInfo = {
                        limitIndex: limitIndex,
                        originalCount: originalCount,
                        purgedCount: purgedPosts.length,
                        finalCount: postsToExport.length,
                        limitPost: purgedPosts[0] ? {
                            author: purgedPosts[0].author,
                            text: purgedPosts[0].text.slice(0, 100) + '...'
                        } : null
                    };

                    console.log(`🗑️ Purgados ${purgedPosts.length} posts (desde índice ${limitIndex})`);
                    console.log(`📊 Posts finales para export: ${postsToExport.length}/${originalCount}`);
                } else {
                    console.log("⚠️ Post límite no encontrado en array para purga");
                }
            }

            const data = {
                posts: postsToExport,
                stats: this.getStats(),
                exportDate: new Date().toISOString(),
                purgeInfo: purgeInfo
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `facebook-posts-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            if (purgeInfo) {
                console.log("✅ Export completado con purga aplicada");
            } else {
                console.log("✅ Export completado sin purga");
            }
        }

        // Activar/desactivar debug
        setDebug(flag) {
            this.debug = flag;
            console.log(`Debug ${flag ? 'activado' : 'desactivado'}`);
        }

        // Destruir scraper
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
            }
            this.unblockScroll();
            this.preventFurtherCaptures = true;
            console.log('Scraper destruido');
        }
    }
    
    // Inicializar el scraper
    const postCapture = new FacebookPostCapture({
        debug: false
    });

    // API publica global con nombre discreto
    globalThis.browserGet = {
        start: () => postCapture.init(),
        stop: () => postCapture.destroy(),
        getPosts: () => postCapture.getAllPosts(),
        setPattern: (pattern) => postCapture.setLastPostTextPattern(pattern),
        getStats: () => postCapture.getStats(),
        export: () => postCapture.exportData(),
        debug: (flag) => postCapture.setDebug(flag),
        exportLogs: () => postCapture.exportLogs(),
        showLogs: (category) => postCapture.showLogs(category),
        getTemporalResults: () => postCapture.getTemporalResults(),
        getRecentPosts: () => postCapture.getRecentPosts(),
        setTargetText: (text, author) => postCapture.setTargetText(text, author),
        // Métodos de scroll humanizado
        scrollUp: (distance) => postCapture.scrollUp(distance),
        scrollDown: (distance) => postCapture.scrollDown(distance),
        scrollToTop: () => postCapture.scrollToTop(),
        scrollToElement: (element) => postCapture.scrollToElement(element),
        humanizedScroll: (type, targetY) => postCapture.humanizedScroll(type, targetY),
        // Método de escape
        simulateEscape: () => postCapture.simulateEscape()
    };

    console.log('=== FACEBOOK SCRAPER v2.1 CARGADO EXITOSAMENTE ===');
    console.log('API disponible en: browserGet');
    console.log('Comandos principales:');
    console.log('  browserGet.debug(true)           - Activar logs detallados');
    console.log('  browserGet.getPosts()            - Ver posts capturados');
    console.log('  browserGet.getStats()            - Ver estadisticas');
    console.log('  browserGet.setTargetText(txt)    - Buscar texto específico');
    console.log('  browserGet.getRecentPosts()      - Solo posts nuevos');
    console.log('  browserGet.getTemporalResults()  - Resultados con análisis');
    console.log('  browserGet.export()              - Exportar datos');
    console.log('  browserGet.exportLogs()          - Exportar logs completos');
    console.log('  browserGet.stop()                - Detener scraper');
    console.log('Scroll humanizado:');
    console.log('  browserGet.scrollUp(distance)    - Scroll arriba humanizado');
    console.log('  browserGet.scrollDown(distance)  - Scroll abajo humanizado');
    console.log('  browserGet.scrollToTop()         - Scroll al inicio humanizado');
    console.log('Utilidades:');
    console.log('  facebookpleasebanemeimscrapping.simulateEscape()      - Cerrar vistas individuales');
    console.log('=== SCRAPER ACTIVO - DETECTANDO POSTS AUTOMATICAMENTE ===');

})();

browserGet.setTargetText("estamos contratando")
