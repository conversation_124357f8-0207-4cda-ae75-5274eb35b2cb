#!/usr/bin/env python3
# Testing purposes currently being evalueted
"""
PROTOTIPO: Observador simple que usa el flow existente para autenticación
Este es código prototípico para pruebas antes de refactorizar en clases
"""

import json
import random
import time
from datetime import datetime
from pathlib import Path
from database.post_repository import PostRepository

def simple_manual_analysis(page, group_url="https://www.facebook.com/groups/118581375315200/?sorting_setting=CHRONOLOGICAL"):
    """
    ANÁLISIS MANUAL GUIADO SIMPLIFICADO
    Solo análisis manual sin automatización
    """
    print(f"🎯 INICIANDO ANÁLISIS MANUAL GUIADO SIMPLIFICADO")
    print(f"🔗 Grupo: {group_url}")
    print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # Navegar al grupo
        print(f"\n🌐 Navegando al grupo...")
        page.goto(group_url)
        time.sleep(3)

        # Configuración simplificada
        accumulated_posts = []  # Lista acumulativa de posts extraídos

        print(f"\n🎯 [ANÁLISIS MANUAL] Iniciando análisis guiado")
        print(f"📋 [INSTRUCCIONES] Este modo te permite analizar manualmente el comportamiento")

        scroll_count = 1

        # BUCLE SIMPLIFICADO - Solo análisis manual
        while True:
            try:
                print(f"\n📜 ANÁLISIS {scroll_count}")
                print(f"-" * 30)

                # ANÁLISIS MANUAL GUIADO (sin scroll automático)
                current_posts = manual_guided_analysis(page, accumulated_posts, scroll_count)

                if current_posts['new_posts']:
                    print(f"   ✅ Posts analizados: {len(current_posts['new_posts'])}")
                    print(f"   📊 Total posts acumulados: {len(accumulated_posts)}")

                    # Agregar posts a acumulados
                    accumulated_posts.extend(current_posts['new_posts'])

                    # Verificar si se encontró el límite
                    if current_posts['limit_found']:
                        print(f"   🎯 ¡LÍMITE ENCONTRADO! Finalizando análisis")
                        break
                else:
                    print(f"   📭 No se analizaron posts en esta iteración")

                # Preguntar si continuar
                print(f"\n🔄 [CONTINUAR] Enter=Continuar scroll, 's'=Salir y exportar resultados")
                user_input = input().strip().lower()
                if user_input == 's':
                    print(f"   🛑 Análisis finalizado por el usuario")
                    break

                scroll_count += 1

                # Límite de seguridad
                if scroll_count > 20:
                    print(f"   ⚠️ Límite de 20 análisis alcanzado")
                    break

            except Exception as e:
                print(f"   ❌ Error en análisis {scroll_count}: {e}")
                break

        # Resumen final y exportación
        print(f"\n📊 RESUMEN FINAL:")
        print(f"   📝 Total posts analizados: {len(accumulated_posts)}")
        print(f"   🔄 Análisis realizados: {scroll_count}")

        # Exportar resultados
        try:
            import json
            from datetime import datetime

            export_data = {
                'timestamp': datetime.now().isoformat(),
                'analysis_summary': {
                    'total_posts_analyzed': len(accumulated_posts),
                    'analysis_count': scroll_count,
                    'group_url': group_url
                },
                'posts_data': accumulated_posts,
                'detailed_analysis': {
                    'posts_with_authors': len([p for p in accumulated_posts if p.get('author') and p['author'] not in ['No extraído', 'Error en extracción']]),
                    'posts_with_text': len([p for p in accumulated_posts if p.get('text') and len(p['text']) > 10]),
                    'posts_with_links': len([p for p in accumulated_posts if p.get('link') and p['link'] != 'No extraído']),
                    'posts_with_images': len([p for p in accumulated_posts if p.get('images', 0) > 0])
                }
            }

            # Guardar archivo de exportación
            export_filename = f"flow/manual_analysis_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(export_filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            print(f"\n💾 [EXPORTADO] Resultados guardados en: {export_filename}")
            print(f"📊 [ESTADÍSTICAS]:")
            print(f"   👤 Posts con autor: {export_data['detailed_analysis']['posts_with_authors']}")
            print(f"   📝 Posts con texto: {export_data['detailed_analysis']['posts_with_text']}")
            print(f"   🔗 Posts con link: {export_data['detailed_analysis']['posts_with_links']}")
            print(f"   🖼️ Posts con imágenes: {export_data['detailed_analysis']['posts_with_images']}")

        except Exception as e:
            print(f"⚠️ [WARNING] Error exportando resultados: {e}")

        return {
            'success': True,
            'posts_analyzed': len(accumulated_posts),
            'analysis_count': scroll_count,
            'posts': accumulated_posts
        }

    except Exception as e:
        print(f"❌ Error en análisis manual: {e}")
        return {
            'success': False,
            'error': str(e)
        }


def extract_group_id_from_url(group_url):
    """
    Extrae el group_id de una URL de Facebook
    Ejemplo: https://www.facebook.com/groups/118581375315200/ -> 118581375315200
    """
    try:
        import re
        # Buscar patrón de números después de /groups/
        match = re.search(r'/groups/(\d+)', group_url)
        if match:
            return int(match.group(1))
        return None
    except Exception as e:
        print(f"❌ Error extrayendo group_id de URL: {e}")
        return None

def observe_group_with_page(page, group_url="https://www.facebook.com/groups/118581375315200/?sorting_setting=CHRONOLOGICAL"):
    """
    Observa un grupo específico con la página ya autenticada
    """
    print(f"🎯 INICIANDO OBSERVACIÓN EXPERIMENTAL")
    print(f"🔗 Grupo: {group_url}")
    print(f"=" * 60)
    
    results = {
        'group_url': group_url,
        'timestamp': datetime.now().isoformat(),
        'observations': []
    }
    
    try:
        # 1. Navegar al grupo
        print(f"🌐 Navegando al grupo...")
        page.goto(group_url, wait_until="domcontentloaded")
        print(f"✅ DOMContentLoaded alcanzado")
        
        # Pausa de 3 segundos para permitir carga adicional
        print(f"⏳ Pausando 3 segundos para carga adicional...")
        time.sleep(3)
        
        # Verificar que el grupo cargó correctamente usando criterio de fallback
        from utils.page_detectors import page_detectors
        max_attempts = 6  # 30 segundos total
        for attempt in range(max_attempts):
            group_detection = page_detectors.is_group_page(page)
            
            if group_detection['is_group_page']:
                print(f"✅ Grupo detectado correctamente (intento {attempt+1})")
                print(f"📊 Artículos detectados: {group_detection['details'].get('articles_count', 0)}")
                break
            
            if attempt < max_attempts - 1:
                print(f"⏳ Intento {attempt+1}/{max_attempts} - esperando 5s...")
                time.sleep(5)
        else:
            # Si no se detecta grupo después de todos los intentos, fallar
            results['success'] = False
            results['message'] = "❌ Error: No se pudo detectar grupo válido después de 30s"
            return results
        
        print(f"✅ Navegación completada")
        print(f"📍 URL actual: {page.url}")

        # 2. Observación inicial
        print(f"\n📊 OBSERVACIÓN INICIAL")
        print(f"-" * 40)
        initial_state = capture_state(page, "initial")
        results['observations'].append(initial_state)
        
        # Mostrar conteos iniciales
        counts = initial_state.get('counts', {})
        for key, value in counts.items():
            print(f"   {key}: {value}")

        # 3. Determinar objetivo de posts
        target_posts = 10
        print(f"\n🎯 OBJETIVO: Obtener al menos {target_posts} posts completos")
        print(f"📜 SCROLL INTELIGENTE CON DETECCIÓN GLIMMER")

        # 4. Preparar datos de límite para verificación durante scroll
        stored_posts = [{
            'text': 'A quien de ustedes les gustaria aprender a mejorar en programacion ya sea la logica o otras cuestiones quien dice yo',
            'author': 'Ulises Naun'
        }]

        print(f"📚 Post límite para verificación: {stored_posts[0]['author']}")
        print(f"   Texto: {stored_posts[0]['text'][:100]}...")

        # 5. Configuración de scrolls desde archivo de configuración
        print(f"\n🎯 CONFIGURACIÓN DE SCROLLS")
        print(f"-" * 40)

        # Cargar configuración desde archivo
        try:
            import json
            from pathlib import Path

            config_file = Path(__file__).parent / "config" / "scroll_config.json"

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    scroll_config = json.load(f)
                    max_scrolls = scroll_config.get('scroll_per_group', 10)
                    print(f"📜 Configuración cargada desde archivo: {max_scrolls} scrolls por grupo")
                    print(f"✅ Usando configuración 'scroll per group': {max_scrolls} scrolls")
            else:
                max_scrolls = 10
                print(f"⚠️ Archivo de configuración no encontrado, usando valor por defecto: {max_scrolls} scrolls")

        except Exception as e:
            max_scrolls = 10
            print(f"⚠️ Error cargando configuración: {e}")
            print(f"✅ Usando valor por defecto: {max_scrolls} scrolls")

        # 6. Scroll inteligente con extracción progresiva (NUEVA LÓGICA)
        scroll_count = 0
        accumulated_posts = []  # Lista acumulativa de posts extraídos
        limit_found_during_scroll = False  # Flag para parar cuando se encuentre límite

        # MEDICIÓN DE TIEMPOS - Inicio del ciclo completo
        # Mide: tiempo total del proceso + tiempo individual de cada scroll + extracción
        cycle_start_time = time.time()
        scroll_times = []  # Lista para almacenar tiempo de cada scroll
        print(f"⏱️ [TIMING] Iniciando medición de tiempos del ciclo completo")

        print(f"\n📜 INICIANDO SCROLL INTELIGENTE")
        print(f"🎯 Objetivo: {max_scrolls} scrolls máximo")
        print(f"=" * 50)

        while scroll_count < max_scrolls and not limit_found_during_scroll:
            scroll_count += 1

            # MEDICIÓN DE TIEMPOS - Inicio del scroll individual
            scroll_start_time = time.time()
            print(f"\n📜 SCROLL {scroll_count}/{max_scrolls}")
            print(f"-" * 30)
            print(f"⏱️ [TIMING] Iniciando scroll {scroll_count} - {time.strftime('%H:%M:%S')}")

            # COMENTADO PARA TEST: Verificación de overlays (consume tiempo)
            # print(f"   🔍 Verificando overlays...")
            # overlay_closed = detect_and_close_overlays(page)
            # overlay_still_visible = False
            # overlay_indicators = [...]
            # if overlay_still_visible: continue

            # VERSIÓN SIMPLIFICADA PARA TEST (comentando verificaciones lentas)
            print(f"   📜 Ejecutando scroll...")
            page.evaluate("window.scrollBy(0, 800)")
            time.sleep(2)  # Pausa mínima después del scroll

            glimmer_count = 0  # Variable necesaria para lógica posterior

            # COMENTADO: Verificaciones que consumen tiempo
            # glimmer_count = detect_glimmer_elements(page)
            # print(f"   🔍 Elementos glimmer detectados: {glimmer_count}")
            # print(f"   ⏳ Esperando que terminen de cargar los glimmers...")
            # wait_for_glimmers_to_disappear(page)
            # print(f"   🔍 Verificando completitud de posts...")
            # complete_posts_count = wait_for_posts_completion(page)

            # FUNCIÓN DE TEST RÁPIDA (comentando implementación anterior)
            print(f"   🧪 [TEST] Usando función test_extract rápida...")

            # REFACTORIZADO: Usando nueva clase PostExtractor
            print(f"   🚀 [REFACTORIZADO] Extracción 1 a 1 con expansión individual usando PostExtractor...")
            print(f"   🔧 [REFACTORIZADO] Usando PostExtractor.extract_posts_one_by_one_with_expansion()")

            # Importar y usar la nueva clase refactorizada
            from post_operations import PostExtractor
            post_extractor = PostExtractor()

            # Extraer información del grupo desde la URL de la página
            post_extractor.extract_group_info_from_page(page)

            current_posts_result = post_extractor.extract_posts_one_by_one_with_expansion(
                page=page,
                stored_posts=stored_posts,
                accumulated_posts=accumulated_posts,
                max_posts=10
            )

            # Extraer los posts del resultado
            current_posts = current_posts_result.get('new_posts', [])
            limit_found = current_posts_result.get('limit_found', False)
            total_processed = current_posts_result.get('total_processed', 0)

            print(f"   📊 [REFACTORIZADO] Resultado: {len(current_posts)} posts nuevos, límite: {limit_found}, procesados: {total_processed}")

            # COMENTADO: Análisis manual guiado (era muy lento)
            # current_posts = manual_guided_analysis(page, accumulated_posts, scroll_count)

            # ANULADA: Extracción en lote (sobreescribiría nuestra extracción 1 a 1)
            # expand_content_if_needed(page)
            # current_extraction = extract_posts_with_limit_detection(page, stored_posts, max_posts=10)

            if current_posts:
                print(f"   ✅ Posts nuevos extraídos: {len(current_posts)}")
                print(f"   📊 Total posts acumulados: {len(accumulated_posts)}")

                # Verificar si se encontró el límite
                if limit_found:
                    print(f"   🎯 ¡LÍMITE ENCONTRADO! Parando scroll inmediatamente")
                    limit_found_during_scroll = True
            else:
                print(f"   📭 No se extrajeron posts en este scroll")

            # Capturar estado
            scroll_state = capture_state(page, f"scroll_{scroll_count}")
            results['observations'].append(scroll_state)

            # Verificar si hemos alcanzado el objetivo
            current_posts = count_complete_posts(page)
            print(f"   📊 Posts completos actuales: {current_posts}")

            if current_posts >= target_posts:
                print(f"   🎉 ¡Objetivo alcanzado! {current_posts} posts completos")
                break

            # Verificar si hay más contenido para cargar
            if glimmer_count == 0:
                print(f"   ⚠️ No se detectaron glimmers - posible fin de contenido")
                # Hacer un scroll adicional para verificar
                page.evaluate("window.scrollBy(0, 1000)")
                time.sleep(2)
                new_glimmer_count = detect_glimmer_elements(page)
                if new_glimmer_count == 0:
                    print(f"   🔚 Confirmado: No hay más contenido para cargar")
                    break

            # MEDICIÓN DE TIEMPOS - Final del scroll individual
            scroll_end_time = time.time()
            scroll_duration = scroll_end_time - scroll_start_time
            scroll_times.append(scroll_duration)
            print(f"⏱️ [TIMING] Scroll {scroll_count} completado en {scroll_duration:.2f}s")

        # 5. Observación final
        print(f"\n📊 OBSERVACIÓN FINAL")
        print(f"-" * 40)
        final_state = capture_state(page, "final")
        results['observations'].append(final_state)

        # Mostrar conteos finales
        final_counts = final_state.get('counts', {})
        for key, value in final_counts.items():
            print(f"   {key}: {value}")

        # 6. NUEVA LÓGICA: Usar posts acumulados durante scroll
        print(f"\n🎯 PROCESANDO POSTS ACUMULADOS DURANTE SCROLL")
        print(f"-" * 40)

        print(f"📊 Posts acumulados durante scroll: {len(accumulated_posts)}")
        print(f"🎯 Límite encontrado durante scroll: {limit_found_during_scroll}")

        # MEDICIÓN DE TIEMPOS - Final del ciclo completo
        cycle_end_time = time.time()
        total_cycle_time = cycle_end_time - cycle_start_time

        print(f"\n⏱️ [TIMING] RESUMEN DE TIEMPOS:")
        print(f"   🕐 Tiempo total del ciclo: {total_cycle_time:.2f}s ({total_cycle_time/60:.1f} min)")
        print(f"   📜 Total de scrolls realizados: {len(scroll_times)}")
        if scroll_times:
            avg_scroll_time = sum(scroll_times) / len(scroll_times)
            min_scroll_time = min(scroll_times)
            max_scroll_time = max(scroll_times)
            print(f"   ⏱️ Tiempo promedio por scroll: {avg_scroll_time:.2f}s")
            print(f"   ⚡ Scroll más rápido: {min_scroll_time:.2f}s")
            print(f"   🐌 Scroll más lento: {max_scroll_time:.2f}s")
            print(f"   📊 Tiempos por scroll: {[f'{t:.1f}s' for t in scroll_times]}")

        # Usar posts acumulados como resultado final
        final_posts = accumulated_posts

        # Crear estructura de resultados compatible
        results['extracted_posts'] = final_posts
        results['limit_detection'] = {'found': limit_found_during_scroll, 'position': -1, 'posts_removed': 0}
        results['duplicate_filtering'] = {'duplicates_removed': 0, 'original_count': len(final_posts), 'final_count': len(final_posts)}

        # Mostrar resumen
        limit_info = results['limit_detection']
        duplicate_info = results['duplicate_filtering']

        # COMENTADA: Lógica original de extracción final
        # print(f"📚 Usando post límite definido anteriormente: {stored_posts[0]['author']}")
        # print(f"   Texto: {stored_posts[0]['text'][:100]}...")
        # extraction_result = extract_posts_with_limit_detection(page, stored_posts)
        # results.update(extraction_result['complete_structure'])
        # results['extracted_posts'] = extraction_result['final_posts']
        # results['limit_detection'] = extraction_result['limit_info']
        # results['duplicate_filtering'] = extraction_result['duplicate_info']
        # final_posts = extraction_result['final_posts']
        # limit_info = extraction_result['limit_info']
        # duplicate_info = extraction_result['duplicate_info']

        print(f"\n📊 RESUMEN DE EXTRACCIÓN:")
        print(f"   🎯 Límite encontrado: {limit_info['found']}")
        if limit_info['found']:
            print(f"   📍 Posición del límite: Post {limit_info['position'] + 1}")
            print(f"   ✂️ Posts eliminados (post-límite): {limit_info['posts_removed']}")
        print(f"   🧹 Duplicados eliminados: {duplicate_info['duplicates_removed']}")
        print(f"   ✅ Posts finales válidos: {len(final_posts)}")

        # Mostrar primeros posts finales
        for i, post in enumerate(final_posts[:3]):
            author = post.get('author', 'Sin autor')[:30]
            text = post.get('text', 'Sin contenido')[:50]
            text_length = post.get('text_length', 0)
            print(f"   Post {i+1}: {author} - {text}... (len: {text_length})")

        # 7. GUARDAR POSTS EN BASE DE DATOS
        print(f"\n💾 GUARDANDO POSTS EN BASE DE DATOS")
        print(f"-" * 40)

        # Buscar el grupo en la BD usando las nuevas utilidades
        from database.database_manager import DatabaseManager
        db_manager = DatabaseManager()

        # Usar el PostExtractor para extraer info del grupo y buscar en BD
        group_id = post_extractor.get_group_id_for_database(db_manager, user_id=1)

        if not group_id:
            # Fallback: buscar por URL directamente
            group_record = db_manager.get_group_by_url(group_url, user_id=1)
            if group_record:
                group_id = group_record.get('id')
                print(f"📊 Grupo encontrado (fallback) - ID interno BD: {group_id}")
                print(f"📊 Nombre: {group_record.get('nombre', 'Sin nombre')}")
            else:
                print(f"❌ Grupo no encontrado en BD para URL: {group_url}")
        else:
            print(f"✅ Grupo encontrado usando slug - ID interno BD: {group_id}")

        print(f"📄 Posts a guardar: {len(final_posts)}")

        if group_id and len(final_posts) > 0:
            try:
                # Inicializar PostRepository
                post_repo = PostRepository()

                # Convertir posts al formato esperado por la BD
                posts_for_db = []
                for post in final_posts:
                    db_post = {
                        'content': post.get('text', ''),
                        'author': post.get('author', ''),
                        'author_link': post.get('authorLink', ''),
                        'timestamp': post.get('timestamp', datetime.now().isoformat()),
                        'links': [post.get('link', '')] if post.get('link') else [],
                        'images': post.get('images', []),
                        'reactions': post.get('reactions', 0),
                        'comments': post.get('comments', 0),
                        'is_processed_and_sent': False
                    }
                    posts_for_db.append(db_post)

                # Guardar posts usando save_all
                save_result = post_repo.save_all(posts_for_db, group_id, user_id=1)

                print(f"📊 RESULTADO DEL GUARDADO:")
                print(f"   ✅ Posts guardados: {save_result['saved_count']}")
                print(f"   ⏭️ Posts omitidos: {save_result['skipped_count']}")
                print(f"   🔄 Duplicados: {save_result['duplicate_count']}")
                print(f"   ❌ Inválidos: {save_result['invalid_count']}")

                # Agregar resultado a los resultados generales
                results['database_save'] = save_result

            except Exception as e:
                print(f"❌ Error guardando en base de datos: {e}")
                results['database_save'] = {'error': str(e)}
        else:
            print(f"⚠️ No se puede guardar: group_id={group_id}, posts={len(final_posts)}")
            results['database_save'] = {'error': 'No group_id or no posts'}

        results['success'] = True
        results['total_scrolls'] = scroll_count
        results['final_posts_count'] = len(final_posts)

    except Exception as e:
        print(f"❌ Error durante observación: {e}")
        results['success'] = False
        results['error'] = str(e)

    # Exportar resultados
    export_results(results)
    
    return results

def capture_state(page, phase):
    """
    Captura el estado actual de la página
    """
    try:
        # Contadores básicos usando selectores conocidos
        counts = {
            'story_messages': page.locator('[data-ad-rendering-role="story_message"]').count(),
            'articles': page.locator('[role="article"]').count(),
            'post_links': page.locator('a[href*="/posts/"]').count(),
            'story_links': page.locator('a[href*="story.php"]').count(),
            'aria_links': page.locator('a[aria-label]').count(),
            'content_images': page.locator('img[src*="scontent"]').count()
        }
        
        return {
            'phase': phase,
            'timestamp': datetime.now().isoformat(),
            'url': page.url,
            'counts': counts
        }
    except Exception as e:
        return {
            'phase': phase,
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }

def is_element_truly_visible(page, selector):
    """
    Verifica si un elemento está realmente visible (no solo presente en DOM)
    """
    try:
        # JavaScript para verificar visibilidad real
        js_check = f"""
        () => {{
            const elements = document.querySelectorAll('{selector}');
            for (let element of elements) {{
                if (!element) continue;

                const style = window.getComputedStyle(element);
                const rect = element.getBoundingClientRect();

                const isVisible = (
                    style.display !== 'none' &&
                    style.visibility !== 'hidden' &&
                    style.opacity !== '0' &&
                    rect.width > 0 &&
                    rect.height > 0 &&
                    rect.top >= 0 &&
                    rect.left >= 0
                );

                if (isVisible) return true;
            }}
            return false;
        }}
        """
        return page.evaluate(js_check)
    except Exception:
        return False

def setup_close_button_monitor(page):
    """
    Configura un monitor para detectar y hacer clic en botones de cerrar visibles
    """
    try:
        monitor_js = """
        () => {
            if (window.closeButtonMonitor) {
                window.closeButtonMonitor.disconnect();
            }

            let clickTimeout = null;

            function isVisible(element) {
                if (!element) return false;

                const style = window.getComputedStyle(element);
                const rect = element.getBoundingClientRect();

                return (
                    style.display !== 'none' &&
                    style.visibility !== 'hidden' &&
                    style.opacity !== '0' &&
                    rect.width > 0 &&
                    rect.height > 0 &&
                    rect.top >= 0 &&
                    rect.left >= 0
                );
            }

            const observer = new MutationObserver(() => {
                const closeButtons = document.querySelectorAll('[aria-label="Cerrar"], [aria-label*="close"], [aria-label*="Close"], [aria-label*="cerrar"], [data-dismiss], [data-close], [data-action="close"]');

                let visibleButton = null;
                for (let button of closeButtons) {
                    if (isVisible(button)) {
                        visibleButton = button;
                        break;
                    }
                }

                if (visibleButton) {
                    if (clickTimeout) {
                        clearTimeout(clickTimeout);
                        console.log('Cancelando click anterior...');
                    }

                    console.log('Botón de cerrar VISIBLE detectado! Haciendo click en 3 segundos...');

                    clickTimeout = setTimeout(() => {
                        if (isVisible(visibleButton)) {
                            console.log('Haciendo click en el botón de cerrar...');
                            visibleButton.click();
                        } else {
                            console.log('Botón ya no está visible, cancelando click...');
                        }
                        clickTimeout = null;
                    }, 3000);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            window.closeButtonMonitor = observer;
            return 'Monitor de botón cerrar activado';
        }
        """
        result = page.evaluate(monitor_js)
        print(f"   🔍 {result}")
        return True
    except Exception as e:
        print(f"   ❌ Error configurando monitor: {e}")
        return False

def detect_and_close_overlays(page):
    """
    Detecta y cierra overlays/modales que bloquean la vista
    MEJORADO: Verificación de visibilidad real + monitor de botón cerrar
    """
    try:
        # Detectores de vista individual/overlay
        overlay_indicators = [
            '[role="dialog"]',
            '[aria-modal="true"]',
            '[data-pagelet*="PermalinkPost"]',
            '.x1n2onr6',
            '[data-testid="modal-backdrop"]'
        ]

        # Verificar URL para detectar vista individual PRIMERO
        current_url = page.url
        url_has_overlay = any(pattern in current_url for pattern in ['/photo/', '/posts/', '/watch/', '/permalink/'])

        # Verificar si hay overlay REALMENTE VISIBLE
        overlay_detected = False
        visible_overlays = 0

        for selector in overlay_indicators:
            try:
                if is_element_truly_visible(page, selector):
                    overlay_detected = True
                    visible_overlays += 1
            except Exception:
                continue

        if overlay_detected or url_has_overlay:
            print(f"   🔄 Overlay VISIBLE detectado - iniciando cierre...")
            print(f"      👁️ Overlays visibles: {visible_overlays}")
            print(f"      🔗 URL con overlay: {url_has_overlay}")

            # Configurar monitor de botón cerrar como fallback
            setup_close_button_monitor(page)

            # CIERRE AGRESIVO: Múltiples intentos de Escape
            for attempt in range(3):
                page.keyboard.press("Escape")
                time.sleep(0.5)  # Pausa entre intentos

                # Verificar si se cerró (visibilidad real)
                still_visible = False
                for selector in overlay_indicators:
                    try:
                        if is_element_truly_visible(page, selector):
                            still_visible = True
                            break
                    except Exception:
                        continue

                if not still_visible:
                    print(f"   ✅ Overlay cerrado en intento {attempt + 1}")
                    return True
                else:
                    print(f"   🔄 Intento {attempt + 1} - Overlay aún visible")

            # Si después de 3 intentos sigue visible (optimizado: 5s → 2s)
            print(f"   ⚠️ Overlay persistente - monitor activo por 2s...")
            time.sleep(2)  # Tiempo optimizado para el monitor

            # Verificación final
            final_check = False
            for selector in overlay_indicators:
                if is_element_truly_visible(page, selector):
                    final_check = True
                    break

            if final_check:
                print(f"   ❌ Overlay aún visible - continuando con scroll")
                return False
            else:
                print(f"   ✅ Overlay cerrado por monitor")
                return True
        else:
            return False

    except Exception as e:
        print(f"   ❌ Error detectando overlays: {e}")
        return False

def detect_glimmer_elements(page):
    """
    Detecta elementos glimmer (placeholders de carga) usando selectores del script original
    """
    glimmer_selectors = [
        '[style*="--glimmer-stagger-time"]',
        '[class*="glimmer"]',
        '[data-testid*="glimmer"]',
        '.placeholder',
        '[aria-label*="Loading"]',
        '[aria-label*="Cargando"]'
    ]

    total_glimmers = 0
    for selector in glimmer_selectors:
        try:
            count = page.locator(selector).count()
            total_glimmers += count
        except Exception:
            continue

    return total_glimmers

def expand_content_if_needed(page):
    """
    Expande contenido truncado usando lógica del script original
    MEJORADO: Solo expande "Ver más" que estén cerca de posts/artículos
    """
    try:
        # Selectores para contenedores de posts/artículos
        post_containers = [
            '[data-pagelet*="FeedUnit"]',
            '[data-testid*="story"]',
            '[role="article"]',
            '[data-testid="story-subtitle"]',
            '.story_body_container',
            '[data-ad-preview="message"]'
        ]

        expanded_count = 0

        # Buscar contenedores de posts primero
        for container_selector in post_containers:
            try:
                containers = page.locator(container_selector).all()

                for container in containers[:5]:  # Máximo 5 contenedores por vez
                    try:
                        if not container.is_visible():
                            continue

                        # Buscar "Ver más" DENTRO del contenedor del post
                        expand_selectors_in_post = [
                            '[aria-label*="Ver más"]',
                            '[aria-label*="See more"]',
                            '[role="button"]:has-text("Ver más")',
                            '[role="button"]:has-text("See more")',
                            'div[role="button"]:has-text("más")',
                            'div[role="button"]:has-text("more")'
                        ]

                        for expand_selector in expand_selectors_in_post:
                            try:
                                # Buscar botones de "Ver más" DENTRO del contenedor
                                buttons_in_container = container.locator(expand_selector).all()

                                for button in buttons_in_container[:1]:  # Solo 1 por contenedor
                                    try:
                                        if button.is_visible():
                                            # Verificar que el botón esté realmente dentro del viewport
                                            bbox = button.bounding_box()
                                            if bbox and bbox['y'] > 0 and bbox['y'] < page.viewport_size['height']:
                                                button.click()
                                                expanded_count += 1
                                                time.sleep(0.5)  # Pausa entre clicks
                                                print(f"   📖 Expandido contenido en post")
                                                break  # Solo un "Ver más" por contenedor
                                    except Exception:
                                        continue

                                if expanded_count > 0:
                                    break  # Ya expandimos algo en este contenedor

                            except Exception:
                                continue

                    except Exception:
                        continue

            except Exception:
                continue

        if expanded_count > 0:
            print(f"   📖 Total expandido: {expanded_count} contenidos de posts")
            return True
        return False

    except Exception as e:
        print(f"   ❌ Error expandiendo contenido: {e}")
        return False

def wait_for_glimmers_to_disappear(page, max_wait_seconds=10):
    """
    Espera a que los elementos glimmer desaparezcan usando lógica de DeepWiki
    """
    glimmer_selectors = [
        '[style*="--glimmer-stagger-time"]',
        '[class*="glimmer"]',
        '[data-testid*="glimmer"]',
        '.placeholder',
        '[aria-label*="Loading"]',
        '[aria-label*="Cargando"]'
    ]

    start_time = time.time()

    for selector in glimmer_selectors:
        try:
            # Esperar a que el elemento esté oculto (método recomendado por DeepWiki)
            page.wait_for_selector(selector, state='hidden', timeout=3000)
        except Exception:
            # Si no existe o ya está oculto, continuar
            continue

    # Espera adicional para estabilización
    elapsed = time.time() - start_time
    if elapsed < 1.0:  # Si fue muy rápido, esperar un poco más
        time.sleep(1.0 - elapsed)

def wait_for_posts_completion(page, max_wait_seconds=5):
    """
    Espera a que los posts se completen usando lógica del script original
    Con fallback aleatorio TOTAL de 15-25 segundos (incluyendo tiempo normal)
    """
    start_time = time.time()

    # Generar timeout TOTAL aleatorio optimizado (reducido de 10-15s a 3-6s)
    total_timeout = random.randint(3, 6)
    print(f"   ⏰ Timeout TOTAL configurado: {total_timeout}s")

    # Fase 1: Espera normal (hasta max_wait_seconds o hasta que desaparezcan glimmers)
    while time.time() - start_time < max_wait_seconds:
        # Expandir contenido si es necesario
        expand_content_if_needed(page)

        # Verificar si hay glimmers activos
        glimmer_count = detect_glimmer_elements(page)
        if glimmer_count == 0:
            elapsed = time.time() - start_time
            print(f"   ✅ Glimmers desaparecieron en {elapsed:.1f}s")
            return count_complete_posts(page)

        time.sleep(0.5)

    # Fase 2: Si aún hay glimmers, continuar hasta el timeout TOTAL
    elapsed_so_far = time.time() - start_time
    remaining_time = total_timeout - elapsed_so_far

    if remaining_time > 0:
        glimmer_count = detect_glimmer_elements(page)
        if glimmer_count > 0:
            print(f"   ⚠️ Glimmers persistentes: {glimmer_count}")
            print(f"   🔄 Continuando espera por {remaining_time:.1f}s más...")

            # Continuar esperando hasta el timeout total
            while time.time() - start_time < total_timeout:
                glimmer_count = detect_glimmer_elements(page)
                if glimmer_count == 0:
                    elapsed = time.time() - start_time
                    print(f"   ✅ Glimmers desaparecieron en {elapsed:.1f}s total")
                    return count_complete_posts(page)
                time.sleep(1.0)  # Verificar cada segundo

    # Timeout alcanzado
    final_glimmer_count = detect_glimmer_elements(page)
    total_elapsed = time.time() - start_time
    print(f"   ⏰ Timeout alcanzado ({total_elapsed:.1f}s)")
    print(f"   🚀 Continuando con siguiente scroll (glimmers restantes: {final_glimmer_count})")

    return count_complete_posts(page)

def count_complete_posts(page):
    """
    Cuenta posts que parecen estar completamente cargados
    """
    try:
        # Usar selector principal de posts
        story_elements = page.locator('[data-ad-rendering-role="story_message"]').all()

        complete_posts = 0
        for story in story_elements[:15]:  # Verificar máximo 15 posts
            try:
                # Verificar que tenga autor
                has_author = (
                    story.locator('b > span, strong > span, h3 a').count() > 0 or
                    story.locator('a[aria-label], svg[aria-label]').count() > 0
                )

                # Verificar que tenga contenido
                has_content = (
                    story.locator('div[data-ad-preview], [data-testid*="post_message"]').count() > 0 or
                    story.locator('img, video').count() > 0
                )

                if has_author and has_content:
                    complete_posts += 1

            except Exception as e:
                continue

        # Si tenemos al menos 1 post completo, continuar
        return complete_posts

    except Exception as e:
        print(f"   ❌ Error contando posts: {e}")
        return 0

def is_nested_story(story_element):
    """
    Verifica si un story_message está anidado dentro de otro (es hijo)
    """
    try:
        # Buscar ancestros que sean story_message
        # Si tiene ancestros story_message, es un post anidado (hijo)
        parent_stories = story_element.locator('xpath=ancestor::*[@data-ad-rendering-role="story_message"]')
        is_nested = parent_stories.count() > 0

        if is_nested:
            print(f"   🔍 Historia anidada detectada (tiene {parent_stories.count()} ancestros)")

        return is_nested
    except Exception as e:
        print(f"   ⚠️ Error verificando anidamiento: {e}")
        return False

def load_fake_authors_list():
    """
    Carga la lista de autores falsos desde archivo de configuración
    """
    try:
        import json
        from pathlib import Path

        config_file = Path(__file__).parent / "config" / "fake_authors.json"

        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('fake_authors', [])
        else:
            print(f"⚠️ Archivo de configuración no encontrado: {config_file}")
            # Fallback a lista hardcodeada
            return [
                "Enviar mensaje de WhatsApp",
                "Send WhatsApp message",
                "Ver más", "See more",
                "Ampliar", "Expand",
                "Mostrar más", "Show more",
                "Leer más", "Read more"
            ]
    except Exception as e:
        print(f"⚠️ Error cargando lista de autores falsos: {e}")
        # Fallback a lista básica
        return ["Enviar mensaje de WhatsApp", "Ver más", "Ampliar"]

def is_fake_author(author_name):
    """
    Detecta autores falsos comunes en posts compartidos/anidados
    Carga la lista desde archivo de configuración
    """
    fake_authors = load_fake_authors_list()
    is_fake = author_name.strip() in fake_authors

    if is_fake:
        print(f"   🚫 Autor falso detectado: '{author_name}' (de lista de {len(fake_authors)} elementos)")

    return is_fake

def extract_parent_post_with_nested_content(parent_story_element, index, page):
    """
    Extrae un post padre incluyendo el contenido de historias anidadas
    Filtra autores falsos y maneja contenido compartido
    """
    try:
        # Extraer datos básicos del post padre
        post_data = extract_complete_post_data(parent_story_element, index, page)
        if not post_data:
            return None

        # FILTRO: Verificar si el autor es falso
        if is_fake_author(post_data['author']):
            print(f"   🚫 Autor falso detectado: '{post_data['author']}' - Saltando post")
            return None

        # Buscar historias anidadas dentro del post padre
        nested_stories = parent_story_element.locator('[data-ad-rendering-role="story_message"]').all()

        if len(nested_stories) > 0:
            print(f"   📎 Post padre con {len(nested_stories)} historias anidadas")

            # Combinar texto del padre con contenido anidado
            parent_text = post_data['text']
            combined_text = parent_text

            for i, nested_story in enumerate(nested_stories):
                try:
                    nested_text = extract_post_text(nested_story)
                    if nested_text and len(nested_text.strip()) > 10:
                        # Agregar contenido anidado al texto principal
                        combined_text += f" [Compartido {i+1}: {nested_text.strip()[:200]}]"
                        print(f"     📄 Contenido anidado {i+1}: {nested_text[:50]}...")
                except Exception as e:
                    print(f"     ⚠️ Error extrayendo contenido anidado {i+1}: {e}")
                    continue

            # Actualizar el post con contenido combinado
            post_data['text'] = combined_text
            post_data['text_length'] = len(combined_text)
            post_data['has_nested_content'] = True
            post_data['nested_stories_count'] = len(nested_stories)
        else:
            post_data['has_nested_content'] = False
            post_data['nested_stories_count'] = 0

        return post_data

    except Exception as e:
        print(f"   ❌ Error extrayendo post padre con contenido anidado: {e}")
        return None

def manual_guided_analysis(page, accumulated_posts, scroll_count):
    """
    Análisis manual guiado para trackear comportamiento real del usuario
    - Trackea mouse, scroll, tiempo entre acciones
    - Extrae: autor, texto, link, imágenes (con "Ver más")
    - Espera confirmación del usuario (Enter) para continuar
    - Analiza viewport y calcula tiempos reales
    """
    import time
    import json
    from datetime import datetime

    print(f"\n🎯 [ANÁLISIS MANUAL] Scroll {scroll_count} - Análisis guiado iniciado")
    print(f"📋 [INSTRUCCIONES] Presiona Enter cuando hayas revisado los posts y quieras continuar")

    analysis_start = time.time()
    session_data = {
        'scroll_number': scroll_count,
        'start_time': datetime.now().isoformat(),
        'actions': [],
        'posts_analysis': [],
        'viewport_info': {},
        'timing_data': {}
    }

    # PASO 1: Analizar viewport inicial
    print(f"\n📊 [VIEWPORT] Analizando posts en viewport...")

    try:
        # Obtener todos los posts
        all_story_elements = page.locator('[data-ad-rendering-role="story_message"]').all()
        parent_story_elements = []
        for story in all_story_elements:
            if not is_nested_story(story):
                parent_story_elements.append(story)

        total_posts = len(parent_story_elements)
        visible_posts = 0

        # Analizar cuáles están visibles
        for i, story_element in enumerate(parent_story_elements):
            try:
                is_visible = story_element.is_visible()
                if is_visible:
                    visible_posts += 1
            except:
                pass

        session_data['viewport_info'] = {
            'total_posts_found': total_posts,
            'visible_posts': visible_posts,
            'posts_outside_viewport': total_posts - visible_posts
        }

        print(f"   📊 Posts totales encontrados: {total_posts}")
        print(f"   👁️ Posts visibles en viewport: {visible_posts}")
        print(f"   📤 Posts fuera del viewport: {total_posts - visible_posts}")

    except Exception as e:
        print(f"   ❌ Error analizando viewport: {e}")

    # PASO 2: Extraer datos de posts visibles
    print(f"\n🔍 [EXTRACCIÓN] Analizando posts visibles...")

    extracted_posts = []
    for i, story_element in enumerate(parent_story_elements[:10]):  # Analizar primeros 10
        try:
            post_start_time = time.time()
            print(f"\n   🔧 [POST {i+1}] Analizando post {i+1}...")

            # Verificar si tiene "Ver más"
            see_more_buttons = story_element.locator('div[role="button"]').all()
            has_see_more = False

            for btn in see_more_buttons:
                try:
                    btn_text = btn.text_content().lower()
                    if 'ver más' in btn_text or 'see more' in btn_text:
                        has_see_more = True
                        print(f"      🔍 Detectado 'Ver más' - expandiendo automáticamente...")
                        if btn.is_visible():
                            btn.click()
                            time.sleep(1)
                            print(f"      ✅ Expandido exitosamente")
                        break
                except:
                    continue

            # Extraer datos usando funciones existentes
            post_data = {
                'post_number': i + 1,
                'has_see_more': has_see_more,
                'extraction_time': time.time() - post_start_time
            }

            # Extraer autor (con más tiempo y múltiples intentos)
            try:
                print(f"      🔍 Extrayendo autor...")
                time.sleep(0.5)  # Pausa para asegurar carga

                # Múltiples intentos para extraer autor
                author_data = None
                for attempt in range(3):
                    try:
                        author_data = extract_author_data(story_element)
                        if author_data and author_data != "Autor desconocido":
                            break
                        time.sleep(0.3)  # Pausa entre intentos
                    except:
                        continue

                if isinstance(author_data, tuple):
                    post_data['author'] = author_data[0]
                elif author_data:
                    post_data['author'] = author_data
                else:
                    post_data['author'] = "No extraído"

            except Exception as e:
                print(f"      ⚠️ Error extrayendo autor: {e}")
                post_data['author'] = "Error en extracción"

            # Extraer texto
            try:
                post_data['text'] = extract_post_text(story_element)[:200] + "..." if len(extract_post_text(story_element)) > 200 else extract_post_text(story_element)
            except:
                post_data['text'] = "No extraído"

            # Extraer link
            try:
                post_data['link'] = extract_post_link(story_element)
            except:
                post_data['link'] = "No extraído"

            # Extraer imágenes
            try:
                images = extract_post_images(story_element)
                post_data['images'] = len(images)
                post_data['image_urls'] = images[:3]  # Primeras 3 URLs
            except:
                post_data['images'] = 0
                post_data['image_urls'] = []

            # Mostrar resumen del post
            print(f"      👤 Autor: {post_data['author']}")
            print(f"      📝 Texto: {post_data['text'][:100]}...")
            print(f"      🔗 Link: {post_data['link']}")
            print(f"      🖼️ Imágenes: {post_data['images']}")
            print(f"      ⏱️ Tiempo extracción: {post_data['extraction_time']:.2f}s")

            extracted_posts.append(post_data)
            session_data['posts_analysis'].append(post_data)

        except Exception as e:
            print(f"      ❌ Error en post {i+1}: {e}")

    # PASO 3: Esperar confirmación del usuario
    print(f"\n⏸️ [PAUSA] Análisis completado. Revisa los datos extraídos.")
    print(f"📋 [ACCIÓN] Presiona Enter cuando quieras hacer scroll y continuar...")

    wait_start = time.time()
    input()  # Esperar Enter del usuario
    wait_time = time.time() - wait_start

    session_data['timing_data'] = {
        'total_analysis_time': time.time() - analysis_start,
        'user_review_time': wait_time,
        'extraction_time': sum([p.get('extraction_time', 0) for p in extracted_posts])
    }

    print(f"✅ [TIMING] Tiempo total análisis: {session_data['timing_data']['total_analysis_time']:.2f}s")
    print(f"⏱️ [TIMING] Tiempo revisión usuario: {session_data['timing_data']['user_review_time']:.2f}s")
    print(f"🔧 [TIMING] Tiempo extracción: {session_data['timing_data']['extraction_time']:.2f}s")

    # Guardar datos de sesión
    try:
        session_file = f"flow/analysis_session_scroll_{scroll_count}_{datetime.now().strftime('%H%M%S')}.json"
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
        print(f"💾 [GUARDADO] Datos de sesión: {session_file}")
    except Exception as e:
        print(f"⚠️ [WARNING] No se pudo guardar sesión: {e}")

    # Retornar en formato compatible
    return {
        'new_posts': extracted_posts,
        'limit_found': False
    }


def test_extract(page, accumulated_posts, max_posts=10):
    """
    Función de test rápida para extraer posts con tiempos fijos:
    - Scroll normal: 5 segundos
    - Si hay "Ver más": 5+2 = 7 segundos
    - Imprime nombre y post capturado
    """
    import time

    print(f"🧪 [TEST] Iniciando extracción rápida...")

    # Usar selector principal de posts
    all_story_elements = page.locator('[data-ad-rendering-role="story_message"]').all()

    # FILTRAR: Solo posts padre (no anidados)
    parent_story_elements = []
    for story in all_story_elements:
        if not is_nested_story(story):
            parent_story_elements.append(story)

    new_posts = []
    print(f"   📊 Posts padre encontrados: {len(parent_story_elements)}")

    # EXTRACCIÓN RÁPIDA CON TIEMPOS FIJOS
    for i, story_element in enumerate(parent_story_elements[:max_posts]):
        try:
            start_time = time.time()
            print(f"   🔧 [TEST] Procesando post {i+1}/{min(len(parent_story_elements), max_posts)}...")

            # PASO 1: Verificar si el post tiene "Ver más"
            see_more_buttons = story_element.locator('div[role="button"]').all()
            has_see_more = False

            for btn in see_more_buttons:
                try:
                    btn_text = btn.text_content().lower()
                    if 'ver más' in btn_text or 'see more' in btn_text or 'ver mas' in btn_text:
                        has_see_more = True
                        print(f"   🚀 [TEST] Post {i+1} tiene 'Ver más' - expandiendo...")

                        # EXPANDIR
                        if btn.is_visible():
                            btn.click()
                            time.sleep(1)  # Esperar expansión
                            print(f"   ✅ [TEST] Post {i+1} expandido")
                        break
                except Exception:
                    continue

            if not has_see_more:
                print(f"   📝 [TEST] Post {i+1} no necesita expansión")

            # PASO 2: Extraer datos básicos
            try:
                # Extraer autor con selectores mejorados
                author = ""
                author_selectors = [
                    'strong a[role="link"]',  # Selector más específico
                    'h3 a[role="link"]',      # Alternativo
                    'a[aria-label]',          # Con aria-label
                    'strong span',            # Fallback
                    'h3 span',               # Otro fallback
                    '[data-hovercard-user-id] span'  # Específico de Facebook
                ]

                for selector in author_selectors:
                    try:
                        author_elem = story_element.locator(selector).first
                        if author_elem.count() > 0:
                            author = author_elem.text_content(timeout=300)
                            if author and len(author.strip()) > 2:  # Mínimo 3 caracteres
                                author = author.strip()
                                break
                    except:
                        continue

                # Si no encuentra autor, intentar extraer de aria-label
                if not author:
                    try:
                        aria_links = story_element.locator('a[aria-label]').all()
                        for link in aria_links:
                            aria_text = link.get_attribute('aria-label')
                            if aria_text and len(aria_text) > 2:
                                author = aria_text.strip()
                                break
                    except:
                        pass

                # Extraer texto
                text_content = story_element.text_content() or ""

                if author and text_content:
                    post_data = {
                        'author': author.strip(),
                        'text': text_content.strip()[:500],  # Primeros 500 caracteres
                        'timestamp': time.time()
                    }

                    # Verificar duplicados simples
                    is_duplicate = False
                    for existing_post in accumulated_posts:
                        if (existing_post.get('author') == post_data['author'] and
                            existing_post.get('text', '')[:100] == post_data['text'][:100]):
                            is_duplicate = True
                            break

                    if not is_duplicate:
                        accumulated_posts.append(post_data)
                        new_posts.append(post_data)

                        # IMPRIMIR RESULTADO
                        elapsed = time.time() - start_time
                        target_time = 7 if has_see_more else 5
                        print(f"   ✅ [TEST] Post {i+1} capturado en {elapsed:.1f}s (objetivo: {target_time}s)")
                        print(f"       👤 Autor: {author}")
                        print(f"       📝 Texto: {text_content[:100]}...")
                    else:
                        print(f"   🔄 [TEST] Post {i+1} duplicado - saltando")
                else:
                    print(f"   ❌ [TEST] Post {i+1} sin datos válidos")

            except Exception as e:
                print(f"   ❌ [TEST] Error en post {i+1}: {e}")

            # ESPERAR TIEMPO FIJO
            elapsed = time.time() - start_time
            target_time = 7 if has_see_more else 5
            remaining = target_time - elapsed

            if remaining > 0:
                print(f"   ⏳ [TEST] Esperando {remaining:.1f}s más para completar {target_time}s...")
                time.sleep(remaining)

        except Exception as e:
            print(f"   ❌ [TEST] Error general en post {i+1}: {e}")
            continue

    print(f"🧪 [TEST] Extracción completada: {len(new_posts)} posts nuevos")

    return {
        'new_posts': new_posts,
        'limit_found': False
    }


def extract_posts_one_by_one_with_expansion(page, stored_posts, accumulated_posts, max_posts=10):
    """
    Extrae posts uno por uno con expansión individual de "Ver más"
    Evita sobreescribir la extracción 1 a 1 con extracción en lote
    """
    try:
        print(f"🚀 [EDUARDO] Iniciando extracción 1 a 1 con expansión individual...")
        print(f"🔧 [EDUARDO] Función: extract_posts_one_by_one_with_expansion()")

        # Importar funciones de comparación
        from proto_post_comparison import we_found_the_limit_post

        # Usar selector principal de posts
        all_story_elements = page.locator('[data-ad-rendering-role="story_message"]').all()

        # FILTRAR: Solo posts padre (no anidados)
        parent_story_elements = []
        for story in all_story_elements:
            if not is_nested_story(story):
                parent_story_elements.append(story)

        new_posts = []
        limit_found = False
        consecutive_duplicates = 0  # Contador para parada temprana

        print(f"   📊 Total story_message encontrados: {len(all_story_elements)}")
        print(f"   📊 Posts padre (no anidados): {len(parent_story_elements)}")

        # EXTRACCIÓN 1 A 1 CON EXPANSIÓN INDIVIDUAL
        for i, story_element in enumerate(parent_story_elements[:max_posts]):
            try:
                print(f"   🔧 [EDUARDO] Procesando post {i+1}/{min(len(parent_story_elements), max_posts)}...")

                # PASO 1: Verificar si el post tiene "Ver más"
                see_more_buttons = story_element.locator('div[role="button"]').all()
                has_see_more = False

                for btn in see_more_buttons:
                    try:
                        btn_text = btn.text_content().lower()
                        if 'ver más' in btn_text or 'see more' in btn_text or 'ver mas' in btn_text:
                            has_see_more = True
                            print(f"   🚀 [EDUARDO] Post {i+1} tiene 'Ver más' - expandiendo...")

                            # PASO 2: Expandir individualmente
                            if btn.is_visible():
                                btn.click()
                                time.sleep(0.8)  # Esperar expansión
                                print(f"   ✅ [EDUARDO] Post {i+1} expandido exitosamente")
                            break
                    except Exception:
                        continue

                if not has_see_more:
                    print(f"   📝 [EDUARDO] Post {i+1} no necesita expansión")

                # PASO 3: VERIFICACIÓN RÁPIDA DE DUPLICADOS (antes del timeout de 5s)
                # Solo verificar si hay posts acumulados para comparar
                if len(accumulated_posts) > 0:
                    try:
                        print(f"   🔍 [DEBUG] Verificando duplicado rápido para post {i+1} (hay {len(accumulated_posts)} posts acumulados)")

                        # Extraer autor rápidamente
                        quick_author = ""
                        author_selectors = ['strong a', 'h3 a', 'a[role="link"]', 'strong span']

                        for selector in author_selectors:
                            try:
                                author_elem = story_element.locator(selector).first
                                quick_author = author_elem.text_content(timeout=200)
                                if quick_author and len(quick_author.strip()) > 0:
                                    break
                            except:
                                continue

                        # Extraer texto rápidamente
                        quick_text = ""
                        try:
                            full_text = story_element.text_content(timeout=200)
                            quick_text = full_text[:100] if full_text else ""
                        except:
                            pass

                        print(f"   🔍 [DEBUG] Datos rápidos - Autor: '{quick_author[:20]}...', Texto: '{quick_text[:30]}...'")

                        # Verificar duplicados con datos rápidos
                        is_duplicate_quick = False
                        if quick_author and quick_text:
                            for existing_post in accumulated_posts:
                                existing_author = existing_post.get('author', '')
                                existing_text = existing_post.get('text', '')[:100]

                                if (quick_author == existing_author and quick_text == existing_text):
                                    print(f"   🔄 [OPTIMIZADO] Post {i+1} duplicado detectado rápidamente - saltando")
                                    is_duplicate_quick = True
                                    break

                        if is_duplicate_quick:
                            consecutive_duplicates += 1
                            print(f"   📊 [OPTIMIZADO] Duplicados consecutivos: {consecutive_duplicates}")

                            # Parada temprana si hay muchos duplicados consecutivos
                            if consecutive_duplicates >= 5:
                                print(f"   🛑 [OPTIMIZADO] Parando extracción: {consecutive_duplicates} duplicados consecutivos")
                                break

                            continue  # Saltar al siguiente post del bucle principal

                        print(f"   ✅ [DEBUG] Post {i+1} no es duplicado, procediendo con extracción completa")

                    except Exception as e:
                        print(f"   ⚠️ [DEBUG] Verificación rápida falló: {e}")
                        pass
                else:
                    print(f"   🔍 [DEBUG] No hay posts acumulados, extrayendo post {i+1} normalmente")

                # PASO 4: Extraer contenido completo (solo si no es duplicado)
                post_data = extract_parent_post_with_nested_content(story_element, i, page)
                if not post_data:
                    print(f"   ❌ Post {i+1}: No se pudo extraer")
                    continue

                # PASO 5: Verificación final de duplicados (por seguridad)
                is_duplicate = False
                for existing_post in accumulated_posts:
                    if (post_data.get('author') == existing_post.get('author') and
                        post_data.get('text', '')[:100] == existing_post.get('text', '')[:100]):
                        is_duplicate = True
                        print(f"   🔄 Post {i+1} duplicado - saltando")
                        break

                if is_duplicate:
                    consecutive_duplicates += 1
                    print(f"   📊 [FINAL] Duplicados consecutivos: {consecutive_duplicates}")

                    # Parada temprana si hay muchos duplicados consecutivos
                    if consecutive_duplicates >= 5:
                        print(f"   🛑 [FINAL] Parando extracción: {consecutive_duplicates} duplicados consecutivos")
                        break

                    continue

                # PASO 6: Verificar límite ANTES de agregar
                if we_found_the_limit_post(
                    content=post_data['text'],
                    author=post_data['author'],
                    stored_posts=stored_posts,
                    threshold=0.8
                ):
                    limit_found = True
                    print(f"   🎯 LÍMITE ENCONTRADO en post {i+1}: {post_data['author']}")
                    print(f"   🛑 PARANDO extracción 1 a 1")
                    break  # PARAR inmediatamente

                # PASO 7: Agregar a listas (accumulated_posts y new_posts)
                accumulated_posts.append(post_data)
                new_posts.append(post_data)
                consecutive_duplicates = 0  # Reset contador al encontrar post nuevo
                print(f"   ✅ Post {i+1} agregado: {post_data['author'][:30]}...")

            except Exception as e:
                print(f"   ❌ Error en post {i+1}: {e}")
                continue

        print(f"📊 Extracción 1 a 1 completada: {len(new_posts)} posts nuevos")

        return {
            'new_posts': new_posts,
            'limit_found': limit_found,
            'total_processed': min(len(parent_story_elements), max_posts)
        }

    except Exception as e:
        print(f"❌ Error en extracción 1 a 1: {e}")
        return {
            'new_posts': [],
            'limit_found': False,
            'total_processed': 0
        }

def extract_posts_with_limit_detection(page, stored_posts, max_posts=20):
    """
    Extrae posts con detección de límite y filtrado de duplicados
    Implementa la lógica completa: extracción → límite → limpieza → filtrado
    """
    try:
        print(f"🎯 Iniciando extracción con detección de límite...")

        # Importar funciones de comparación
        from proto_post_comparison import we_found_the_limit_post, check_if_there_are_repeated_post_in_this_group_round

        # Usar selector principal de posts
        all_story_elements = page.locator('[data-ad-rendering-role="story_message"]').all()

        # FILTRAR: Solo posts padre (no historias anidadas)
        parent_story_elements = []
        for story in all_story_elements:
            if not is_nested_story(story):
                parent_story_elements.append(story)
            else:
                print(f"   ⏭️ Saltando historia anidada (es hija)")

        extracted_posts = []
        limit_found = False
        limit_position = -1

        print(f"   📊 Total story_message encontrados: {len(all_story_elements)}")
        print(f"   📊 Posts padre (no anidados): {len(parent_story_elements)}")

        # FASE 1: Extracción con detección de límite (solo posts padre)
        for i, story_element in enumerate(parent_story_elements[:max_posts]):
            try:
                # Usar función que incluye contenido anidado
                post_data = extract_parent_post_with_nested_content(story_element, i, page)
                if not post_data:
                    continue

                # FILTRO ADICIONAL: Verificar autor falso por si acaso
                if is_fake_author(post_data['author']):
                    print(f"   🚫 Filtro adicional: Autor falso '{post_data['author']}' - Saltando")
                    continue

                # VERIFICAR LÍMITE antes de agregar
                if we_found_the_limit_post(
                    content=post_data['text'],
                    author=post_data['author'],
                    stored_posts=stored_posts,
                    threshold=0.8
                ):
                    limit_found = True
                    limit_position = i
                    print(f"   🎯 LÍMITE ENCONTRADO en post {i+1}: {post_data['author']}")
                    print(f"   🛑 PARANDO extracción - No agregar más posts")
                    break  # PARAR inmediatamente

                # Solo agregar si NO es límite
                extracted_posts.append(post_data)
                print(f"   ✅ Post {i+1} agregado: {post_data['author'][:30]}...")

            except Exception as e:
                print(f"   ❌ Error en post {i+1}: {e}")
                continue

        # FASE 2: Manejo de edge case (posts más viejos que límite)
        posts_removed = 0
        if limit_found and limit_position > 0:
            # Si encontramos límite después del primer post, hay posts más nuevos válidos
            print(f"   ✅ Límite encontrado en posición {limit_position}, posts válidos: {len(extracted_posts)}")
        elif limit_found and limit_position == 0:
            # Límite encontrado inmediatamente - sin posts nuevos
            extracted_posts = []
            print(f"   📭 Sin posts nuevos - Límite encontrado inmediatamente")

        # FASE 3: Filtrado de duplicados en la ronda actual
        print(f"   🧹 Filtrando duplicados en {len(extracted_posts)} posts...")
        if len(extracted_posts) > 0:
            unique_posts = check_if_there_are_repeated_post_in_this_group_round(
                posts=extracted_posts,
                threshold=0.8
            )
            duplicates_removed = len(extracted_posts) - len(unique_posts)
        else:
            unique_posts = []
            duplicates_removed = 0

        # FASE 4: Generar estructura completa
        complete_structure = {
            "content_extraction": {
                "complete_posts": {
                    "posts": unique_posts,
                    "total_count": len(unique_posts),
                    "extraction_timestamp": datetime.now().isoformat(),
                    "limit_detection_applied": True,
                    "duplicate_filtering_applied": True
                }
            }
        }

        # Información de límite
        limit_info = {
            'found': limit_found,
            'position': limit_position,
            'posts_removed': posts_removed
        }

        # Información de duplicados
        duplicate_info = {
            'duplicates_removed': duplicates_removed,
            'original_count': len(extracted_posts),
            'final_count': len(unique_posts)
        }

        print(f"📊 Extracción completada:")
        print(f"   - Posts extraídos inicialmente: {len(extracted_posts)}")
        print(f"   - Límite encontrado: {limit_found}")
        print(f"   - Duplicados eliminados: {duplicates_removed}")
        print(f"   - Posts finales válidos: {len(unique_posts)}")

        return {
            'complete_structure': complete_structure,
            'final_posts': unique_posts,
            'limit_info': limit_info,
            'duplicate_info': duplicate_info
        }

    except Exception as e:
        print(f"❌ Error en extracción con límite: {e}")
        return {
            'complete_structure': {
                "content_extraction": {
                    "complete_posts": {
                        "posts": [],
                        "total_count": 0,
                        "extraction_timestamp": datetime.now().isoformat(),
                        "error": str(e)
                    }
                }
            },
            'final_posts': [],
            'limit_info': {'found': False, 'position': -1, 'posts_removed': 0},
            'duplicate_info': {'duplicates_removed': 0, 'original_count': 0, 'final_count': 0}
        }

def extract_posts_with_complete_structure(page):
    """
    Extrae posts con estructura completa incluyendo todos los metadatos
    Recreación de la función perdida basada en la lógica del JavaScript
    """
    try:
        print(f"🎯 Iniciando extracción con estructura completa...")

        # Usar selector principal de posts
        story_elements = page.locator('[data-ad-rendering-role="story_message"]').all()

        complete_posts = []

        for i, story_element in enumerate(story_elements[:20]):  # Máximo 20 posts
            try:
                post_data = extract_complete_post_data(story_element, i, page)
                if post_data:
                    complete_posts.append(post_data)
                    print(f"   ✅ Post {i+1}: {post_data['author'][:30]}... (len: {post_data['text_length']})")
                else:
                    print(f"   ❌ Post {i+1}: No se pudo extraer")

            except Exception as e:
                print(f"   ❌ Error en post {i+1}: {e}")
                continue

        print(f"📊 Extracción completada: {len(complete_posts)} posts válidos")

        # Retornar en la estructura esperada por proto_post_comparison.py
        return {
            "content_extraction": {
                "complete_posts": {
                    "posts": complete_posts,
                    "total_count": len(complete_posts),
                    "extraction_timestamp": datetime.now().isoformat()
                }
            }
        }

    except Exception as e:
        print(f"❌ Error en extracción general: {e}")
        return {
            "content_extraction": {
                "complete_posts": {
                    "posts": [],
                    "total_count": 0,
                    "extraction_timestamp": datetime.now().isoformat(),
                    "error": str(e)
                }
            }
        }

def extract_posts_with_known_methods(page):
    """
    Extrae posts usando métodos conocidos del facebook-scraper-v2.1.js
    Wrapper para mantener compatibilidad - usa la función completa
    """
    result = extract_posts_with_complete_structure(page)
    return result["content_extraction"]["complete_posts"]["posts"]

def extract_complete_post_data(story_element, index, page):
    """
    Extrae datos completos de un post usando la lógica del JavaScript facebook-scraper-v2.1.js
    Adaptado para Playwright con espera de elementos
    """
    try:
        # Obtener el contenedor padre del post
        post_container = story_element
        current_element = story_element

        # Buscar el contenedor padre más apropiado
        for _ in range(5):  # Máximo 5 niveles hacia arriba
            try:
                parent = current_element.locator('xpath=..').first
                if parent.count() > 0:
                    # Verificar si este nivel tiene más información
                    if (parent.locator('a[aria-label]').count() > 0 or
                        parent.locator('a[href*="/posts/"]').count() > 0):
                        post_container = parent
                current_element = parent
            except:
                break

        # ESPERAR A QUE EL POST ESTÉ COMPLETAMENTE CARGADO (optimizado: 5000ms → 2000ms)
        print(f"   ⏳ Esperando que el post {index+1} esté completamente cargado...")
        post_ready = wait_for_post_completion(page, post_container, max_wait_ms=2000)

        if not post_ready['complete']:
            print(f"   ⚠️ Post {index+1} no se completó en {post_ready['waited_ms']}ms")
        else:
            print(f"   ✅ Post {index+1} completado en {post_ready['waited_ms']}ms")

        # Extraer link del post usando patrones del JavaScript
        post_link = extract_post_link(post_container)

        # Extraer autor usando estrategias múltiples del JavaScript
        author_name, author_link = extract_author_data(post_container)

        # Extraer contenido del post
        text_content = extract_post_text(story_element)

        # Extraer metadatos adicionales
        images = extract_post_images(post_container)
        videos = extract_post_videos(post_container)
        reactions = extract_post_reactions(post_container)
        comments = extract_post_comments(post_container)
        shares = extract_post_shares(post_container)

        # Crear estructura completa como la del JavaScript
        post_data = {
            'index': index,
            'id': generate_post_id(post_container, author_name, text_content),
            'author': author_name,
            'authorLink': author_link,
            'text': text_content,
            'text_length': len(text_content) if text_content else 0,
            'link': post_link,
            'has_content': bool(text_content and len(text_content.strip()) > 0),
            'container_found': True,
            'timestamp': datetime.now().isoformat(),
            'images': images,
            'videos': videos,
            'reactions': reactions,
            'comments': comments,
            'shares': shares
        }

        return post_data

    except Exception as e:
        print(f"   ❌ Error extrayendo post completo {index}: {e}")
        return None

def extract_single_post(story_element, index):
    """
    Extrae datos de un solo post usando estrategias del script original
    """
    try:
        post_data = {
            'index': index,
            'author': 'Sin autor',
            'content': 'Sin contenido',
            'timestamp': datetime.now().isoformat()
        }

        # Estrategia 1: Buscar autor en elementos conocidos
        author_selectors = [
            'b > span',
            'strong > span',
            'h3 a',
            'a[aria-label]',
            'svg[aria-label]'
        ]

        for selector in author_selectors:
            try:
                author_element = story_element.locator(selector).first
                if author_element.count() > 0:
                    author_text = author_element.text_content()
                    if author_text and len(author_text.strip()) > 0:
                        post_data['author'] = author_text.strip()[:100]
                        break
            except Exception:
                continue

        # Estrategia 2: Buscar contenido en elementos conocidos
        content_selectors = [
            'div[data-ad-preview]',
            '[data-testid*="post_message"]',
            'div[dir="auto"]',
            'span[dir="auto"]'
        ]

        for selector in content_selectors:
            try:
                content_elements = story_element.locator(selector).all()
                for content_element in content_elements[:3]:  # Máximo 3 elementos
                    content_text = content_element.text_content()
                    if content_text and len(content_text.strip()) > 10:
                        post_data['content'] = content_text.strip()[:500]
                        break
                if post_data['content'] != 'Sin contenido':
                    break
            except Exception:
                continue

        # Estrategia 3: Buscar imágenes
        try:
            images = story_element.locator('img[src*="scontent"]').all()
            if len(images) > 0:
                post_data['has_images'] = True
                post_data['image_count'] = len(images)
        except Exception:
            pass

        # Estrategia 4: Usar locator("..")  para subir un nivel (método permitido en Playwright)
        # Subir niveles hasta encontrar contenedor completo
        current_element = story_element
        for level in range(15):  # Hasta 15 niveles como en script original
            parent_container = current_element.locator("..")
            if parent_container.count() == 0:
                break

            # Verificar si este contenedor tiene elementos completos de un post
            has_author = (
                parent_container.locator('b > span, strong > span, h3 a, [role="link"]').count() > 0 or
                parent_container.locator('a[aria-label], svg[aria-label], [title]').count() > 0
            )

            has_content = (
                parent_container.locator('div[data-ad-preview], [data-testid*="post_message"]').count() > 0 or
                parent_container.locator('img[src*="scontent"], video').count() > 0
            )

            if has_author and has_content:
                # Este nivel tiene información más completa
                try:
                    # Intentar extraer autor más completo
                    if post_data['author'] == 'Sin autor':
                        author_element = parent_container.locator('b > span, strong > span, h3 a').first
                        if author_element.count() > 0:
                            author_text = author_element.text_content()
                            if author_text:
                                post_data['author'] = author_text.strip()[:100]

                    # Intentar extraer contenido más completo
                    if post_data['content'] == 'Sin contenido':
                        content_element = parent_container.locator('div[data-ad-preview], [data-testid*="post_message"]').first
                        if content_element.count() > 0:
                            content_text = content_element.text_content()
                            if content_text and len(content_text.strip()) > 10:
                                post_data['content'] = content_text.strip()[:500]
                except Exception:
                    pass

                break  # Encontramos un buen nivel, salir del loop

            current_element = parent_container

        # Validar que tenemos datos mínimos
        if post_data['author'] != 'Sin autor' or post_data['content'] != 'Sin contenido':
            return post_data
        else:
            return None

    except Exception as e:
        print(f"   ❌ Error extrayendo post individual: {e}")
        return None

def extract_post_link(post_container):
    """
    Extrae el link del post usando patrones del JavaScript
    """
    try:
        # Obtener HTML del contenedor para buscar patrones
        html_content = post_container.inner_html()

        # Patrones de links del JavaScript
        link_patterns = [
            r'href="([^"]*\/posts\/[^"]*)"',
            r'href="([^"]*story\.php[^"]*)"',
            r'href="([^"]*permalink[^"]*)"',
            r'href="([^"]*fbid[^"]*)"',
            r'href="([^"]*\/photo\/[^"]*)"',
            r'href="([^"]*\/video\/[^"]*)"'
        ]

        import re
        for pattern in link_patterns:
            match = re.search(pattern, html_content)
            if match:
                post_link = match.group(1)

                # Normalizar el link del post para que sea clickeable
                if post_link.startswith('/'):
                    post_link = f"https://www.facebook.com{post_link}"
                elif not post_link.startswith('http'):
                    post_link = f"https://www.facebook.com/{post_link}"

                return post_link

        return ''
    except Exception as e:
        return ''

def extract_author_data(post_container):
    """
    Extrae datos del autor usando estrategias múltiples del JavaScript
    """
    try:
        author_name = 'Autor desconocido'
        author_link = ''

        # Estrategia 1: aria-label en enlaces (del JavaScript)
        try:
            link_with_aria = post_container.locator('a[aria-label]').first
            if link_with_aria.count() > 0:
                aria_label = link_with_aria.get_attribute('aria-label')
                if aria_label and len(aria_label) > 2 and 'http' not in aria_label:
                    author_name = aria_label
                    author_link = link_with_aria.get_attribute('href') or ''
        except:
            pass

        # Estrategia 2: SVG con aria-label
        if author_name == 'Autor desconocido':
            try:
                svg_with_aria = post_container.locator('svg[aria-label]').first
                if svg_with_aria.count() > 0:
                    aria_label = svg_with_aria.get_attribute('aria-label')
                    if aria_label and len(aria_label) > 2 and 'http' not in aria_label:
                        author_name = aria_label
            except:
                pass

        # Estrategia 3: Atributo title
        if author_name == 'Autor desconocido':
            try:
                element_with_title = post_container.locator('[title]').first
                if element_with_title.count() > 0:
                    title = element_with_title.get_attribute('title')
                    if title and len(title) > 2 and 'http' not in title:
                        author_name = title
            except:
                pass

        # Estrategia 4: Selectores tradicionales como fallback
        if author_name == 'Autor desconocido':
            try:
                fallback_selectors = [
                    'b > span', 'strong > span', 'h3 a',
                    '[data-ad-rendering-role="profile_name"] a',
                    'h3 span', '[role="link"]', 'strong a'
                ]

                for selector in fallback_selectors:
                    element = post_container.locator(selector).first
                    if element.count() > 0:
                        text = element.text_content()
                        if text and len(text.strip()) > 2:
                            author_name = text.strip()
                            break
            except:
                pass

        # Buscar link del autor si no lo tenemos
        if not author_link:
            try:
                author_link_selectors = [
                    'a[href*="/user/"]', 'a[href*="/profile.php"]', 'a[aria-label]'
                ]
                for selector in author_link_selectors:
                    element = post_container.locator(selector).first
                    if element.count() > 0:
                        author_link = element.get_attribute('href') or ''
                        break
            except:
                pass

        # Normalizar el link del autor para que sea clickeable
        if author_link:
            # Si es un link relativo, agregar el dominio de Facebook
            if author_link.startswith('/'):
                author_link = f"https://www.facebook.com{author_link}"
            # Si no tiene protocolo, agregarlo
            elif not author_link.startswith('http'):
                author_link = f"https://www.facebook.com/{author_link}"

        return author_name, author_link

    except Exception as e:
        return 'Autor desconocido', ''

def extract_post_text(story_element):
    """
    Extrae el texto del post
    """
    try:
        text_content = story_element.text_content()
        return text_content.strip() if text_content else ''
    except Exception as e:
        return ''

def generate_post_id(post_container, author_name, text_content):
    """
    Genera ID único para el post basado en la lógica del JavaScript
    """
    try:
        # Usar contenido estable para generar ID
        content_parts = [
            author_name or 'unknown',
            text_content[:100] if text_content else 'no_text',
            'post_id'
        ]

        content = '|'.join(content_parts)

        # Hash simple como en el JavaScript
        hash_value = 0
        if content:
            for char in content:
                hash_value = ((hash_value << 5) - hash_value) + ord(char)
                hash_value = hash_value & 0xFFFFFFFF  # 32bit integer

        return str(abs(hash_value))

    except Exception as e:
        return f"post_{datetime.now().timestamp()}"

def wait_for_post_completion(page, post_container, max_wait_ms=2000):
    """
    Espera a que un post esté completamente cargado usando page.evaluate()
    Basado en la lógica observePostUntilComplete del JavaScript
    """
    try:
        # Obtener un selector único para el elemento
        try:
            # Intentar obtener un atributo único del elemento
            data_testid = post_container.get_attribute('data-testid')
            if data_testid:
                selector = f'[data-testid="{data_testid}"]'
            else:
                # Usar el índice del elemento como fallback
                all_stories = page.locator('[data-ad-rendering-role="story_message"]').all()
                element_index = -1
                for i, story in enumerate(all_stories):
                    if story == post_container:
                        element_index = i
                        break

                if element_index >= 0:
                    selector = f'[data-ad-rendering-role="story_message"]:nth-child({element_index + 1})'
                else:
                    selector = '[data-ad-rendering-role="story_message"]'
        except:
            selector = '[data-ad-rendering-role="story_message"]'

        # Usar page.evaluate para ejecutar la lógica de espera en el navegador
        result = page.evaluate("""
            (params) => {
                const selector = params.selector;
                const maxWaitMs = params.maxWaitMs;
                const postElement = document.querySelector(selector);

                if (!postElement) {
                    return {
                        complete: false,
                        waited_ms: 0,
                        elements_found: {},
                        timeout: true,
                        error: 'Element not found'
                    };
                }
                return new Promise((resolve) => {
                    const start = performance.now();
                    const requiredElements = {
                        author: false,
                        content: false,
                        interactions: false,
                        media: false
                    };

                    let checkInterval;
                    let mutationObserver;

                    const checkCompleteness = () => {
                        const elapsed = performance.now() - start;

                        // Verificar autor usando múltiples estrategias
                        let authorFound = false;

                        // Estrategia 1: aria-label en enlaces
                        const linkWithAriaLabel = postElement.querySelector('a[aria-label]');
                        if (linkWithAriaLabel) {
                            const ariaLabel = linkWithAriaLabel.getAttribute('aria-label');
                            if (ariaLabel && ariaLabel.length > 2 && !ariaLabel.includes('http')) {
                                authorFound = true;
                            }
                        }

                        // Estrategia 2: SVG con aria-label
                        if (!authorFound) {
                            const svgWithAriaLabel = postElement.querySelector('svg[aria-label]');
                            if (svgWithAriaLabel) {
                                const ariaLabel = svgWithAriaLabel.getAttribute('aria-label');
                                if (ariaLabel && ariaLabel.length > 2 && !ariaLabel.includes('http')) {
                                    authorFound = true;
                                }
                            }
                        }

                        // Estrategia 3: Selectores tradicionales
                        if (!authorFound) {
                            const fallbackAuthor = postElement.querySelector('b > span, strong > span, h3 a');
                            if (fallbackAuthor && fallbackAuthor.textContent.trim().length > 2) {
                                authorFound = true;
                            }
                        }

                        requiredElements.author = authorFound;

                        // Verificar contenido
                        const contentElement = postElement.querySelector('[data-ad-preview], [data-testid*="post_message"]');
                        requiredElements.content = contentElement && contentElement.textContent.trim().length > 0;

                        // Verificar si hay interacciones visibles
                        const interactionElements = postElement.querySelectorAll('span[dir="auto"]');
                        requiredElements.interactions = interactionElements.length > 0;

                        // Verificar media
                        const mediaElements = postElement.querySelectorAll('img[src*="scontent"], video');
                        requiredElements.media = mediaElements.length > 0;

                        // Considerar completo si tenemos autor y contenido
                        const isComplete = requiredElements.author && requiredElements.content;

                        if (isComplete || elapsed > maxWaitMs) {
                            clearInterval(checkInterval);
                            if (mutationObserver) mutationObserver.disconnect();

                            resolve({
                                complete: isComplete,
                                waited_ms: Math.round(elapsed),
                                elements_found: requiredElements,
                                timeout: elapsed > maxWaitMs
                            });
                        }
                    };

                    // Observer para detectar cambios en el post
                    mutationObserver = new MutationObserver(() => {
                        checkCompleteness();
                    });

                    mutationObserver.observe(postElement, {
                        childList: true,
                        subtree: true,
                        attributes: true
                    });

                    // Verificación periódica adicional
                    checkInterval = setInterval(checkCompleteness, 200);

                    // Verificación inicial
                    checkCompleteness();
                });
            }
        """, {
            'selector': selector,
            'maxWaitMs': max_wait_ms
        })

        return result

    except Exception as e:
        print(f"   ❌ Error esperando completitud del post: {e}")
        return {
            'complete': False,
            'waited_ms': 0,
            'elements_found': {},
            'timeout': True,
            'error': str(e)
        }

def extract_post_images(post_container):
    """
    Extrae imágenes del post basado en la lógica del JavaScript
    """
    try:
        images = []
        img_elements = post_container.locator('img[src*="scontent"]').all()

        for img in img_elements:
            try:
                src = img.get_attribute('src')
                alt = img.get_attribute('alt') or ''

                if src and 'emoji' not in src:
                    images.append({
                        'src': src,
                        'alt': alt
                    })
            except:
                continue

        return images
    except Exception as e:
        return []

def extract_post_videos(post_container):
    """
    Extrae videos del post basado en la lógica del JavaScript
    """
    try:
        videos = []
        video_elements = post_container.locator('video').all()

        for video in video_elements:
            try:
                src = video.get_attribute('src')
                poster = video.get_attribute('poster') or ''

                if src:
                    videos.append({
                        'src': src,
                        'poster': poster
                    })
            except:
                continue

        return videos
    except Exception as e:
        return []

def extract_post_reactions(post_container):
    """
    Extrae reacciones del post
    """
    try:
        # Buscar texto de reacciones
        reaction_texts = ['reacciones', 'reactions', 'Me gusta', 'like', 'likes']

        for text in reaction_texts:
            try:
                elements = post_container.locator(f'span[dir="auto"]:has-text("{text}")').all()
                for element in elements:
                    content = element.text_content()
                    if content:
                        import re
                        match = re.search(r'(\d+)', content)
                        if match:
                            return int(match.group(1))
            except:
                continue

        return 0
    except Exception as e:
        return 0

def extract_post_comments(post_container):
    """
    Extrae número de comentarios del post
    """
    try:
        comment_texts = ['comentarios', 'comments', 'comentario', 'comment']

        for text in comment_texts:
            try:
                elements = post_container.locator(f'span:has-text("{text}")').all()
                for element in elements:
                    content = element.text_content()
                    if content:
                        import re
                        match = re.search(r'(\d+)', content)
                        if match:
                            return int(match.group(1))
            except:
                continue

        return 0
    except Exception as e:
        return 0

def extract_post_shares(post_container):
    """
    Extrae número de compartidos del post
    """
    try:
        share_texts = ['compartidos', 'shares', 'compartido', 'share']

        for text in share_texts:
            try:
                elements = post_container.locator(f'span:has-text("{text}")').all()
                for element in elements:
                    content = element.text_content()
                    if content:
                        import re
                        match = re.search(r'(\d+)', content)
                        if match:
                            return int(match.group(1))
            except:
                continue

        return 0
    except Exception as e:
        return 0

def export_results(results):
    """
    Exporta resultados a archivo JSON
    """
    try:
        # Crear directorio de resultados
        results_dir = Path("results/observations")
        results_dir.mkdir(parents=True, exist_ok=True)

        # Generar nombre de archivo con timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"group_observation_{timestamp}.json"
        filepath = results_dir / filename

        # Exportar con formato legible
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"\n💾 RESULTADOS EXPORTADOS")
        print(f"📁 Archivo: {filepath}")

        # Mostrar resumen
        print(f"\n📋 RESUMEN:")
        if 'observations' in results:
            for obs in results['observations']:
                phase = obs.get('phase', 'unknown')
                counts = obs.get('counts', {})
                story_count = counts.get('story_messages', 0)
                article_count = counts.get('articles', 0)
                print(f"   {phase}: {story_count} stories, {article_count} articles")

    except Exception as e:
        print(f"❌ Error exportando resultados: {e}")

if __name__ == "__main__":
    print("🔍 Este script debe ser importado y usado con una página autenticada")
    print("💡 Usa: from proto_simple_observer import observe_group_with_page")
