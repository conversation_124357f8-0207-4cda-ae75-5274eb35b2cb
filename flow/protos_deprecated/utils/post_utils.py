# Testing purposes currently being evalueted
"""
Utilidades para posts - funciones de comparación, validación y procesamiento de texto
Incluye calculate_jaccard y funciones relacionadas con posts
"""

import re
from typing import Optional, List, Dict, Any, Set


def calculate_jaccard(text1: str, text2: str, threshold: float = 0.8) -> float:
    """
    Calcula la similitud Jaccard entre dos textos.
    
    Args:
        text1: Primer texto a comparar
        text2: Segundo texto a comparar  
        threshold: Umbral de similitud (configurable, por defecto 0.8)
        
    Returns:
        float: Coeficiente de Jaccard (0.0 a 1.0)
    """
    # Normalizar textos: minúsculas, sin puntuación extra, espacios múltiples
    def normalize_text(text: str) -> str:
        # Convertir a minúsculas
        text = text.lower()
        # Remover caracteres especiales pero mantener espacios y letras
        text = re.sub(r'[^\w\s]', ' ', text)
        # Normalizar espacios múltiples
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    # Normalizar ambos textos
    normalized_text1 = normalize_text(text1)
    normalized_text2 = normalize_text(text2)
    
    # Manejar casos extremos
    if not normalized_text1 and not normalized_text2:
        return 1.0  # Ambos vacíos = idénticos
    
    if not normalized_text1 or not normalized_text2:
        return 0.0  # Uno vacío, otro no = completamente diferentes
    
    # Crear conjuntos de palabras
    words1 = set(normalized_text1.split())
    words2 = set(normalized_text2.split())
    
    # Calcular intersección y unión
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    # Evitar división por cero
    if len(union) == 0:
        return 0.0
    
    # Calcular coeficiente de Jaccard
    jaccard_coefficient = len(intersection) / len(union)
    
    return jaccard_coefficient


def we_found_the_limit_post(content: str, author: Optional[str] = None, 
                           stored_posts: List[Dict[str, Any]] = None,
                           threshold: float = 0.8) -> bool:
    """
    Verifica si hemos encontrado el post límite (último post obtenido previamente).
    
    Args:
        content: Contenido del post actual
        author: Autor del post (opcional para mayor precisión)
        stored_posts: Lista de posts almacenados en la base de datos
        threshold: Umbral de similitud Jaccard (por defecto 0.8)
        
    Returns:
        bool: True si encontramos el post límite, False en caso contrario
    """
    if not stored_posts:
        return False
    
    # Buscar el post más reciente (índice 0 según la especificación)
    if len(stored_posts) == 0:
        return False
    
    # El post más reciente debería estar en el índice 0
    most_recent_post = stored_posts[0]
    stored_content = most_recent_post.get('content', '') or most_recent_post.get('text', '')
    stored_author = most_recent_post.get('author', '')
    
    # Calcular similitud de contenido
    content_similarity = calculate_jaccard(content, stored_content, threshold)
    
    # Si tenemos autor, verificar también similitud de autor
    if author and stored_author:
        author_similarity = calculate_jaccard(author, stored_author, threshold)
        # Requerir alta similitud tanto en contenido como en autor
        return content_similarity >= threshold and author_similarity >= 0.9
    else:
        # Solo verificar contenido si no tenemos información de autor
        return content_similarity >= threshold


def validate_post_content(content: str, min_words: int = 5) -> bool:
    """
    Valida que el contenido del post cumpla con los requisitos mínimos.
    
    Args:
        content: Contenido del post a validar
        min_words: Número mínimo de palabras requeridas (por defecto 5)
        
    Returns:
        bool: True si el post es válido, False en caso contrario
    """
    if not content or not content.strip():
        return False
    
    # Contar palabras (separadas por espacios)
    words = content.strip().split()
    return len(words) >= min_words


def check_if_there_are_repeated_posts_in_group_round(posts: List[Dict[str, Any]], 
                                                   threshold: float = 0.8) -> Dict[str, Any]:
    """
    Verifica duplicados dentro de una ronda de posts del mismo grupo.
    
    Args:
        posts: Lista de posts a verificar
        threshold: Umbral de similitud para considerar duplicados (por defecto 0.8)
        
    Returns:
        Dict con posts únicos y estadísticas de duplicados
    """
    if not posts:
        return {
            'unique_posts': [],
            'discarded_posts': [],
            'stats': {
                'total_input': 0,
                'unique_count': 0,
                'duplicates_count': 0,
                'threshold_used': threshold
            }
        }
    
    unique_posts: List[Dict[str, Any]] = []
    discarded_posts: List[Dict[str, Any]] = []
    
    for i, current_post in enumerate(posts):
        current_content = current_post.get('content', '') or current_post.get('text', '')
        current_author = current_post.get('author', '')
        
        is_duplicate = False
        
        # Comparar con posts ya aceptados como únicos
        for unique_post in unique_posts:
            unique_content = unique_post.get('content', '') or unique_post.get('text', '')
            unique_author = unique_post.get('author', '')
            
            # Calcular similitud de contenido
            content_similarity = calculate_jaccard(current_content, unique_content, threshold)
            
            # Si la similitud supera el umbral, es un duplicado
            if content_similarity >= threshold:
                is_duplicate = True
                discarded_posts.append({
                    'original_index': i,
                    'post': current_post,
                    'similar_to_index': unique_post.get('index', 'unknown'),
                    'similarity_score': content_similarity,
                    'reason': 'content_similarity'
                })
                break
        
        # Si no es duplicado, agregarlo a únicos
        if not is_duplicate:
            # Agregar índice para referencia
            current_post['index'] = i
            unique_posts.append(current_post)
    
    return {
        'unique_posts': unique_posts,
        'discarded_posts': discarded_posts,
        'stats': {
            'total_input': len(posts),
            'unique_count': len(unique_posts),
            'duplicates_count': len(discarded_posts),
            'threshold_used': threshold
        }
    }


def normalize_post_data(post: Dict[str, Any]) -> Dict[str, Any]:
    """
    Normaliza los datos de un post para asegurar consistencia.
    
    Args:
        post: Diccionario con datos del post
        
    Returns:
        Dict: Post normalizado
    """
    # Mapear campos alternativos a campos estándar
    content = post.get('content') or post.get('text', '')
    author = post.get('author', '')
    
    # Limpiar contenido
    if content:
        content = content.strip()
    
    if author:
        author = author.strip()
    
    # Crear post normalizado
    normalized_post = {
        'content': content,
        'author': author,
        'timestamp': post.get('timestamp'),
        'links': post.get('links', []),
        'images': post.get('images', []),
        'reactions': post.get('reactions', 0),
        'comments': post.get('comments', 0),
        'is_processed_and_sent': post.get('is_processed_and_sent', False),
        # Mantener campos adicionales
        **{k: v for k, v in post.items() if k not in ['content', 'text', 'author']}
    }
    
    return normalized_post
