# Testing purposes currently being evalueted
"""
Post Comparison - Funciones formales para comparación y detección de duplicados en posts
Clase refactorizada desde proto_post_comparison.py
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple


class PostComparison:
    """
    Clase para manejar comparación de posts y detección de duplicados
    """
    
    def __init__(self, default_threshold: float = 0.8):
        """
        Inicializa el comparador de posts
        
        Args:
            default_threshold: Umbral por defecto para similitud Jaccard
        """
        self.default_threshold = default_threshold
    
    def calculate_jaccard(self, text1: str, text2: str, threshold: float = None) -> float:
        """
        Calcula la similitud Jaccard entre dos textos.
        
        Args:
            text1: Primer texto a comparar
            text2: Segundo texto a comparar  
            threshold: Umbral de similitud (usa default si no se especifica)
            
        Returns:
            float: Coeficiente de Jaccard (0.0 a 1.0)
        """
        if threshold is None:
            threshold = self.default_threshold
            
        # Normalizar textos: minúsculas, sin puntuación extra, espacios múltiples
        def normalize_text(text: str) -> str:
            # Convertir a minúsculas
            text = text.lower()
            # Remover caracteres especiales pero mantener espacios y letras
            text = re.sub(r'[^\w\s]', ' ', text)
            # Normalizar espacios múltiples
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        
        # Normalizar ambos textos
        norm_text1 = normalize_text(text1)
        norm_text2 = normalize_text(text2)
        
        # Crear conjuntos de palabras
        words1 = set(norm_text1.split())
        words2 = set(norm_text2.split())
        
        # Calcular intersección y unión
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        # Evitar división por cero
        if len(union) == 0:
            return 0.0
        
        # Calcular coeficiente de Jaccard
        jaccard_coefficient = len(intersection) / len(union)
        
        return jaccard_coefficient
    
    def we_found_the_limit_post(self, content: str, author: Optional[str] = None, 
                               stored_posts: List[Dict[str, Any]] = None,
                               threshold: float = None) -> bool:
        """
        Verifica si hemos encontrado el post límite (último post obtenido previamente).
        
        Args:
            content: Contenido del post actual
            author: Autor del post (opcional para mayor precisión)
            stored_posts: Lista de posts almacenados en la base de datos
            threshold: Umbral de similitud Jaccard (usa default si no se especifica)
            
        Returns:
            bool: True si encontramos el post límite, False en caso contrario
        """
        if threshold is None:
            threshold = self.default_threshold
            
        if not stored_posts:
            return False
        
        # Buscar el post más reciente (índice 0 según la especificación)
        if len(stored_posts) == 0:
            return False
        
        # El post más reciente debería estar en el índice 0
        most_recent_post = stored_posts[0]
        stored_content = most_recent_post.get('text', '')
        stored_author = most_recent_post.get('author', '')
        
        # Calcular similitud de contenido
        content_similarity = self.calculate_jaccard(content, stored_content, threshold)
        
        # Si tenemos autor, verificar también similitud de autor
        if author and stored_author:
            author_similarity = self.calculate_jaccard(author, stored_author, threshold)
            # Requerir alta similitud tanto en contenido como en autor
            return content_similarity >= threshold and author_similarity >= 0.9
        else:
            # Solo verificar contenido si no tenemos información de autor
            return content_similarity >= threshold
    
    def check_if_there_are_repeated_post_in_this_group_round(self, posts: List[Dict[str, Any]], 
                                                            threshold: float = None) -> List[Dict[str, Any]]:
        """
        Revisa si en la ronda actual hay posts similares y los descarta.
        
        Args:
            posts: Lista de posts de la ronda actual
            threshold: Umbral de similitud Jaccard (usa default si no se especifica)
            
        Returns:
            List[Dict[str, Any]]: Lista de posts sin duplicados
        """
        if threshold is None:
            threshold = self.default_threshold
            
        if len(posts) <= 1:
            return posts
        
        # Lista para posts únicos
        unique_posts: List[Dict[str, Any]] = []
        # Lista para tracking de posts descartados
        discarded_posts = []
        
        for i, current_post in enumerate(posts):
            current_content = current_post.get('text', '')
            current_author = current_post.get('author', '')
            
            is_duplicate = False
            
            # Comparar con posts ya aceptados como únicos
            for unique_post in unique_posts:
                unique_content = unique_post.get('text', '')
                unique_author = unique_post.get('author', '')
                
                # Calcular similitud de contenido
                content_similarity = self.calculate_jaccard(current_content, unique_content, threshold)
                
                # Si la similitud supera el umbral, es un duplicado
                if content_similarity >= threshold:
                    is_duplicate = True
                    discarded_posts.append({
                        'original_index': i,
                        'post': current_post,
                        'similar_to_index': unique_post.get('index', 'unknown'),
                        'similarity_score': content_similarity,
                        'reason': 'content_similarity'
                    })
                    break
            
            # Si no es duplicado, agregarlo a la lista de únicos
            if not is_duplicate:
                unique_posts.append(current_post)
        
        # Log de resultados
        original_count = len(posts)
        final_count = len(unique_posts)
        discarded_count = len(discarded_posts)
        
        print(f"🧹 Filtrado de duplicados completado:")
        print(f"   📊 Posts originales: {original_count}")
        print(f"   ✅ Posts únicos: {final_count}")
        print(f"   🗑️ Posts descartados: {discarded_count}")
        
        if discarded_count > 0:
            print(f"   📋 Detalles de posts descartados:")
            for discarded in discarded_posts:
                similarity = discarded['similarity_score']
                print(f"      - Post {discarded['original_index']}: {similarity:.2f} similitud")
        
        return unique_posts
    
    def load_test_posts_from_observations(self) -> List[Dict[str, Any]]:
        """
        Carga posts de prueba desde los archivos de observación existentes.

        Returns:
            List[Dict[str, Any]]: Lista de posts para pruebas
        """
        observations_dir = Path("results/observations")
        test_posts: List[Dict[str, Any]] = []
        
        if not observations_dir.exists():
            print("❌ No se encontró directorio de observaciones")
            return []
        
        # Buscar el archivo de observación más reciente
        observation_files = list(observations_dir.glob("group_observation_*.json"))
        if not observation_files:
            print("❌ No se encontraron archivos de observación")
            return []
        
        # Usar el archivo más reciente
        latest_file = max(observation_files, key=lambda f: f.stat().st_mtime)
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extraer posts del archivo de observación
            content_extraction = data.get('content_extraction', {})
            complete_posts = content_extraction.get('complete_posts', {})
            posts = complete_posts.get('posts', [])
            
            print(f"📁 Cargados {len(posts)} posts desde {latest_file.name}")
            return posts
            
        except Exception as e:
            print(f"❌ Error cargando posts de prueba: {e}")
            return []


# Funciones de compatibilidad para mantener la API existente
def calculate_jaccard(text1: str, text2: str, threshold: float = 0.8) -> float:
    """Función de compatibilidad - usa la clase PostComparison"""
    comparator = PostComparison(threshold)
    return comparator.calculate_jaccard(text1, text2, threshold)


def we_found_the_limit_post(content: str, author: Optional[str] = None, 
                           stored_posts: List[Dict[str, Any]] = None,
                           threshold: float = 0.8) -> bool:
    """Función de compatibilidad - usa la clase PostComparison"""
    comparator = PostComparison(threshold)
    return comparator.we_found_the_limit_post(content, author, stored_posts, threshold)


def check_if_there_are_repeated_post_in_this_group_round(posts: List[Dict[str, Any]], 
                                                        threshold: float = 0.8) -> List[Dict[str, Any]]:
    """Función de compatibilidad - usa la clase PostComparison"""
    comparator = PostComparison(threshold)
    return comparator.check_if_there_are_repeated_post_in_this_group_round(posts, threshold)


def load_test_posts_from_observations() -> List[Dict[str, Any]]:
    """Función de compatibilidad - usa la clase PostComparison"""
    comparator = PostComparison()
    return comparator.load_test_posts_from_observations()
