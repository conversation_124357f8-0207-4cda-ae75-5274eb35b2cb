# Testing purposes currently being evalueted
"""
Post Operations - Operaciones específicas para manejo de posts
Extracción, procesamiento y verificación de duplicados de posts
Refactorizado desde proto_simple_observer.py con métodos que funcionan correctamente
"""

import json
import time
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from group_utils import extract_group_slug_from_url, extract_group_info_from_url


class PostExtractor:
    """
    Clase encargada de todas las operaciones de extracción de posts de Facebook
    Basada en los métodos que funcionan correctamente del commit 161ba7f
    """
    
    def __init__(self):
        """Inicializa el extractor de posts con configuración por defecto"""
        self.duplicate_threshold = 0.8
        self.fake_authors_cache = None
        self.current_group_info = None  # Info del grupo actual
        self.group_id_for_database = None  # ID del grupo en BD
        self.group_name = None  # Nombre del grupo

    def extract_group_info_from_page(self, page):
        """
        Extrae información del grupo desde la URL de la página actual

        Args:
            page: Página de Playwright

        Returns:
            Dict con información del grupo o None si no se puede extraer
        """
        try:
            current_url = page.url
            group_info = extract_group_info_from_url(current_url)

            if group_info['slug']:
                self.current_group_info = group_info
                print(f"📊 Grupo detectado: {group_info['slug']} (tipo: {group_info['url_type']})")
                return group_info
            else:
                print(f"⚠️ No se pudo extraer información del grupo de URL: {current_url}")
                return None

        except Exception as e:
            print(f"❌ Error extrayendo info del grupo: {e}")
            return None

    def get_group_id_for_database(self, database_manager, user_id=1):
        """
        Obtiene el ID del grupo en la base de datos usando el slug extraído

        Args:
            database_manager: Instancia de DatabaseManager
            user_id: ID del usuario (default 1)

        Returns:
            int: ID del grupo en la base de datos o None si no se encuentra
        """
        if not self.current_group_info or not self.current_group_info['slug']:
            print("⚠️ No hay información del grupo disponible")
            return None

        try:
            # Buscar por URL base del grupo
            base_url = self.current_group_info['base_url']
            group_record = database_manager.get_group_by_url(base_url, user_id)

            if group_record:
                group_id = group_record.get('id')
                group_name = group_record.get('name', f"Grupo {self.current_group_info['slug']}")

                # Establecer atributos de la clase
                self.group_id_for_database = group_id
                self.group_name = group_name

                print(f"✅ Grupo encontrado en BD: ID={group_id}, slug={self.current_group_info['slug']}")
                return group_id
            else:
                print(f"❌ Grupo no encontrado en BD para slug: {self.current_group_info['slug']}")
                self.group_id_for_database = None
                self.group_name = None
                return None

        except Exception as e:
            print(f"❌ Error buscando grupo en BD: {e}")
            return None

    def extract_posts_one_by_one_with_expansion(self, page, stored_posts, accumulated_posts, max_posts=10):
        """
        Extrae posts uno por uno con expansión individual de "Ver más"
        Método principal que funcionaba correctamente en commit 161ba7f
        
        Args:
            page: Página de Playwright
            stored_posts: Posts almacenados para detectar límite
            accumulated_posts: Lista acumulativa de posts extraídos
            max_posts: Máximo número de posts a procesar por llamada
            
        Returns:
            Dict con new_posts, limit_found y total_processed
        """
        try:
            print(f"🚀 [EXTRACTOR] Iniciando extracción 1 a 1 con expansión individual...")
            print(f"🔧 [EXTRACTOR] Función: extract_posts_one_by_one_with_expansion()")

            # Importar funciones de comparación
            from post_comparison import we_found_the_limit_post

            # Usar selector principal de posts
            all_story_elements = page.locator('[data-ad-rendering-role="story_message"]').all()

            # FILTRAR: Solo posts padre (no anidados)
            parent_story_elements = []
            for story in all_story_elements:
                if not self._is_nested_story(story):
                    parent_story_elements.append(story)

            new_posts = []
            limit_found = False
            consecutive_duplicates = 0  # Contador para parada temprana

            print(f"   📊 Total story_message encontrados: {len(all_story_elements)}")
            print(f"   📊 Posts padre (no anidados): {len(parent_story_elements)}")

            # EXTRACCIÓN 1 A 1 CON EXPANSIÓN INDIVIDUAL
            for i, story_element in enumerate(parent_story_elements[:max_posts]):
                try:
                    print(f"   🔧 [EXTRACTOR] Procesando post {i+1}/{min(len(parent_story_elements), max_posts)}...")

                    # PASO 1: Verificar si el post tiene "Ver más"
                    see_more_buttons = story_element.locator('div[role="button"]').all()
                    has_see_more = False

                    for btn in see_more_buttons:
                        try:
                            btn_text = btn.text_content().lower()
                            if 'ver más' in btn_text or 'see more' in btn_text or 'ver mas' in btn_text:
                                has_see_more = True
                                print(f"   🚀 [EXTRACTOR] Post {i+1} tiene 'Ver más' - expandiendo...")

                                # PASO 2: Expandir individualmente
                                if btn.is_visible():
                                    btn.click()
                                    time.sleep(0.8)  # Esperar expansión
                                    print(f"   ✅ [EXTRACTOR] Post {i+1} expandido exitosamente")
                                break
                        except Exception:
                            continue

                    if not has_see_more:
                        print(f"   📝 [EXTRACTOR] Post {i+1} no necesita expansión")

                    # PASO 3: Verificación rápida de duplicados (optimización)
                    if len(accumulated_posts) > 0:
                        try:
                            print(f"   🔍 [DEBUG] Verificando duplicado rápido para post {i+1}")
                            
                            # Extraer autor rápidamente
                            quick_author = self._extract_author_quick(story_element)
                            quick_text = self._extract_text_quick(story_element)
                            
                            print(f"   🔍 [DEBUG] Datos rápidos - Autor: '{quick_author[:20]}...', Texto: '{quick_text[:30]}...'")

                            # Verificar duplicados con datos rápidos
                            if self._is_duplicate_quick(quick_author, quick_text, accumulated_posts):
                                consecutive_duplicates += 1
                                print(f"   🔄 [OPTIMIZADO] Post {i+1} duplicado detectado rápidamente - saltando")
                                print(f"   📊 [OPTIMIZADO] Duplicados consecutivos: {consecutive_duplicates}")

                                # Parada temprana si hay muchos duplicados consecutivos
                                if consecutive_duplicates >= 5:
                                    print(f"   🛑 [OPTIMIZADO] Parando extracción: {consecutive_duplicates} duplicados consecutivos")
                                    break

                                continue  # Saltar al siguiente post

                            print(f"   ✅ [DEBUG] Post {i+1} no es duplicado, procediendo con extracción completa")

                        except Exception as e:
                            print(f"   ⚠️ [DEBUG] Verificación rápida falló: {e}")
                            pass
                    else:
                        print(f"   🔍 [DEBUG] No hay posts acumulados, extrayendo post {i+1} normalmente")

                    # PASO 4: Extraer contenido completo (solo si no es duplicado)
                    post_data = self.extract_parent_post_with_nested_content(story_element, i, page)
                    if not post_data:
                        print(f"   ❌ Post {i+1}: No se pudo extraer")
                        continue

                    # PASO 5: Verificación final de duplicados (por seguridad)
                    if self._is_duplicate_final(post_data, accumulated_posts):
                        consecutive_duplicates += 1
                        print(f"   🔄 Post {i+1} duplicado - saltando")
                        print(f"   📊 [FINAL] Duplicados consecutivos: {consecutive_duplicates}")

                        # Parada temprana si hay muchos duplicados consecutivos
                        if consecutive_duplicates >= 5:
                            print(f"   🛑 [FINAL] Parando extracción: {consecutive_duplicates} duplicados consecutivos")
                            break

                        continue

                    # PASO 6: Verificar límite ANTES de agregar
                    if we_found_the_limit_post(
                        content=post_data['text'],
                        author=post_data['author'],
                        stored_posts=stored_posts,
                        threshold=0.8
                    ):
                        limit_found = True
                        print(f"   🎯 LÍMITE ENCONTRADO en post {i+1}: {post_data['author']}")
                        print(f"   🛑 PARANDO extracción 1 a 1")
                        break  # PARAR inmediatamente

                    # PASO 7: Agregar a listas (accumulated_posts y new_posts)
                    accumulated_posts.append(post_data)
                    new_posts.append(post_data)
                    consecutive_duplicates = 0  # Reset contador al encontrar post nuevo
                    print(f"   ✅ Post {i+1} agregado: {post_data['author'][:30]}...")

                except Exception as e:
                    print(f"   ❌ Error en post {i+1}: {e}")
                    continue

            print(f"📊 Extracción 1 a 1 completada: {len(new_posts)} posts nuevos")

            return {
                'new_posts': new_posts,
                'limit_found': limit_found,
                'total_processed': min(len(parent_story_elements), max_posts)
            }

        except Exception as e:
            print(f"❌ Error en extracción 1 a 1: {e}")
            return {
                'new_posts': [],
                'limit_found': False,
                'total_processed': 0
            }

    def extract_parent_post_with_nested_content(self, parent_story_element, index, page):
        """
        Extrae un post padre incluyendo el contenido de historias anidadas
        Filtra autores falsos y maneja contenido compartido
        """
        try:
            # Extraer datos básicos del post padre
            post_data = self.extract_complete_post_data(parent_story_element, index, page)
            if not post_data:
                return None

            # FILTRO: Verificar si el autor es falso
            if self._is_fake_author(post_data['author']):
                print(f"   🚫 Autor falso detectado: '{post_data['author']}' - Saltando post")
                return None

            # Buscar historias anidadas dentro del post padre
            nested_stories = parent_story_element.locator('[data-ad-rendering-role="story_message"]').all()

            if len(nested_stories) > 0:
                print(f"   📎 Post padre con {len(nested_stories)} historias anidadas")

                # Combinar texto del padre con contenido anidado
                parent_text = post_data['text']
                combined_text = parent_text

                for i, nested_story in enumerate(nested_stories):
                    try:
                        nested_text = self.extract_post_text(nested_story)
                        if nested_text and len(nested_text.strip()) > 10:
                            # Agregar contenido anidado al texto principal
                            combined_text += f" [Compartido {i+1}: {nested_text.strip()[:200]}]"
                            print(f"     📄 Contenido anidado {i+1}: {nested_text[:50]}...")
                    except Exception as e:
                        print(f"     ⚠️ Error extrayendo contenido anidado {i+1}: {e}")
                        continue

                # Actualizar el post con contenido combinado
                post_data['text'] = combined_text
                post_data['text_length'] = len(combined_text)
                post_data['has_nested_content'] = True
                post_data['nested_stories_count'] = len(nested_stories)
            else:
                post_data['has_nested_content'] = False
                post_data['nested_stories_count'] = 0

            return post_data

        except Exception as e:
            print(f"   ❌ Error extrayendo post padre con contenido anidado: {e}")
            return None

    def extract_complete_post_data(self, story_element, index, page):
        """
        Extrae datos completos de un post usando la lógica del JavaScript facebook-scraper-v2.1.js
        Adaptado para Playwright con espera de elementos
        """
        try:
            # Obtener el contenedor padre del post
            post_container = story_element
            current_element = story_element

            # Buscar el contenedor padre más apropiado
            for _ in range(5):  # Máximo 5 niveles hacia arriba
                try:
                    parent = current_element.locator('xpath=..').first
                    if parent.count() > 0:
                        # Verificar si este nivel tiene más información
                        if (parent.locator('a[aria-label]').count() > 0 or
                            parent.locator('a[href*="/posts/"]').count() > 0):
                            post_container = parent
                    current_element = parent
                except:
                    break

            # ESPERAR A QUE EL POST ESTÉ COMPLETAMENTE CARGADO
            print(f"   ⏳ Esperando que el post {index+1} esté completamente cargado...")
            post_ready = self._wait_for_post_completion(page, post_container, max_wait_ms=5000)

            if not post_ready['complete']:
                print(f"   ⚠️ Post {index+1} no se completó en {post_ready['waited_ms']}ms")
            else:
                print(f"   ✅ Post {index+1} completado en {post_ready['waited_ms']}ms")

            # Extraer link del post usando patrones del JavaScript
            post_link = self.extract_post_link(post_container)

            # Extraer autor usando estrategias múltiples del JavaScript
            author_name, author_link = self.extract_author_data(post_container)

            # Extraer contenido del post
            text_content = self.extract_post_text(story_element)

            # Extraer metadatos adicionales
            images = self.extract_post_images(post_container)
            videos = self.extract_post_videos(post_container)
            reactions = self.extract_post_reactions(post_container)
            comments = self.extract_post_comments(post_container)
            shares = self.extract_post_shares(post_container)

            # Crear estructura completa como la del JavaScript
            post_data = {
                'index': index,
                'id': self._generate_post_id(post_container, author_name, text_content),
                'author': author_name,
                'authorLink': author_link,
                'text': text_content,
                'text_length': len(text_content) if text_content else 0,
                'link': post_link,
                'has_content': bool(text_content and len(text_content.strip()) > 0),
                'container_found': True,
                'timestamp': datetime.now().isoformat(),
                'images': images,
                'videos': videos,
                'reactions': reactions,
                'comments': comments,
                'shares': shares
            }

            return post_data

        except Exception as e:
            print(f"   ❌ Error extrayendo post completo {index}: {e}")
            return None

    def extract_author_data(self, post_container):
        """
        Extrae datos del autor usando estrategias múltiples del JavaScript
        """
        try:
            author_name = 'Autor desconocido'
            author_link = ''

            # Estrategia 1: aria-label en enlaces (del JavaScript)
            try:
                link_with_aria = post_container.locator('a[aria-label]').first
                if link_with_aria.count() > 0:
                    aria_label = link_with_aria.get_attribute('aria-label')
                    if aria_label and len(aria_label) > 2 and 'http' not in aria_label:
                        author_name = aria_label
                        author_link = link_with_aria.get_attribute('href') or ''
            except:
                pass

            # Estrategia 2: SVG con aria-label
            if author_name == 'Autor desconocido':
                try:
                    svg_with_aria = post_container.locator('svg[aria-label]').first
                    if svg_with_aria.count() > 0:
                        aria_label = svg_with_aria.get_attribute('aria-label')
                        if aria_label and len(aria_label) > 2 and 'http' not in aria_label:
                            author_name = aria_label
                except:
                    pass

            # Estrategia 3: Atributo title
            if author_name == 'Autor desconocido':
                try:
                    element_with_title = post_container.locator('[title]').first
                    if element_with_title.count() > 0:
                        title = element_with_title.get_attribute('title')
                        if title and len(title) > 2 and 'http' not in title:
                            author_name = title
                except:
                    pass

            # Estrategia 4: Selectores tradicionales como fallback
            if author_name == 'Autor desconocido':
                try:
                    fallback_selectors = [
                        'b > span', 'strong > span', 'h3 a',
                        '[data-ad-rendering-role="profile_name"] a',
                        'h3 span', '[role="link"]', 'strong a'
                    ]

                    for selector in fallback_selectors:
                        element = post_container.locator(selector).first
                        if element.count() > 0:
                            text = element.text_content()
                            if text and len(text.strip()) > 2:
                                author_name = text.strip()
                                break
                except:
                    pass

            # Buscar link del autor si no lo tenemos
            if not author_link:
                try:
                    author_link_selectors = [
                        'a[href*="/user/"]', 'a[href*="/profile.php"]', 'a[aria-label]'
                    ]
                    for selector in author_link_selectors:
                        element = post_container.locator(selector).first
                        if element.count() > 0:
                            author_link = element.get_attribute('href') or ''
                            break
                except:
                    pass

            # Normalizar el link del autor para que sea clickeable
            if author_link:
                # Si es un link relativo, agregar el dominio de Facebook
                if author_link.startswith('/'):
                    author_link = f"https://www.facebook.com{author_link}"
                # Si no tiene protocolo, agregarlo
                elif not author_link.startswith('http'):
                    author_link = f"https://www.facebook.com/{author_link}"

            return author_name, author_link

        except Exception as e:
            return 'Autor desconocido', ''

    def extract_post_text(self, story_element):
        """
        Extrae el texto del post
        """
        try:
            text_content = story_element.text_content()
            return text_content.strip() if text_content else ''
        except Exception as e:
            return ''

    def extract_post_link(self, post_container):
        """
        Extrae el link del post usando patrones del JavaScript
        """
        try:
            # Obtener HTML del contenedor para buscar patrones
            html_content = post_container.inner_html()

            # Patrones de links del JavaScript
            link_patterns = [
                r'href="([^"]*\/posts\/[^"]*)"',
                r'href="([^"]*story\.php[^"]*)"',
                r'href="([^"]*permalink[^"]*)"',
                r'href="([^"]*fbid[^"]*)"',
                r'href="([^"]*\/photo\/[^"]*)"',
                r'href="([^"]*\/video\/[^"]*)"'
            ]

            for pattern in link_patterns:
                match = re.search(pattern, html_content)
                if match:
                    post_link = match.group(1)

                    # Normalizar el link del post para que sea clickeable
                    if post_link.startswith('/'):
                        post_link = f"https://www.facebook.com{post_link}"
                    elif not post_link.startswith('http'):
                        post_link = f"https://www.facebook.com/{post_link}"

                    return post_link

            return ''
        except Exception as e:
            return ''

    def extract_post_images(self, post_container):
        """
        Extrae imágenes del post
        """
        try:
            images = []
            img_elements = post_container.locator('img').all()

            for img in img_elements:
                try:
                    src = img.get_attribute('src')
                    if src and 'scontent' in src:  # Filtrar imágenes de Facebook
                        images.append(src)
                except:
                    continue

            return images
        except Exception as e:
            return []

    def extract_post_videos(self, post_container):
        """
        Extrae videos del post
        """
        try:
            videos = []
            video_elements = post_container.locator('video').all()

            for video in video_elements:
                try:
                    src = video.get_attribute('src')
                    if src:
                        videos.append(src)
                except:
                    continue

            return videos
        except Exception as e:
            return []

    def extract_post_reactions(self, post_container):
        """
        Extrae reacciones del post
        """
        try:
            # Buscar elementos de reacciones
            reaction_elements = post_container.locator('[aria-label*="reacciones"]').all()
            if not reaction_elements:
                reaction_elements = post_container.locator('[aria-label*="reactions"]').all()

            reactions = 0
            for elem in reaction_elements:
                try:
                    text = elem.text_content()
                    # Extraer números del texto
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        reactions = int(numbers[0])
                        break
                except:
                    continue

            return reactions
        except Exception as e:
            return 0

    def extract_post_comments(self, post_container):
        """
        Extrae número de comentarios del post
        """
        try:
            # Buscar elementos de comentarios
            comment_elements = post_container.locator('[aria-label*="comentarios"]').all()
            if not comment_elements:
                comment_elements = post_container.locator('[aria-label*="comments"]').all()

            comments = 0
            for elem in comment_elements:
                try:
                    text = elem.text_content()
                    # Extraer números del texto
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        comments = int(numbers[0])
                        break
                except:
                    continue

            return comments
        except Exception as e:
            return 0

    def extract_post_shares(self, post_container):
        """
        Extrae número de compartidos del post
        """
        try:
            # Buscar elementos de compartidos
            share_elements = post_container.locator('[aria-label*="compartidos"]').all()
            if not share_elements:
                share_elements = post_container.locator('[aria-label*="shares"]').all()

            shares = 0
            for elem in share_elements:
                try:
                    text = elem.text_content()
                    # Extraer números del texto
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        shares = int(numbers[0])
                        break
                except:
                    continue

            return shares
        except Exception as e:
            return 0

    # Métodos auxiliares privados
    def _is_nested_story(self, story_element):
        """
        Verifica si un story_message está anidado dentro de otro (es hijo)
        """
        try:
            # Buscar ancestros que sean story_message
            # Si tiene ancestros story_message, es un post anidado (hijo)
            parent_stories = story_element.locator('xpath=ancestor::*[@data-ad-rendering-role="story_message"]')
            is_nested = parent_stories.count() > 0

            if is_nested:
                print(f"   🔍 Historia anidada detectada (tiene {parent_stories.count()} ancestros)")

            return is_nested
        except Exception as e:
            print(f"   ⚠️ Error verificando anidamiento: {e}")
            return False

    def _is_fake_author(self, author_name):
        """
        Verifica si un autor es falso (botones, enlaces, etc.)
        """
        try:
            if not author_name or author_name == 'Autor desconocido':
                return True

            # Cargar lista de autores falsos si no está en cache
            if self.fake_authors_cache is None:
                self.fake_authors_cache = self._load_fake_authors_list()

            # Verificar si el autor está en la lista de falsos
            author_lower = author_name.lower().strip()
            for fake_author in self.fake_authors_cache:
                if fake_author.lower() in author_lower or author_lower in fake_author.lower():
                    return True

            return False
        except Exception as e:
            print(f"⚠️ Error verificando autor falso: {e}")
            return False

    def _load_fake_authors_list(self):
        """
        Carga la lista de autores falsos desde archivo de configuración
        """
        try:
            config_file = Path(__file__).parent / "config" / "fake_authors.json"

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('fake_authors', [])
            else:
                print(f"⚠️ Archivo de configuración no encontrado: {config_file}")
                # Fallback a lista hardcodeada
                return [
                    "Enviar mensaje de WhatsApp",
                    "Send WhatsApp message",
                    "Ver más", "See more",
                    "Ampliar", "Expand",
                    "Mostrar más", "Show more",
                    "Leer más", "Read more"
                ]
        except Exception as e:
            print(f"⚠️ Error cargando lista de autores falsos: {e}")
            # Fallback a lista básica
            return ["Enviar mensaje de WhatsApp", "Ver más", "Ampliar"]

    def _extract_author_quick(self, story_element):
        """
        Extrae autor rápidamente para verificación de duplicados
        """
        try:
            author_selectors = ['strong a', 'h3 a', 'a[role="link"]', 'strong span']

            for selector in author_selectors:
                try:
                    author_elem = story_element.locator(selector).first
                    quick_author = author_elem.text_content(timeout=200)
                    if quick_author and len(quick_author.strip()) > 0:
                        return quick_author.strip()
                except:
                    continue
            return ""
        except:
            return ""

    def _extract_text_quick(self, story_element):
        """
        Extrae texto rápidamente para verificación de duplicados
        """
        try:
            full_text = story_element.text_content(timeout=200)
            return full_text[:100] if full_text else ""
        except:
            return ""

    def _is_duplicate_quick(self, quick_author, quick_text, accumulated_posts):
        """
        Verificación rápida de duplicados
        """
        if not quick_author or not quick_text:
            return False

        for existing_post in accumulated_posts:
            existing_author = existing_post.get('author', '')
            existing_text = existing_post.get('text', '')[:100]

            if (quick_author == existing_author and quick_text == existing_text):
                return True
        return False

    def _is_duplicate_final(self, post_data, accumulated_posts):
        """
        Verificación final de duplicados
        """
        for existing_post in accumulated_posts:
            if (post_data.get('author') == existing_post.get('author') and
                post_data.get('text', '')[:100] == existing_post.get('text', '')[:100]):
                return True
        return False

    def _wait_for_post_completion(self, page, post_container, max_wait_ms=5000):
        """
        Espera a que un post esté completamente cargado
        """
        try:
            start_time = time.time() * 1000
            waited_ms = 0

            while waited_ms < max_wait_ms:
                try:
                    # Verificar si el post tiene contenido básico
                    has_author = post_container.locator('a[aria-label], strong a, h3 a').count() > 0
                    has_text = len(post_container.text_content().strip()) > 10

                    if has_author and has_text:
                        return {'complete': True, 'waited_ms': waited_ms}

                    time.sleep(0.1)
                    waited_ms = (time.time() * 1000) - start_time

                except Exception:
                    break

            return {'complete': False, 'waited_ms': waited_ms}
        except Exception as e:
            return {'complete': False, 'waited_ms': 0}

    def _generate_post_id(self, post_container, author_name, text_content):
        """
        Genera un ID único para el post basado en su contenido
        """
        try:
            # Crear hash basado en autor y primeras palabras del texto
            content_hash = f"{author_name}_{text_content[:50] if text_content else ''}"
            # Usar timestamp para hacer único
            timestamp = str(int(time.time() * 1000))
            return f"post_{hash(content_hash)}_{timestamp}"
        except Exception as e:
            return f"post_unknown_{int(time.time() * 1000)}"

    # Métodos de verificación adicionales (para uso futuro)
    def verify_post_structure(self, post_data):
        """
        Verifica que un post tenga la estructura correcta
        """
        required_fields = ['author', 'text', 'timestamp', 'index']

        for field in required_fields:
            if field not in post_data:
                return False, f"Campo requerido faltante: {field}"

        if not post_data['author'] or post_data['author'] == 'Autor desconocido':
            return False, "Autor no válido"

        if not post_data['text'] or len(post_data['text'].strip()) < 5:
            return False, "Texto muy corto o vacío"

        return True, "Post válido"

    def check_post_quality(self, post_data):
        """
        Evalúa la calidad de un post extraído
        """
        quality_score = 0
        issues = []

        # Verificar autor
        if post_data.get('author') and post_data['author'] != 'Autor desconocido':
            quality_score += 25
        else:
            issues.append("Autor desconocido o faltante")

        # Verificar texto
        text_length = len(post_data.get('text', ''))
        if text_length > 50:
            quality_score += 25
        elif text_length > 10:
            quality_score += 15
        else:
            issues.append("Texto muy corto")

        # Verificar link
        if post_data.get('link'):
            quality_score += 20
        else:
            issues.append("Link del post faltante")

        # Verificar metadatos
        if post_data.get('authorLink'):
            quality_score += 15

        if post_data.get('images') or post_data.get('videos'):
            quality_score += 15

        return {
            'score': quality_score,
            'quality': 'Alta' if quality_score >= 80 else 'Media' if quality_score >= 60 else 'Baja',
            'issues': issues
        }

    def validate_extraction_batch(self, posts_batch):
        """
        Valida un lote de posts extraídos
        """
        if not posts_batch:
            return {'valid': False, 'reason': 'Lote vacío'}

        valid_posts = 0
        total_posts = len(posts_batch)
        quality_scores = []

        for post in posts_batch:
            is_valid, reason = self.verify_post_structure(post)
            if is_valid:
                valid_posts += 1
                quality = self.check_post_quality(post)
                quality_scores.append(quality['score'])

        success_rate = (valid_posts / total_posts) * 100
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0

        return {
            'valid': success_rate >= 70,  # Al menos 70% de posts válidos
            'success_rate': success_rate,
            'valid_posts': valid_posts,
            'total_posts': total_posts,
            'average_quality': avg_quality,
            'reason': f"Tasa de éxito: {success_rate:.1f}%, Calidad promedio: {avg_quality:.1f}"
        }


# Clase de compatibilidad para mantener la interfaz existente
class PostOperations(PostExtractor):
    """
    Clase de compatibilidad que mantiene la interfaz original
    Hereda de PostExtractor para tener todos los métodos refactorizados
    """

    def __init__(self):
        """Inicializa con compatibilidad hacia atrás"""
        super().__init__()
        self.extracted_posts = []  # Para compatibilidad con código existente

    def extract_posts_from_group(self, group_id: str, max_posts: int = 50) -> List[Dict[str, Any]]:
        """
        Método de compatibilidad para extracción de posts

        Args:
            group_id: ID del grupo
            max_posts: Número máximo de posts a extraer

        Returns:
            Lista de posts extraídos
        """
        print(f"📄 [COMPATIBILITY] Extrayendo posts del grupo: {group_id}")
        print(f"    → Método de compatibilidad - usar PostExtractor directamente para nuevas implementaciones")
        print(f"    → Buscando hasta {max_posts} posts")

        # TODO: Implementar usando los métodos refactorizados cuando se tenga acceso a la página de Playwright
        # Por ahora, devolver lista vacía para mantener compatibilidad

        self.extracted_posts = []
        return self.extracted_posts

    def get_extraction_stats(self) -> Dict[str, Any]:
        """
        Obtiene estadísticas de extracción

        Returns:
            Dict con estadísticas
        """
        return {
            "total_extracted": len(self.extracted_posts),
            "last_extraction": datetime.now().isoformat()
        }

    def process_posts_with_duplicates(self, posts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Procesa posts verificando duplicados (método de compatibilidad)

        Args:
            posts: Lista de posts a procesar

        Returns:
            Lista de posts sin duplicados
        """
        print(f"🔍 [COMPATIBILITY] Procesando {len(posts)} posts para verificar duplicados")

        unique_posts = []
        seen_posts = set()

        for post in posts:
            # Crear clave única basada en autor y texto
            post_key = f"{post.get('author', '')}_{post.get('text', '')[:100]}"

            if post_key not in seen_posts:
                seen_posts.add(post_key)
                unique_posts.append(post)
            else:
                print(f"    🔄 Post duplicado eliminado: {post.get('author', 'Sin autor')[:30]}...")

        print(f"    ✅ Procesamiento completado: {len(unique_posts)} posts únicos de {len(posts)} originales")
        return unique_posts
