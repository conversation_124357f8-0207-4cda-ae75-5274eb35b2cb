# Testing purposes currently being evalueted
"""
Post Repository - Repositorio para operaciones de posts con validación y detección de duplicados
Implementa save_all, save_post y retrieve_limit_post con lógica de negocio completa
"""

import sqlite3
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from utils.post_utils import (
    calculate_jaccard,
    validate_post_content,
    check_if_there_are_repeated_posts_in_group_round,
    normalize_post_data,
    we_found_the_limit_post
)
from utils.path_utils import get_database_str


class PostRepository:
    """
    Repositorio especializado para operaciones de posts con validación avanzada
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Inicializa el repositorio de posts
        
        Args:
            db_path: Ruta al archivo de base de datos SQLite
        """
        if db_path is None:
            db_path = get_database_str()
        
        self.db_path = str(db_path)
        self._ensure_db_exists()
    
    def _ensure_db_exists(self):
        """Asegura que la base de datos existe"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def save_all(self, posts: List[Dict[str, Any]], group_id: int, user_id: int = 1) -> Dict[str, Any]:
        """
        Guarda una lista de posts validando contenido y duplicados.
        Los posts se guardan desde el índice más alto (más viejo) al 0 (más nuevo).
        
        Args:
            posts: Lista de posts a guardar (índice 0 = más reciente)
            group_id: ID del grupo
            user_id: ID del usuario (por defecto 1)
            
        Returns:
            Dict con estadísticas del guardado
        """
        if not posts:
            return {
                'success': True,
                'saved_count': 0,
                'skipped_count': 0,
                'duplicate_count': 0,
                'invalid_count': 0,
                'details': []
            }
        
        print(f"💾 Iniciando save_all: {len(posts)} posts para grupo {group_id}")
        
        # 1. Normalizar posts
        normalized_posts = [normalize_post_data(post) for post in posts]
        
        # 2. Validar contenido mínimo (5 palabras)
        valid_posts = []
        invalid_count = 0
        
        for i, post in enumerate(normalized_posts):
            if validate_post_content(post.get('content', ''), min_words=5):
                valid_posts.append(post)
            else:
                invalid_count += 1
                print(f"   ⚠️ Post {i} descartado: contenido insuficiente")
        
        print(f"   ✅ Posts válidos: {len(valid_posts)}/{len(posts)}")
        
        # 3. Verificar duplicados dentro de la ronda
        dedup_result = check_if_there_are_repeated_posts_in_group_round(valid_posts, threshold=0.8)
        unique_posts = dedup_result['unique_posts']
        duplicate_count = dedup_result['stats']['duplicates_count']
        
        print(f"   🔍 Duplicados internos encontrados: {duplicate_count}")
        print(f"   ✅ Posts únicos para guardar: {len(unique_posts)}")
        
        # 4. Obtener posts existentes del grupo para verificar duplicados con BD
        existing_posts = self.get_recent_posts_by_group(group_id, limit=100)
        
        # 5. Guardar posts en orden: desde el más viejo al más nuevo
        # Invertir la lista para que el índice más alto se guarde primero
        posts_to_save = list(reversed(unique_posts))
        
        saved_count = 0
        skipped_count = 0
        details = []
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for i, post in enumerate(posts_to_save):
                try:
                    # Verificar duplicados contra BD existente
                    is_duplicate_in_db = self._check_duplicate_against_db(
                        post, existing_posts, threshold=0.8
                    )
                    
                    if is_duplicate_in_db:
                        skipped_count += 1
                        details.append({
                            'index': i,
                            'action': 'skipped',
                            'reason': 'duplicate_in_database',
                            'author': post.get('author', '')[:30]
                        })
                        print(f"   ⏭️ Post {i} omitido: duplicado en BD")
                        continue
                    
                    # Guardar post
                    cursor.execute("""
                        INSERT INTO posts (
                            user_id, group_id, content, author, author_link, timestamp,
                            links, images, reactions, comments, is_processed_and_sent
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id,
                        group_id,
                        post.get('content', ''),
                        post.get('author', ''),
                        post.get('author_link', ''),
                        post.get('timestamp'),
                        json.dumps(post.get('links', [])),
                        json.dumps(post.get('images', [])),
                        post.get('reactions', 0),
                        post.get('comments', 0),
                        post.get('is_processed_and_sent', False)
                    ))
                    
                    saved_count += 1
                    details.append({
                        'index': i,
                        'action': 'saved',
                        'reason': 'valid_unique_post',
                        'author': post.get('author', '')[:30]
                    })
                    print(f"   ✅ Post {i} guardado: {post.get('author', '')[:30]}")
                    
                except Exception as e:
                    skipped_count += 1
                    details.append({
                        'index': i,
                        'action': 'error',
                        'reason': f'database_error: {str(e)}',
                        'author': post.get('author', '')[:30]
                    })
                    print(f"   ❌ Error guardando post {i}: {e}")
                    continue
            
            conn.commit()
        
        result = {
            'success': True,
            'saved_count': saved_count,
            'skipped_count': skipped_count,
            'duplicate_count': duplicate_count,
            'invalid_count': invalid_count,
            'details': details
        }
        
        print(f"📊 save_all completado: {saved_count} guardados, {skipped_count} omitidos")
        return result
    
    def save_post(self, post: Dict[str, Any], group_id: int, user_id: int = 1) -> Dict[str, Any]:
        """
        Guarda un post individual con validación completa.
        
        Args:
            post: Post a guardar
            group_id: ID del grupo
            user_id: ID del usuario (por defecto 1)
            
        Returns:
            Dict con resultado del guardado
        """
        print(f"💾 Iniciando save_post para grupo {group_id}")
        
        # Usar save_all con un solo post para reutilizar toda la lógica
        result = self.save_all([post], group_id, user_id)
        
        # Adaptar resultado para un solo post
        single_result = {
            'success': result['success'],
            'saved': result['saved_count'] > 0,
            'reason': 'valid_unique_post' if result['saved_count'] > 0 else 'validation_failed',
            'details': result['details'][0] if result['details'] else None
        }
        
        action = 'guardado' if single_result['saved'] else 'omitido'
        print(f"📊 save_post completado: post {action}")
        
        return single_result
    
    def retrieve_limit_post(self, group_id: int) -> Optional[Dict[str, Any]]:
        """
        Obtiene el post más reciente de un grupo para usar como límite.
        Devuelve grupo, texto y autor para pasarlo a we_found_the_limit_post.
        
        Args:
            group_id: ID del grupo
            
        Returns:
            Dict con datos del post límite o None si no hay posts
        """
        print(f"🔍 Obteniendo post límite para grupo {group_id}")
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Obtener el post más reciente del grupo
            cursor.execute("""
                SELECT content, author, timestamp, created_at
                FROM posts
                WHERE group_id = ?
                ORDER BY timestamp DESC, created_at DESC
                LIMIT 1
            """, (group_id,))
            
            row = cursor.fetchone()
            
            if row:
                limit_post = {
                    'group_id': group_id,
                    'content': row['content'],
                    'text': row['content'],  # Alias para compatibilidad
                    'author': row['author'],
                    'timestamp': row['timestamp'],
                    'created_at': row['created_at']
                }
                
                print(f"   ✅ Post límite encontrado: {row['author'][:30] if row['author'] else 'Sin autor'}")
                return limit_post
            else:
                print(f"   ℹ️ No hay posts previos en el grupo {group_id}")
                return None
    
    def get_recent_posts_by_group(self, group_id: int, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Obtiene posts recientes de un grupo específico.
        
        Args:
            group_id: ID del grupo
            limit: Número máximo de posts a obtener
            
        Returns:
            Lista de posts recientes
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM posts
                WHERE group_id = ?
                ORDER BY timestamp DESC, created_at DESC
                LIMIT ?
            """, (group_id, limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def _check_duplicate_against_db(self, post: Dict[str, Any], 
                                  existing_posts: List[Dict[str, Any]], 
                                  threshold: float = 0.8) -> bool:
        """
        Verifica si un post es duplicado contra posts existentes en BD.
        
        Args:
            post: Post a verificar
            existing_posts: Posts existentes en BD
            threshold: Umbral de similitud
            
        Returns:
            bool: True si es duplicado
        """
        if not existing_posts:
            return False
        
        post_content = post.get('content', '')
        post_author = post.get('author', '')
        
        for existing_post in existing_posts:
            existing_content = existing_post.get('content', '')
            existing_author = existing_post.get('author', '')
            
            # Calcular similitud de contenido
            content_similarity = calculate_jaccard(post_content, existing_content, threshold)
            
            # Si la similitud supera el umbral, es duplicado
            if content_similarity >= threshold:
                return True
        
        return False
