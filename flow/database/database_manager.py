"""
Database Manager - Sistema completo de base de datos multi-usuario
Manejo de usuarios, configuración, grupos y posts con esquema escalable
"""

import sqlite3
import json
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from utils.path_utils import get_database_str


class DatabaseManager:
    """
    Gestor completo de base de datos para el sistema multi-usuario
    Maneja usuarios, configuración, grupos y posts
    """

    def __init__(self, db_path: Optional[str] = None):
        """
        Inicializa el gestor de base de datos

        Args:
            db_path: Ruta al archivo de base de datos SQLite
        """
        if db_path is None:
            # Usar ruta absoluta
            db_path = get_database_str()

        self.db_path = str(db_path)
        self._ensure_db_directory()
        self._init_database()
        print(f"✅ DatabaseManager inicializado: {self.db_path}")

    def _ensure_db_directory(self) -> None:
        """Asegura que el directorio de la base de datos existe"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)

    def _init_database(self) -> None:
        """Inicializa la base de datos y crea todas las tablas necesarias"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Verificar si necesitamos migrar desde el esquema anterior
            self._check_and_migrate_schema(cursor)

            # Tabla de usuarios
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    email TEXT NOT NULL,
                    password TEXT NOT NULL,
                    fb_email TEXT,
                    fb_password TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active'
                )
            """)

            # Tabla de configuración de Facebook por usuario
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_facebook_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    email TEXT NOT NULL,
                    password TEXT NOT NULL,
                    notas TEXT,
                    credentials_approved BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    UNIQUE(user_id)
                )
            """)

            # Tabla de configuración de humanización por usuario
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_humanization_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    home_time_min INTEGER DEFAULT 4,
                    home_time_max INTEGER DEFAULT 6,
                    like_probability REAL DEFAULT 0.2,
                    scroll_probability REAL DEFAULT 0.8,
                    feed_click_probability REAL DEFAULT 0.15,
                    comment_probability REAL DEFAULT 0.05,
                    scroll_min_rounds INTEGER DEFAULT 1,
                    scroll_max_rounds INTEGER DEFAULT 4,
                    scroll_min_duration INTEGER DEFAULT 2,
                    scroll_max_duration INTEGER DEFAULT 8,
                    scroll_distance_min INTEGER DEFAULT 200,
                    scroll_distance_max INTEGER DEFAULT 600,
                    action_delay_min INTEGER DEFAULT 3,
                    action_delay_max INTEGER DEFAULT 12,
                    between_activities_min INTEGER DEFAULT 15,
                    between_activities_max INTEGER DEFAULT 45,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    UNIQUE(user_id)
                )
            """)

            # Tabla de grupos por usuario
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS groups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    nombre TEXT NOT NULL,
                    url TEXT NOT NULL,
                    in_flow BOOLEAN NOT NULL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT DEFAULT '{}',
                    status TEXT DEFAULT 'active',
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    UNIQUE(user_id, url)
                )
            """)

            # Tabla de posts por usuario y grupo
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS posts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    group_id INTEGER NOT NULL,
                    content TEXT NOT NULL,
                    author TEXT,
                    author_link TEXT DEFAULT '',
                    timestamp TIMESTAMP,
                    links TEXT DEFAULT '[]',
                    images TEXT DEFAULT '[]',
                    reactions INTEGER DEFAULT 0,
                    comments INTEGER DEFAULT 0,
                    is_processed_and_sent BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (group_id) REFERENCES groups(id)
                )
            """)

            # Tabla de configuración del sistema
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS setup (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    is_first_run BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Insertar registro inicial si la tabla está vacía
            cursor.execute("SELECT COUNT(*) FROM setup")
            setup_count = cursor.fetchone()[0]
            if setup_count == 0:
                cursor.execute("""
                    INSERT INTO setup (is_first_run, created_at, updated_at)
                    VALUES (1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """)

            # Crear índices para optimizar consultas
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_groups_user_id ON groups(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_groups_in_flow ON groups(in_flow)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_groups_url ON groups(url)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_posts_group_id ON posts(group_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_posts_timestamp ON posts(timestamp)")

            conn.commit()
            print("✅ Esquema de base de datos inicializado")

    def _check_and_migrate_schema(self, cursor) -> None:
        """Verifica y migra desde el esquema anterior si es necesario"""
        try:
            # Verificar si existe la tabla groups con el esquema anterior
            cursor.execute("PRAGMA table_info(groups)")
            columns = [row[1] for row in cursor.fetchall()]

            if columns and 'user_id' not in columns:
                print("🔄 Detectado esquema anterior, iniciando migración...")
                self._migrate_old_schema(cursor)

            # Verificar y agregar nueva columna is_processed_and_sent en posts
            cursor.execute("PRAGMA table_info(posts)")
            posts_columns = [row[1] for row in cursor.fetchall()]

            if posts_columns and 'is_processed_and_sent' not in posts_columns:
                print("🔄 Agregando columna is_processed_and_sent a tabla posts...")
                cursor.execute("ALTER TABLE posts ADD COLUMN is_processed_and_sent BOOLEAN DEFAULT 0")
                print("✅ Columna is_processed_and_sent agregada exitosamente")

            # Verificar y agregar nueva columna author_link en posts
            if posts_columns and 'author_link' not in posts_columns:
                print("🔄 Agregando columna author_link a tabla posts...")
                cursor.execute("ALTER TABLE posts ADD COLUMN author_link TEXT DEFAULT ''")
                print("✅ Columna author_link agregada exitosamente")

        except sqlite3.OperationalError:
            # La tabla no existe, es una instalación nueva
            print("🆕 Instalación nueva detectada")

    def _migrate_old_schema(self, cursor) -> None:
        """Migra desde el esquema anterior al nuevo"""
        print("📦 Migrando esquema de base de datos...")

        # Respaldar tabla anterior
        cursor.execute("ALTER TABLE groups RENAME TO groups_old")

        # Crear usuario por defecto
        cursor.execute("""
            INSERT OR IGNORE INTO users (id, username, email, password, created_at)
            VALUES (1, 'default', '<EMAIL>', 'password', CURRENT_TIMESTAMP)
        """)

        # Recrear tabla groups con nuevo esquema
        cursor.execute("""
            CREATE TABLE groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL DEFAULT 1,
                nombre TEXT NOT NULL,
                url TEXT NOT NULL,
                in_flow BOOLEAN NOT NULL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT DEFAULT '{}',
                status TEXT DEFAULT 'active',
                FOREIGN KEY (user_id) REFERENCES users(id),
                UNIQUE(user_id, url)
            )
        """)

        # Migrar datos existentes
        cursor.execute("""
            INSERT INTO groups (id, user_id, nombre, url, in_flow, created_at, updated_at, metadata, status)
            SELECT id, 1, nombre, url, in_flow, created_at, updated_at,
                   COALESCE(metadata, '{}'), COALESCE(status, 'active')
            FROM groups_old
        """)

        # Eliminar tabla anterior
        cursor.execute("DROP TABLE groups_old")

        print("✅ Migración completada")

    # ==================== MÉTODOS DE USUARIOS ====================

    def create_user(self, username: str, email: str, password: str,
                   fb_email: Optional[str] = None, fb_password: Optional[str] = None) -> int:
        """
        Crea un nuevo usuario

        Args:
            username: Nombre de usuario único
            email: Email del usuario
            password: Contraseña del usuario
            fb_email: Email de Facebook (opcional)
            fb_password: Contraseña de Facebook (opcional)

        Returns:
            ID del usuario creado
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO users (username, email, password, fb_email, fb_password, updated_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (username, email, password, fb_email, fb_password))

            user_id = cursor.lastrowid
            conn.commit()

            if user_id is None:
                raise RuntimeError("No se pudo obtener el ID del usuario creado")

            print(f"✅ Usuario creado: ID={user_id}, username='{username}'")
            return user_id

    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """
        Obtiene un usuario por su nombre de usuario

        Args:
            username: Nombre de usuario

        Returns:
            Diccionario con datos del usuario o None
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM users WHERE username = ? AND status = 'active'", (username,))
            row = cursor.fetchone()

            return dict(row) if row else None

    # ==================== MÉTODOS DE GRUPOS ====================

    def get_groups_in_flow(self, user_id: int = 1) -> List[Dict[str, Any]]:
        """
        Obtiene todos los grupos que tienen in_flow = true para un usuario

        Args:
            user_id: ID del usuario (default 1 para compatibilidad)

        Returns:
            Lista de grupos en flujo
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM groups
                WHERE user_id = ? AND in_flow = 1 AND status = 'active'
                ORDER BY updated_at DESC
            """, (user_id,))

            return [dict(row) for row in cursor.fetchall()]

    def get_all_groups(self, user_id: int = 1, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Obtiene todos los grupos activos de un usuario

        Args:
            user_id: ID del usuario (default 1 para compatibilidad)
            limit: Límite de resultados (opcional)

        Returns:
            Lista de diccionarios con datos de grupos
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = "SELECT * FROM groups WHERE user_id = ? AND status = 'active' ORDER BY updated_at DESC"
            params = [user_id]

            if limit:
                query += " LIMIT ?"
                params.append(limit)

            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]

    def get_group_by_url(self, url: str, user_id: int = 1) -> Optional[Dict[str, Any]]:
        """
        Busca un grupo por URL

        Args:
            url: URL del grupo
            user_id: ID del usuario (default 1 para compatibilidad)

        Returns:
            Diccionario con datos del grupo o None si no existe
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM groups
                WHERE url = ? AND user_id = ? AND status = 'active'
            """, (url, user_id))

            row = cursor.fetchone()
            return dict(row) if row else None

    def add_group(self, user_id: int, nombre: str, url: str, in_flow: bool = False,
                  metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        Añade un nuevo grupo a la base de datos

        Args:
            user_id: ID del usuario propietario
            nombre: Nombre del grupo
            url: URL del grupo (debe ser única por usuario)
            in_flow: Si el grupo está en el flujo actual
            metadata: Metadatos adicionales del grupo

        Returns:
            ID del grupo creado
        """
        metadata_json = json.dumps(metadata or {})

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO groups (user_id, nombre, url, in_flow, metadata, updated_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (user_id, nombre, url, in_flow, metadata_json))

            group_id = cursor.lastrowid
            conn.commit()

            if group_id is None:
                raise RuntimeError("No se pudo obtener el ID del grupo creado")

            print(f"✅ Grupo añadido: ID={group_id}, nombre='{nombre}', user_id={user_id}")
            return group_id



    def update_group_flow_status(self, url: str, in_flow: bool, user_id: int = 1) -> bool:
        """
        Actualiza el estado in_flow de un grupo por URL

        Args:
            url: URL del grupo
            in_flow: Nuevo estado del flujo
            user_id: ID del usuario (default 1 para compatibilidad)

        Returns:
            bool: True si la actualización fue exitosa
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE groups
                SET in_flow = ?, updated_at = CURRENT_TIMESTAMP
                WHERE url = ? AND user_id = ?
            """, (in_flow, url, user_id))

            updated = cursor.rowcount > 0
            conn.commit()

            if updated:
                print(f"✅ Grupo actualizado: URL='{url}', in_flow={in_flow}")
            else:
                print(f"⚠️ Grupo no encontrado para actualizar: URL='{url}'")

            return updated

    # ==================== MÉTODOS DE POSTS ====================

    def save_posts(self, posts: List[Dict[str, Any]]) -> bool:
        """
        Guarda una lista de posts en la base de datos

        Args:
            posts: Lista de posts a guardar

        Returns:
            bool: True si el guardado fue exitoso
        """
        if not posts:
            return True

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            saved_count = 0
            for post in posts:
                try:
                    cursor.execute("""
                        INSERT INTO posts (
                            user_id, group_id, content, author, timestamp,
                            links, images, reactions, comments, is_processed_and_sent
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        post.get('user_id', 1),
                        post.get('group_id'),
                        post.get('content', ''),
                        post.get('author', ''),
                        post.get('timestamp'),
                        json.dumps(post.get('links', [])),
                        json.dumps(post.get('images', [])),
                        post.get('reactions', 0),
                        post.get('comments', 0),
                        post.get('is_processed_and_sent', False)
                    ))
                    saved_count += 1
                except Exception as e:
                    print(f"⚠️ Error guardando post: {e}")
                    continue

            conn.commit()
            print(f"✅ {saved_count}/{len(posts)} posts guardados exitosamente")
            return saved_count > 0

    def get_recent_posts_by_group(self, group_id: int, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Obtiene posts recientes de un grupo específico

        Args:
            group_id: ID del grupo
            limit: Número máximo de posts a obtener

        Returns:
            Lista de posts recientes
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM posts
                WHERE group_id = ?
                ORDER BY timestamp DESC, created_at DESC
                LIMIT ?
            """, (group_id, limit))

            return [dict(row) for row in cursor.fetchall()]

    def get_processing_statistics(self, user_id: int = 1) -> Dict[str, Any]:
        """
        Obtiene estadísticas del estado de procesamiento

        Args:
            user_id: ID del usuario (default 1 para compatibilidad)

        Returns:
            Dict con estadísticas
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Total de grupos en flujo
            cursor.execute("SELECT COUNT(*) FROM groups WHERE user_id = ? AND in_flow = 1 AND status = 'active'", (user_id,))
            groups_in_flow = cursor.fetchone()[0]

            # Total de grupos
            cursor.execute("SELECT COUNT(*) FROM groups WHERE user_id = ? AND status = 'active'", (user_id,))
            total_groups = cursor.fetchone()[0]

            # Total de posts
            cursor.execute("SELECT COUNT(*) FROM posts WHERE user_id = ?", (user_id,))
            total_posts = cursor.fetchone()[0]

            return {
                "total_groups": total_groups,
                "groups_in_flow": groups_in_flow,
                "groups_out_flow": total_groups - groups_in_flow,
                "total_posts": total_posts,
                "last_update": datetime.now().isoformat()
            }

    def migrate_from_old_groups_repository(self) -> bool:
        """
        Migra datos del GroupsRepository anterior al nuevo esquema

        Returns:
            bool: True si la migración fue exitosa
        """
        try:
            # Crear usuario por defecto si no existe
            default_user = self.get_user_by_username("default")
            if not default_user:
                user_id = self.create_user("default", "<EMAIL>", "password")
                print(f"✅ Usuario por defecto creado: ID={user_id}")
            else:
                user_id = default_user['id']
                print(f"✅ Usuario por defecto encontrado: ID={user_id}")

            return True
        except Exception as e:
            print(f"❌ Error en migración: {e}")
            return False

    def bulk_add_groups(self, groups_data: List[Dict[str, Any]], user_id: int = 1) -> Tuple[int, int]:
        """
        Añade múltiples grupos de forma eficiente

        Args:
            groups_data: Lista de diccionarios con datos de grupos
                        Cada dict debe tener: nombre, url, in_flow (opcional)
            user_id: ID del usuario propietario (default 1 para compatibilidad)

        Returns:
            Tupla (añadidos, duplicados)
        """
        added = 0
        duplicates = 0

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for group_data in groups_data:
                nombre = group_data.get('nombre', group_data.get('text', ''))
                url = group_data.get('url', group_data.get('href', ''))
                in_flow = group_data.get('in_flow', False)
                metadata = group_data.get('metadata', {})

                if not nombre or not url:
                    continue

                try:
                    cursor.execute("""
                        INSERT INTO groups (user_id, nombre, url, in_flow, metadata, updated_at)
                        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    """, (user_id, nombre, url, in_flow, json.dumps(metadata)))
                    added += 1

                except sqlite3.IntegrityError:
                    # URL duplicada
                    duplicates += 1
                    continue

            conn.commit()

        print(f"✅ Bulk insert completado: {added} añadidos, {duplicates} duplicados")
        return added, duplicates

    def print_stats(self, user_id: int = 1) -> None:
        """Imprime estadísticas de la base de datos"""
        stats = self.get_processing_statistics(user_id)

        print("\n📊 ESTADÍSTICAS DE GRUPOS")
        print("=" * 30)
        print(f"📁 Base de datos: {self.db_path}")
        print(f"📈 Total grupos: {stats['total_groups']}")
        print(f"🔄 En flujo: {stats['groups_in_flow']}")
        print(f"⏸️ Fuera de flujo: {stats['groups_out_flow']}")
        print(f"📄 Total posts: {stats['total_posts']}")
        print(f"🕒 Última actualización: {stats['last_update']}")
        print("=" * 30)

    def get_stats(self) -> Dict[str, Any]:
        """
        Obtiene estadísticas generales de la base de datos

        Returns:
            Diccionario con estadísticas
        """
        return self.get_processing_statistics()

    def clear_all_posts(self, user_id: int = 1) -> int:
        """
        Elimina todos los posts de un usuario (útil para limpiar datos de prueba)

        Args:
            user_id: ID del usuario (default 1)

        Returns:
            Número de posts eliminados
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute("DELETE FROM posts WHERE user_id = ?", (user_id,))
            deleted_count = cursor.rowcount
            conn.commit()

            print(f"🗑️ Eliminados {deleted_count} posts de prueba")
            return deleted_count

    def clear_all_posts_silent(self, user_id: int = 1) -> int:
        """Elimina todos los posts silenciosamente"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM posts WHERE user_id = ?", (user_id,))
            deleted_count = cursor.rowcount
            conn.commit()
            return deleted_count

    # ==================== MÉTODOS DE CONFIGURACIÓN SETUP ====================

    def get_setup_config(self) -> Dict[str, Any]:
        """
        Obtiene la configuración del sistema desde la tabla setup
        
        Returns:
            Diccionario con la configuración del sistema
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM setup ORDER BY id LIMIT 1")
            row = cursor.fetchone()
            
            if row:
                return dict(row)
            else:
                # Si no hay registro, crear uno por defecto
                cursor.execute("""
                    INSERT INTO setup (is_first_run, created_at, updated_at)
                    VALUES (1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """)
                conn.commit()
                
                cursor.execute("SELECT * FROM setup ORDER BY id LIMIT 1")
                row = cursor.fetchone()
                return dict(row) if row else {}

    def update_is_first_run(self, is_first_run: bool) -> bool:
        """
        Actualiza el valor de is_first_run en la configuración del sistema
        
        Args:
            is_first_run: Nuevo valor para is_first_run
            
        Returns:
            bool: True si la actualización fue exitosa
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE setup
                SET is_first_run = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = (SELECT MIN(id) FROM setup)
            """, (is_first_run,))
            
            updated = cursor.rowcount > 0
            conn.commit()
            
            if updated:
                print(f"✅ Configuración actualizada: is_first_run={is_first_run}")
            else:
                print(f"⚠️ No se pudo actualizar la configuración")
                
            return updated

    def save_posts_ordered_by_posinset(self, extracted_posts: List[Dict[str, Any]], group_id: int, user_id: int = 1) -> bool:
        """
        Inserta posts provenientes del extractor asegurando el orden correcto basado en 'posinset'.

        Contexto:
        - El extractor devuelve un array donde 'posinset' = 1 es el post más reciente y el valor mayor es el más viejo.
        - No confiamos necesariamente en el orden entregado, así que reordenamos nosotros.
        - No modificamos el esquema (solo usamos columnas existentes). 'posinset' NO se guarda (decisión explícita opción A).
        - Para que el orden de recencia se refleje naturalmente en consultas por created_at DESC / timestamp, insertamos
          primero los más viejos y al final los más nuevos (el más nuevo obtiene el created_at más reciente).

        Mapeo de campos:
        - contenidoPost  -> content
        - nombreAutor    -> author
        - (linkAutor se ignora aquí porque el método save_posts actual no inserta author_link; se podría extender luego)
        - linkPost:
            * Si es distinto de 'No encontrado' se almacena como lista de un solo elemento en 'links'
            * Si es 'No encontrado' se guarda una lista vacía
        - imagenes       -> images (lista)
        - timestamp      -> timestamp (se respeta si viene; si no, cae en None)
        - reactions / comments no existen en el payload actual: se dejan en 0 (default)
        - is_processed_and_sent -> False (default inicial)

        Args:
            extracted_posts: Lista de dicts crudos del extractor JS.
            group_id: ID del grupo al que pertenecen todos los posts.
            user_id: ID del usuario (por compatibilidad multiusuario futura).

        Returns:
            bool: True si al menos un post fue guardado exitosamente.
        """
        if not extracted_posts:
            return True  # Nada que hacer, no es un fallo

        # 1. Normalizar estructura defensivamente
        normalized = []
        for raw in extracted_posts:
            if not isinstance(raw, dict):
                continue
            posinset = raw.get("posinset")
            try:
                # Si no hay posinset o no es convertible, asignamos un número alto para no romper orden relativo
                posinset_val = int(posinset) if posinset is not None else 10**9
            except Exception:
                posinset_val = 10**9

            link_post = raw.get("linkPost", "")
            links_list = []
            if isinstance(link_post, str) and link_post and link_post != "No encontrado":
                links_list = [link_post]

            contenido = raw.get("contenidoPost", "")
            autor = raw.get("nombreAutor", "")

            # Construir dict compatible con save_posts
            normalized.append({
                "user_id": user_id,
                "group_id": group_id,
                "content": contenido,
                "author": autor,
                # 'author_link' se omite porque save_posts no lo maneja todavía
                "timestamp": raw.get("timestamp"),  # Puede ser int (epoch ms); SQLite aceptará int o convertimos a ISO si se decide luego
                "links": links_list,
                "images": raw.get("imagenes", []) if isinstance(raw.get("imagenes"), list) else [],
                "reactions": 0,
                "comments": 0,
                "is_processed_and_sent": False,
                "_posinset_internal": posinset_val  # Campo interno temporal para ordenar antes de pasar a save_posts
            })

        if not normalized:
            return True

        # 2. Orden: ascendente por posinset (1 = más nuevo). Luego insertamos del más viejo al más nuevo.
        #    El más viejo tendrá el mayor posinset, así que insertamos en orden descendente para que el más nuevo sea el último insertado.
        #    Paso A: ordenar asc (para garantizar consistencia determinista), luego invertimos para inserción.
        ordered_newest_first = sorted(normalized, key=lambda p: p["_posinset_internal"])  # [nuevo,...,viejo]
        insertion_sequence = list(reversed(ordered_newest_first))  # [viejo,...,nuevo]

        # 3. Limpiar clave interna antes de guardar
        for p in insertion_sequence:
            p.pop("_posinset_internal", None)

        # 4. Usar método existente para persistir
        return self.save_posts(insertion_sequence)
