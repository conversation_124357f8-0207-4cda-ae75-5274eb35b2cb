#!/usr/bin/env python3
"""
Facebook Login Flow - Basado en patrones aprendidos del tracking
Implementa login adaptativo usando mejores prácticas de Playwright
"""

import os
import sys
import time
import json
from typing import Dict, Optional, Tuple, Any
from camoufox import <PERSON>oufox
from browserforge.fingerprints import Screen
from camoufox.fingerprints import generate_fingerprint

class FacebookLoginFlow:
    def __init__(self, config_path: str = "../profiles/default/config.json"):
        """Inicializa el flow de login con configuración adaptativa"""
        self.config_path = config_path
        self.config = self._load_config()
        
        # Configuración de ventana ajustada (máximo 1280)
        self.fingerprint_file = "../home/<USER>/persistent_fingerprint.json"  # CRÍTICO: Usar mismo fingerprint
        self.session_file = "results/facebook_session.json"  # CRÍTICO: Sesión persistente
        self.results_dir = "results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Cargar fingerprint persistente
        self.fingerprint = self._load_fingerprint()
        
        # Patrones aprendidos del tracking para login
        self.login_patterns = {
            "email_selectors": [
                "input[type='email']",
                "input[name='email']", 
                "input[placeholder*='correo']",
                "input[placeholder*='email']",
                "#email"
            ],
            "password_selectors": [
                "input[type='password']",
                "input[name='pass']",
                "input[name='password']",
                "#pass"
            ],
            "login_button_selectors": [
                "button[type='submit']",
                "button[name='login']",
                "input[type='submit']",
                "[data-testid='royal_login_button']"
            ],
            "error_indicators": [
                "[role='alert']",
                ".error",
                "[data-testid='error']",
                "[aria-label*='error']"
            ]
        }
    
    def _load_config(self) -> Dict:
        """Carga configuración del perfil"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error cargando configuración: {e}")
            return {}
    
    def _load_fingerprint(self):
        """Carga fingerprint persistente o genera uno nuevo"""
        if os.path.exists(self.fingerprint_file):
            try:
                with open(self.fingerprint_file, 'r') as f:
                    fingerprint_data = json.load(f)
                
                # Regenerar con parámetros consistentes pero ventana más pequeña
                saved_screen = fingerprint_data.get('screen', {})
                screen = Screen(
                    max_width=min(saved_screen.get('width', 1280), 1280),
                    max_height=saved_screen.get('height', 720)
                )
                
                saved_os = fingerprint_data.get('navigator', {}).get('platform', 'Win32')
                os_type = 'windows' if 'win' in saved_os.lower() else 'linux'
                
                return generate_fingerprint(os=os_type, screen=screen)
            except Exception as e:
                print(f"⚠️ Error cargando fingerprint: {e}")
        
        # Generar nuevo con ventana pequeña
        screen = Screen(max_width=1280, max_height=720)
        return generate_fingerprint(os='windows', screen=screen)
    
    def _save_session(self, page):
        """Guarda sesión usando storageState() oficial de Playwright"""
        try:
            storage_state = page.context.storage_state(path=self.session_file)
            print(f"💾 Sesión guardada: {len(storage_state.get('cookies', []))} cookies")
        except Exception as e:
            print(f"⚠️ Error guardando sesión: {e}")

    def _session_exists_and_valid(self):
        """Verifica si existe una sesión válida (menos de 24h)"""
        if not os.path.exists(self.session_file):
            return False

        try:
            file_age = time.time() - os.path.getmtime(self.session_file)
            if file_age > 86400:  # 24 horas
                print("⚠️ Sesión expirada (>24h)")
                return False
            return True
        except Exception as e:
            print(f"⚠️ Error verificando sesión: {e}")
            return False

    def _create_camoufox_args(self) -> Dict:
        """Crea argumentos para Camoufox con ventana optimizada"""
        fingerprint_profile = self.config.get('fingerprint_profile', {})
        camoufox_config = fingerprint_profile.get('camoufox_config', {})
        firefox_prefs = fingerprint_profile.get('firefox_prefs', {})
        
        args = {
            'fingerprint': self.fingerprint,
            'os': camoufox_config.get('os', 'windows'),
            'geoip': camoufox_config.get('geoip', True),
            'humanize': camoufox_config.get('humanize', True),
            'headless': camoufox_config.get('headless', False),
            'enable_cache': camoufox_config.get('enable_cache', True),
            'firefox_user_prefs': firefox_prefs
            # NO window - viene del fingerprint
        }

        # CRÍTICO: Cargar sesión persistente para evitar bans
        if self._session_exists_and_valid():
            args['storage_state'] = self.session_file
            print(f"🔄 Cargando sesión persistente para evitar ban")

        return args
    
    def _wait_and_check(self, page, timeout: int = 5000) -> None:
        """Espera humanizada con verificación de errores"""
        time.sleep(0.5 + (time.time() % 1))  # Delay humanizado
        page.wait_for_timeout(timeout)
    
    def _detect_login_elements(self, page) -> Dict:
        """Detecta elementos de login usando patrones adaptativos"""
        elements: Dict[str, Any] = {
            "email_field": None,
            "password_field": None,
            "login_button": None,
            "errors": []
        }
        
        # Buscar campo de email
        for selector in self.login_patterns["email_selectors"]:
            try:
                element = page.locator(selector).first
                if element.is_visible():
                    elements["email_field"] = element
                    break
            except:
                continue
        
        # Buscar campo de password
        for selector in self.login_patterns["password_selectors"]:
            try:
                element = page.locator(selector).first
                if element.is_visible():
                    elements["password_field"] = element
                    break
            except:
                continue
        
        # Buscar botón de login
        for selector in self.login_patterns["login_button_selectors"]:
            try:
                element = page.locator(selector).first
                if element.is_visible():
                    elements["login_button"] = element
                    break
            except:
                continue
        
        # Detectar errores
        for selector in self.login_patterns["error_indicators"]:
            try:
                error_elements = page.locator(selector)
                for i in range(error_elements.count()):
                    if error_elements.nth(i).is_visible():
                        error_text = error_elements.nth(i).text_content()
                        if error_text and len(error_text.strip()) > 0:
                            errors = elements.get("errors", [])
                            if isinstance(errors, list):
                                errors.append(error_text.strip())
            except:
                continue
        
        return elements
    
    def _check_login_problems(self, page) -> Tuple[bool, str]:
        """Verifica si hay problemas durante el login"""
        problems = []
        
        # Verificar errores visibles
        elements = self._detect_login_elements(page)
        if elements["errors"]:
            problems.append(f"Errores detectados: {', '.join(elements['errors'])}")
        
        # Verificar si estamos en página de verificación
        verification_indicators = [
            "verificación",
            "verification", 
            "código",
            "code",
            "two-factor",
            "2fa"
        ]
        
        try:
            page_text = page.locator('body').text_content().lower()
        except:
            page_text = ""
        for indicator in verification_indicators:
            if indicator in page_text:
                problems.append(f"Verificación requerida: {indicator}")
        
        # Verificar si estamos bloqueados (evitar falsos positivos)
        block_indicators = [
            "cuenta bloqueada",
            "account blocked",
            "account suspended",
            "account disabled",
            "temporalmente bloqueada"
        ]

        for indicator in block_indicators:
            if indicator in page_text:
                problems.append(f"Cuenta bloqueada: {indicator}")
        
        # Verificar si hay captcha
        captcha_selectors = [
            "[data-testid='captcha']",
            ".captcha",
            "#captcha",
            "[aria-label*='captcha']"
        ]
        
        for selector in captcha_selectors:
            try:
                if page.locator(selector).is_visible():
                    problems.append("CAPTCHA detectado")
                    break
            except:
                continue
        
        has_problems = len(problems) > 0
        problem_summary = "; ".join(problems) if problems else "Sin problemas detectados"
        
        return has_problems, problem_summary
    
    def login(self, email: str, password: str) -> Dict:
        """Ejecuta el proceso de login adaptativo"""
        print("🔐 Iniciando proceso de login adaptativo...")
        
        result = {
            "success": False,
            "message": "",
            "problems": [],
            "current_url": "",
            "elements_found": {},
            "screenshots": []
        }
        
        camoufox_args = self._create_camoufox_args()
        
        try:
            # Separar argumentos de launch vs new_context para sesión persistente
            launch_args = {k: v for k, v in camoufox_args.items() if k != 'storage_state'}
            context_args = {}
            if 'storage_state' in camoufox_args:
                context_args['storage_state'] = camoufox_args['storage_state']

            with Camoufox(**launch_args, i_know_what_im_doing=True) as browser:
                if context_args:
                    if hasattr(browser, 'new_context'):
                        context = browser.new_context(**context_args)
                        page = context.new_page()
                    else:
                        page = browser.new_page()
                else:
                    page = browser.new_page()
                page.set_default_timeout(30000)
                
                print("🌐 Navegando a Facebook...")
                page.goto("https://www.facebook.com", wait_until="domcontentloaded")

                # Usar detectores como criterio de "ya llegamos"
                from utils.page_detectors import page_detectors
                print("🔍 Verificando qué página cargó usando detectores...")

                max_attempts = 6  # 30 segundos total
                for attempt in range(max_attempts):
                    detection = page_detectors.detect_current_page(page)
                    current_page = detection.get('current_page', 'unknown')

                    if current_page in ['home', 'login']:
                        print(f"✅ Página {current_page} detectada (intento {attempt+1})")
                        break

                    if attempt < max_attempts - 1:
                        print(f"⏳ Intento {attempt+1}/{max_attempts} - esperando 5s...")
                        time.sleep(5)
                else:
                    result["message"] = "❌ Error: No se pudo determinar tipo de página después de 30s"
                    return result

                self._wait_and_check(page, 1000)  # Espera adicional reducida
                
                # Screenshot inicial - DESHABILITADO
                # Screenshots deshabilitados para optimizar rendimiento
                
                # Detectar elementos de login
                print("🔍 Detectando elementos de login...")
                elements = self._detect_login_elements(page)
                result["elements_found"] = {
                    "email_field": elements["email_field"] is not None,
                    "password_field": elements["password_field"] is not None,
                    "login_button": elements["login_button"] is not None,
                    "errors_count": len(elements["errors"])
                }
                
                if not elements["email_field"]:
                    result["message"] = "❌ No se encontró campo de email"
                    return result
                
                if not elements["password_field"]:
                    result["message"] = "❌ No se encontró campo de password"
                    return result
                
                if not elements["login_button"]:
                    result["message"] = "❌ No se encontró botón de login"
                    return result
                
                # Llenar formulario
                print("📝 Llenando formulario de login...")
                elements["email_field"].fill(email)
                self._wait_and_check(page, 1000)
                
                elements["password_field"].fill(password)
                self._wait_and_check(page, 1000)
                
                # Screenshot antes de submit - DESHABILITADO
                # Screenshots deshabilitados para optimizar rendimiento
                
                # Hacer click en login
                print("🚀 Enviando formulario...")
                elements["login_button"].click()
                self._wait_and_check(page, 8000)  # Más tiempo para procesar login
                
                # Screenshot después de submit - DESHABILITADO
                # Screenshots deshabilitados para optimizar rendimiento

                # Esperar más tiempo para que la página se estabilice
                print("⏳ Esperando respuesta del servidor...")
                self._wait_and_check(page, 5000)

                # Screenshot final después de esperar - DESHABILITADO
                # Screenshots deshabilitados para optimizar rendimiento

                # Verificar problemas
                has_problems, problem_summary = self._check_login_problems(page)
                result["problems"] = problem_summary
                result["current_url"] = page.url
                
                if has_problems:
                    result["message"] = f"⚠️ Problemas detectados: {problem_summary}"
                    result["success"] = False
                else:
                    # TODO: Verificar si ya estamos en home
                    # (Pendiente implementar tracking del home para diferenciar)
                    if "facebook.com" in page.url and "login" not in page.url:
                        result["success"] = True
                        result["message"] = "✅ Login aparentemente exitoso"

                        # CRÍTICO: Guardar sesión para evitar bans futuros
                        self._save_session(page)
                    else:
                        result["success"] = False
                        result["message"] = "❓ Estado de login incierto"
                
                return result
                
        except Exception as e:
            result["message"] = f"❌ Error durante login: {str(e)}"
            return result

def main():
    """Función principal para pruebas"""
    print("🧪 PRUEBA DE LOGIN FLOW")
    print("=" * 40)

    # Cargar credenciales del config
    login_flow = FacebookLoginFlow()
    facebook_account = login_flow.config.get('facebook_account', {})

    email = facebook_account.get('email', '')
    password = facebook_account.get('password', '')

    if not email or not password:
        print("❌ No se encontraron credenciales en config")
        print("💡 Verifica profiles/default/config.json")
        return

    print(f"📧 Email encontrado: {email}")
    print(f"🔐 Password: {'*' * len(password)}")
    
    result = login_flow.login(email, password)
    
    print("\n📊 RESULTADO:")
    print(f"✅ Éxito: {result['success']}")
    print(f"💬 Mensaje: {result['message']}")
    print(f"🌐 URL actual: {result['current_url']}")
    print(f"📸 Screenshots: {len(result['screenshots'])}")
    
    if result['elements_found']:
        print(f"🔍 Elementos encontrados:")
        for element, found in result['elements_found'].items():
            status = "✅" if found else "❌"
            print(f"   {status} {element}")

if __name__ == "__main__":
    main()
