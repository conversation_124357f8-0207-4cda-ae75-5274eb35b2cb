#!/usr/bin/env python3
"""
Script de prueba para la función test_save_with_json_data
Demuestra cómo usar datos de JSON para testear el flujo de guardado en la BD
"""

import sys
from pathlib import Path
from post_repository import PostRepository

def test_json_save_demo():
    """
    Demostración de test_save_with_json_data usando un archivo JSON existente
    """
    print("🧪 DEMO: Test de guardado con datos JSON")
    print("=" * 50)
    
    # Ruta al JSON de ejemplo (usar archivo existente)
    json_path = "results/test_extractions/posts_test_20250914_073953.json"
    
    # Verificar que el archivo existe
    if not Path(json_path).exists():
        print(f"❌ Archivo no encontrado: {json_path}")
        print("   Asegúrate de que existe un archivo JSON de test en la ruta especificada")
        return False
    
    # Crear instancia del repositorio
    print(f"🔧 Inicializando PostRepository...")
    repo = PostRepository()
    
    # Ejecutar test con diferentes configuraciones
    print(f"\n📋 TEST 1: Primera ejecución (is_first_run=True)")
    print("-" * 40)
    
    result1 = repo.test_save_with_json_data(
        json_path=json_path,
        group_id=None,  # Que resuelva automáticamente desde la URL del JSON
        user_id=1,      # Usuario de prueba
        is_test=True,
        is_first_run=False  # Cambiar a False para verificar duplicados BD
    )
    
    print(f"Resultado TEST 1:")
    print(f"  ✅ Success: {result1.get('success')}")
    print(f"  💾 Guardados: {result1.get('saved_count', 0)}")
    print(f"  ⏭️ Omitidos: {result1.get('skipped_count', 0)}")
    print(f"  🔍 Duplicados internos: {result1.get('duplicate_count', 0)}")
    print(f"  ⚠️ Inválidos: {result1.get('invalid_count', 0)}")
    
    print(f"\n📋 TEST 2: Segunda ejecución (is_first_run=False)")
    print("-" * 40)
    print("   Nota: Debería detectar duplicados contra la BD")
    
    result2 = repo.test_save_with_json_data(
        json_path=json_path,
        group_id=None,  # Que resuelva automáticamente desde la URL del JSON
        user_id=1,
        is_test=True,
        is_first_run=False  # Segunda ejecución - verifica duplicados BD
    )
    
    print(f"Resultado TEST 2:")
    print(f"  ✅ Success: {result2.get('success')}")
    print(f"  💾 Guardados: {result2.get('saved_count', 0)}")
    print(f"  ⏭️ Omitidos: {result2.get('skipped_count', 0)}")
    print(f"  🔍 Duplicados internos: {result2.get('duplicate_count', 0)}")
    print(f"  ⚠️ Inválidos: {result2.get('invalid_count', 0)}")
    
    print(f"\n📊 RESUMEN COMPARATIVO:")
    print("-" * 40)
    print(f"Primera ejecución:  {result1.get('saved_count', 0)} posts guardados")
    print(f"Segunda ejecución:  {result2.get('saved_count', 0)} posts guardados")
    print(f"Diferencia:         {result1.get('saved_count', 0) - result2.get('saved_count', 0)} posts (duplicados detectados)")
    
    if result1.get('saved_count', 0) > result2.get('saved_count', 0):
        print("✅ Detección de duplicados funcionando correctamente!")
    else:
        print("⚠️ No se detectaron duplicados o algo inesperado ocurrió")
    
    return True

def show_usage():
    """Muestra información de uso del script"""
    print("📖 USO de test_save_with_json_data():")
    print("=" * 50)
    print()
    print("from post_repository import PostRepository")
    print()
    print("# Crear instancia")
    print("repo = PostRepository()")
    print()
    print("# Ejecutar test con JSON")
    print("result = repo.test_save_with_json_data(")
    print("    json_path='flow/results/test_extractions/posts_test_YYYYMMDD_HHMMSS.json',")
    print("    group_id=None,       # None = resuelve automáticamente desde JSON")
    print("    user_id=1,           # ID del usuario")
    print("    is_test=True,        # Modo test (limita posts)")
    print("    is_first_run=False   # Si es primera vez (no verifica duplicados BD)")
    print(")")
    print()
    print("📋 El resultado incluye:")
    print("  - success: bool - Si la operación fue exitosa")
    print("  - saved_count: int - Posts guardados en BD")
    print("  - skipped_count: int - Posts omitidos (duplicados)")
    print("  - duplicate_count: int - Duplicados internos encontrados")
    print("  - invalid_count: int - Posts con contenido insuficiente")
    print("  - json_source: str - Ruta del archivo JSON usado")
    print("  - json_meta: dict - Metadatos del JSON")
    print("  - details: list - Detalles de cada post procesado")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        show_usage()
    else:
        test_json_save_demo()