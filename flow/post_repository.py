"""
Post Repository - Repositorio para operaciones de posts con validación y detección de duplicados
Rescatado y adaptado desde protos_deprecated para trabajar con el extractor actual de Facebook
"""

import sqlite3
import json
import re
from typing import List, Dict, Any, Optional
from pathlib import Path

from utils.path_utils import get_database_str


class PostRepository:
    """
    Repositorio especializado para operaciones de posts con validación avanzada
    Adaptado para trabajar con la estructura del FacebookPostExtractor
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Inicializa el repositorio de posts
        
        Args:
            db_path: Ruta al archivo de base de datos SQLite
        """
        if db_path is None:
            db_path = get_database_str()
        
        self.db_path = str(db_path)
        self._ensure_db_exists()
    
    def _ensure_db_exists(self):
        """Asegura que la base de datos existe"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def _sort_posts_by_posinset(self, posts: List[Dict]) -> List[Dict]:
        """
        Ordena posts por posinset de mayor a menor (más viejo a más nuevo)

        El posinset en Facebook indica la posición en el feed:
        - posinset=1: Post más reciente (más nuevo)
        - posinset=N: Post más viejo

        Para que ID más alto = post más reciente, guardamos:
        - Posts viejos (posinset alto) primero → ID bajo
        - Posts nuevos (posinset bajo) último → ID alto

        Args:
            posts: Lista de posts normalizados con campo 'posinset'

        Returns:
            Lista ordenada por posinset DESC (N, N-1, ..., 2, 1)
        """
        # Ordenar por posinset descendente, posts sin posinset van al principio
        sorted_posts = sorted(posts, key=lambda x: x.get('posinset', 0), reverse=True)

        print(f"   📊 Posts ordenados por posinset (viejo→nuevo para ID alto=reciente):")
        for i, post in enumerate(sorted_posts[:5]):  # Mostrar solo primeros 5
            posinset = post.get('posinset', 'N/A')
            author = post.get('author', 'Sin autor')[:20]
            print(f"      {i+1}. posinset={posinset} | {author}")

        if len(sorted_posts) > 5:
            print(f"      ... y {len(sorted_posts) - 5} posts más")

        return sorted_posts

    def save_extracted_posts(self, extracted_posts: List[Dict[str, Any]], group_id: int, user_id: int = 1,
                           is_test: bool = False, is_first_run: bool = False) -> Dict[str, Any]:
        """
        Guarda posts extraídos del FacebookPostExtractor validando contenido y duplicados.
        Los posts se guardan desde el índice más alto (más viejo) al 0 (más nuevo).

        Args:
            extracted_posts: Lista de posts del extractor (índice 0 = más reciente)
            group_id: ID del grupo
            user_id: ID del usuario (por defecto 1)
            is_test: Si es True, limita a máximo 10 posts para pruebas
            is_first_run: Si es True, no verifica post límite (para primera ejecución)

        Returns:
            Dict con estadísticas del guardado
        """
        if not extracted_posts:
            return {
                'success': True,
                'saved_count': 0,
                'skipped_count': 0,
                'duplicate_count': 0,
                'invalid_count': 0,
                'details': []
            }
        
        print(f"💾 Iniciando save_extracted_posts: {len(extracted_posts)} posts para grupo {group_id}")
        if is_test:
            print(f"🧪 Modo TEST activado: limitando a máximo 10 posts")
        if is_first_run:
            print(f"🚀 Modo FIRST_RUN activado: sin verificación de post límite")

        # 1. Aplicar límite de test si está activado
        posts_to_process = extracted_posts
        if is_test and len(extracted_posts) > 10:
            posts_to_process = extracted_posts[:10]
            print(f"   📏 Posts limitados de {len(extracted_posts)} a {len(posts_to_process)} por modo test")

        # 2. Normalizar posts del extractor a formato BD
        normalized_posts = [self._normalize_extracted_post(post) for post in posts_to_process]
        
        # 2. Validar contenido mínimo (5 palabras)
        valid_posts = []
        invalid_count = 0
        
        for i, post in enumerate(normalized_posts):
            if self._validate_post_content(post.get('content', ''), min_words=5):
                valid_posts.append(post)
            else:
                invalid_count += 1
                print(f"   ⚠️ Post {i} descartado: contenido insuficiente")
        
        print(f"   ✅ Posts válidos: {len(valid_posts)}/{len(posts_to_process)}")

        # 3. Verificar duplicados dentro de la ronda
        dedup_result = self._check_internal_duplicates(valid_posts, threshold=0.8)
        unique_posts = dedup_result['unique_posts']
        duplicate_count = dedup_result['stats']['duplicates_count']

        print(f"   🔍 Duplicados internos encontrados: {duplicate_count}")
        print(f"   ✅ Posts únicos para guardar: {len(unique_posts)}")

        # 4. Obtener posts existentes del grupo para verificar duplicados con BD
        # En first_run no verificamos duplicados contra BD porque no hay posts previos
        existing_posts = []
        if not is_first_run:
            existing_posts = self.get_recent_posts_by_group(group_id, limit=100)
            print(f"   📋 Posts existentes en BD: {len(existing_posts)}")
            # LOGGING DETALLADO: Mostrar algunos posts existentes
            if existing_posts:
                print(f"   🔍 LOGGING: Primeros posts en BD para comparación:")
                for i, existing in enumerate(existing_posts[:2], 1):
                    print(f"      {i}. {existing.get('author', 'Sin autor')}: {existing.get('content', '')[:40]}...")
        else:
            print(f"   🚀 Saltando verificación de duplicados BD (first_run)")
        
        # 5. Ordenar posts por posinset antes de guardar
        # posinset alto (más viejo) se guarda primero → ID bajo
        # posinset bajo (más nuevo) se guarda último → ID alto = más reciente
        posts_to_save = self._sort_posts_by_posinset(unique_posts)
        
        saved_count = 0
        skipped_count = 0
        details = []
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for i, post in enumerate(posts_to_save):
                try:
                    # LOGGING DETALLADO: Mostrar post que se va a verificar
                    post_author = post.get('author', 'Sin autor')
                    post_content = post.get('content', '')[:50]
                    print(f"   🔍 LOGGING: Verificando post {i}: {post_author}: {post_content}...")

                    # Verificar duplicados contra BD existente
                    is_duplicate_in_db = self._check_duplicate_against_db(
                        post, existing_posts, threshold=0.8
                    )

                    # LOGGING DETALLADO: Resultado de verificación
                    print(f"   🔍 LOGGING: _check_duplicate_against_db retornó: {is_duplicate_in_db}")

                    if is_duplicate_in_db:
                        skipped_count += 1
                        details.append({
                            'index': i,
                            'action': 'skipped',
                            'reason': 'duplicate_in_database',
                            'author': post.get('author', '')[:30]
                        })
                        print(f"   ⏭️ Post {i} omitido: duplicado en BD")
                        print(f"   🔍 LOGGING: DUPLICADO DETECTADO - Post omitido correctamente")
                        continue
                    
                    # Guardar post
                    cursor.execute("""
                        INSERT INTO posts (
                            user_id, group_id, content, author, author_link, timestamp,
                            links, images, reactions, comments, is_processed_and_sent
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        user_id,
                        group_id,
                        post.get('content', ''),
                        post.get('author', ''),
                        post.get('author_link', ''),
                        post.get('timestamp'),
                        json.dumps(post.get('links', [])),
                        json.dumps(post.get('images', [])),
                        post.get('reactions', 0),
                        post.get('comments', 0),
                        post.get('is_processed_and_sent', False)
                    ))
                    
                    saved_count += 1
                    details.append({
                        'index': i,
                        'action': 'saved',
                        'reason': 'valid_unique_post',
                        'author': post.get('author', '')[:30]
                    })
                    print(f"   ✅ Post {i} guardado: {post.get('author', '')[:30]}")
                    
                except Exception as e:
                    skipped_count += 1
                    details.append({
                        'index': i,
                        'action': 'error',
                        'reason': f'database_error: {str(e)}',
                        'author': post.get('author', '')[:30]
                    })
                    print(f"   ❌ Error guardando post {i}: {e}")
                    continue
            
            conn.commit()
        
        result = {
            'success': True,
            'saved_count': saved_count,
            'skipped_count': skipped_count,
            'duplicate_count': duplicate_count,
            'invalid_count': invalid_count,
            'details': details
        }
        
        print(f"📊 save_extracted_posts completado: {saved_count} guardados, {skipped_count} omitidos")
        return result
    
    def get_recent_posts_by_group(self, group_id: int, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Obtiene posts recientes de un grupo específico.
        
        Args:
            group_id: ID del grupo
            limit: Número máximo de posts a obtener
            
        Returns:
            Lista de posts recientes
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM posts
                WHERE group_id = ?
                ORDER BY id DESC
                LIMIT ?
            """, (group_id, limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def retrieve_limit_post(self, group_id: int) -> Optional[Dict[str, Any]]:
        """
        Obtiene el post más reciente de un grupo para usar como límite.
        Devuelve grupo, texto y autor para pasarlo a we_found_the_limit_post.
        
        Args:
            group_id: ID del grupo
            
        Returns:
            Dict con datos del post límite o None si no hay posts
        """
        print(f"🔍 Obteniendo post límite para grupo {group_id}")
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Obtener el post más reciente del grupo (ID más alto = más reciente)
            cursor.execute("""
                SELECT content, author, timestamp, created_at
                FROM posts
                WHERE group_id = ?
                ORDER BY id DESC
                LIMIT 1
            """, (group_id,))
            
            row = cursor.fetchone()
            
            if row:
                limit_post = {
                    'group_id': group_id,
                    'content': row['content'],
                    'text': row['content'],  # Alias para compatibilidad
                    'author': row['author'],
                    'timestamp': row['timestamp'],
                    'created_at': row['created_at']
                }
                
                print(f"   ✅ Post límite encontrado: {row['author'][:30] if row['author'] else 'Sin autor'}")
                return limit_post
            else:
                print(f"   ℹ️ No hay posts previos en el grupo {group_id}")
                return None
    
    def we_found_the_limit_post(self, content: str, author: Optional[str] = None,
                               stored_posts: Optional[List[Dict[str, Any]]] = None,
                               threshold: float = 0.8) -> bool:
        """
        Verifica si hemos encontrado el post límite (último post obtenido previamente).
        
        Args:
            content: Contenido del post actual
            author: Autor del post (opcional para mayor precisión)
            stored_posts: Lista de posts almacenados en la base de datos
            threshold: Umbral de similitud Jaccard (por defecto 0.8)
            
        Returns:
            bool: True si encontramos el post límite, False en caso contrario
        """
        if not stored_posts:
            return False
        
        # Buscar el post más reciente (índice 0 según la especificación)
        if len(stored_posts) == 0:
            return False
        
        # El post más reciente debería estar en el índice 0
        most_recent_post = stored_posts[0]
        stored_content = most_recent_post.get('content', '') or most_recent_post.get('text', '')
        stored_author = most_recent_post.get('author', '')
        
        # Calcular similitud de contenido
        content_similarity = self._calculate_jaccard(content, stored_content)

        # Si tenemos autor, verificar también similitud de autor
        if author and stored_author:
            author_similarity = self._calculate_jaccard(author, stored_author)
            # Requerir alta similitud tanto en contenido como en autor
            return content_similarity >= threshold and author_similarity >= 0.9
        else:
            # Solo verificar contenido si no tenemos información de autor
            return content_similarity >= threshold

    def _normalize_extracted_post(self, extracted_post: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normaliza un post extraído del FacebookPostExtractor a formato de BD.

        Mapeo de campos:
        - nombreAutor → author
        - contenidoPost → content
        - linkAutor → author_link
        - linkPost → links (como array)
        - imagenes → images
        - posinset → mantener para orden
        - timestamp → timestamp

        Args:
            extracted_post: Post del extractor

        Returns:
            Dict: Post normalizado para BD
        """
        # Mapear campos del extractor a formato BD
        content = extracted_post.get('contenidoPost', '').strip()
        author = extracted_post.get('nombreAutor', '').strip()
        author_link = extracted_post.get('linkAutor', '')

        # Procesar links - convertir linkPost a array si existe
        links = []
        link_post = extracted_post.get('linkPost', '')
        if link_post and link_post != 'No encontrado':
            links.append(link_post)

        # Procesar imágenes
        images = extracted_post.get('imagenes', [])
        if not isinstance(images, list):
            images = []

        # Crear post normalizado
        normalized_post = {
            'content': content,
            'author': author,
            'author_link': author_link,
            'timestamp': extracted_post.get('timestamp'),
            'links': links,
            'images': images,
            'reactions': 0,  # No extraído por el momento
            'comments': 0,   # No extraído por el momento
            'is_processed_and_sent': False,
            # Mantener campos adicionales del extractor
            'posinset': extracted_post.get('posinset'),
            'processingTime': extracted_post.get('processingTime')
        }

        return normalized_post

    def _validate_post_content(self, content: str, min_words: int = 5) -> bool:
        """
        Valida que el contenido del post cumpla con los requisitos mínimos.

        Args:
            content: Contenido del post a validar
            min_words: Número mínimo de palabras requeridas (por defecto 5)

        Returns:
            bool: True si el post es válido, False en caso contrario
        """
        if not content or not content.strip():
            return False

        # Contar palabras (separadas por espacios)
        words = content.strip().split()
        return len(words) >= min_words

    def _calculate_jaccard(self, text1: str, text2: str) -> float:
        """
        Calcula la similitud Jaccard entre dos textos.

        Args:
            text1: Primer texto a comparar
            text2: Segundo texto a comparar

        Returns:
            float: Coeficiente de Jaccard (0.0 a 1.0)
        """
        # Normalizar textos: minúsculas, sin puntuación extra, espacios múltiples
        def normalize_text(text: str) -> str:
            # Convertir a minúsculas
            text = text.lower()
            # Remover caracteres especiales pero mantener espacios y letras
            text = re.sub(r'[^\w\s]', ' ', text)
            # Normalizar espacios múltiples
            text = re.sub(r'\s+', ' ', text)
            return text.strip()

        # Normalizar ambos textos
        normalized_text1 = normalize_text(text1)
        normalized_text2 = normalize_text(text2)

        # Manejar casos extremos
        if not normalized_text1 and not normalized_text2:
            return 1.0  # Ambos vacíos = idénticos

        if not normalized_text1 or not normalized_text2:
            return 0.0  # Uno vacío, otro no = completamente diferentes

        # Crear conjuntos de palabras
        words1 = set(normalized_text1.split())
        words2 = set(normalized_text2.split())

        # Calcular intersección y unión
        intersection = words1.intersection(words2)
        union = words1.union(words2)

        # Evitar división por cero
        if len(union) == 0:
            return 0.0

        # Calcular coeficiente de Jaccard
        jaccard_coefficient = len(intersection) / len(union)

        return jaccard_coefficient

    def _check_internal_duplicates(self, posts: List[Dict[str, Any]],
                                 threshold: float = 0.8) -> Dict[str, Any]:
        """
        Verifica duplicados dentro de una ronda de posts del mismo grupo.
        Usa thresholds dinámicos basados en autor:
        - Mismo autor: 45% (más estricto, evita spam del mismo usuario)
        - Diferentes autores: 65% (moderado, evita contenido muy similar)

        Args:
            posts: Lista de posts a verificar
            threshold: Umbral legacy para compatibilidad (no se usa en lógica actual)

        Returns:
            Dict con posts únicos y estadísticas de duplicados
        """
        if not posts:
            return {
                'unique_posts': [],
                'discarded_posts': [],
                'stats': {
                    'total_input': 0,
                    'unique_count': 0,
                    'duplicates_count': 0,
                    'threshold_used': threshold
                }
            }

        unique_posts: List[Dict[str, Any]] = []
        discarded_posts: List[Dict[str, Any]] = []

        for i, current_post in enumerate(posts):
            current_content = current_post.get('content', '') or current_post.get('text', '')

            is_duplicate = False

            # Comparar con posts ya aceptados como únicos
            for unique_post in unique_posts:
                unique_content = unique_post.get('content', '') or unique_post.get('text', '')

                # Obtener autores para threshold dinámico
                current_author = current_post.get('author', '').strip()
                unique_author = unique_post.get('author', '').strip()

                # Threshold dinámico basado en autor
                if current_author and unique_author and current_author == unique_author:
                    # Mismo autor: threshold más estricto (45%)
                    dynamic_threshold = 0.45
                    author_comparison = "mismo_autor"
                else:
                    # Diferentes autores: threshold moderado (65%)
                    dynamic_threshold = 0.65
                    author_comparison = "diferentes_autores"

                # Calcular similitud de contenido
                content_similarity = self._calculate_jaccard(current_content, unique_content)

                # Si la similitud supera el umbral dinámico, es un duplicado
                if content_similarity >= dynamic_threshold:
                    is_duplicate = True
                    discarded_posts.append({
                        'original_index': i,
                        'post': current_post,
                        'similar_to_index': unique_post.get('index', 'unknown'),
                        'similarity_score': content_similarity,
                        'threshold_used': dynamic_threshold,
                        'author_comparison': author_comparison,
                        'reason': f'content_similarity_{author_comparison}'
                    })
                    break

            # Si no es duplicado, agregarlo a únicos
            if not is_duplicate:
                # Agregar índice para referencia
                current_post['index'] = i
                unique_posts.append(current_post)

        return {
            'unique_posts': unique_posts,
            'discarded_posts': discarded_posts,
            'stats': {
                'total_input': len(posts),
                'unique_count': len(unique_posts),
                'duplicates_count': len(discarded_posts),
                'threshold_same_author': 0.45,
                'threshold_different_authors': 0.65,
                'threshold_legacy': threshold  # Para compatibilidad
            }
        }

    def _check_duplicate_against_db(self, post: Dict[str, Any],
                                  existing_posts: List[Dict[str, Any]],
                                  threshold: float = 0.8) -> bool:
        """
        Verifica si un post es duplicado contra posts existentes en BD.
        Usa thresholds dinámicos basados en autor:
        - Mismo autor: 45% (más estricto, evita spam del mismo usuario)
        - Diferentes autores: 80% (threshold original para mayor precisión)

        Args:
            post: Post a verificar
            existing_posts: Posts existentes en BD
            threshold: Umbral de similitud por defecto (80% para diferentes autores)

        Returns:
            bool: True si es duplicado
        """
        if not existing_posts:
            print(f"      🔍 LOGGING: No hay posts existentes para comparar")
            return False

        post_content = post.get('content', '')
        post_author = post.get('author', 'Sin autor').strip()

        print(f"      🔍 LOGGING: Comparando contra {len(existing_posts)} posts existentes")
        print(f"      🔍 LOGGING: Post actual - {post_author}: {post_content[:30]}...")

        for i, existing_post in enumerate(existing_posts):
            existing_content = existing_post.get('content', '')
            existing_author = existing_post.get('author', 'Sin autor').strip()

            # Threshold dinámico basado en autor
            if post_author and existing_author and post_author == existing_author:
                # Mismo autor: threshold más estricto (45%)
                dynamic_threshold = 0.45
                author_comparison = "mismo_autor"
            else:
                # Diferentes autores: threshold original (80%)
                dynamic_threshold = threshold
                author_comparison = "diferentes_autores"

            # Calcular similitud de contenido
            content_similarity = self._calculate_jaccard(post_content, existing_content)

            # LOGGING DETALLADO: Mostrar cada comparación
            print(f"      🔍 LOGGING: vs Post {i+1} ({existing_author}): similitud = {content_similarity:.3f} (threshold: {dynamic_threshold:.1%} {author_comparison})")

            # Si la similitud supera el umbral dinámico, es duplicado
            if content_similarity >= dynamic_threshold:
                print(f"      🔍 LOGGING: DUPLICADO ENCONTRADO! {content_similarity:.3f} >= {dynamic_threshold:.3f} ({author_comparison})")
                return True

        print(f"      🔍 LOGGING: No se encontraron duplicados")
        return False

    def test_save_with_json_data(self, json_path: str, group_id: Optional[int] = None, user_id: int = 1,
                                is_test: bool = True, is_first_run: bool = False) -> Dict[str, Any]:
        """
        Función de test para probar el flujo de guardado usando datos de un archivo JSON.
        Lee posts desde un JSON generado por el extractor y los procesa a través del flujo real de guardado.
        
        Si no se proporciona group_id, intenta obtenerlo de la URL del grupo en el JSON
        usando la base de datos para buscar el grupo correspondiente.
        
        Args:
            json_path: Ruta al archivo JSON con posts extraídos
            group_id: ID del grupo (opcional - se obtendrá de la URL del JSON si no se proporciona)
            user_id: ID del usuario (por defecto 1)
            is_test: Si es True, limita a máximo 10 posts para pruebas
            is_first_run: Si es True, no verifica post límite (para primera ejecución)
            
        Returns:
            Dict con estadísticas del guardado y información adicional de test
        """
        print(f"🧪 TEST: Iniciando test_save_with_json_data")
        print(f"   📁 JSON Path: {json_path}")
        print(f"   🎯 Group ID inicial: {group_id}, User ID: {user_id}")
        print(f"   🔧 Flags: is_test={is_test}, is_first_run={is_first_run}")
        
        # 1. Validar que el archivo JSON existe
        json_file = Path(json_path)
        if not json_file.exists():
            error_msg = f"Archivo JSON no encontrado: {json_path}"
            print(f"   ❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'stage': 'file_validation'
            }
        
        # 2. Leer y parsear el archivo JSON
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print(f"   ✅ JSON cargado correctamente")
            
        except Exception as e:
            error_msg = f"Error leyendo JSON: {str(e)}"
            print(f"   ❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'stage': 'json_parsing'
            }
        
        # 3. Extraer información del JSON
        meta = json_data.get('meta', {})
        posts_from_json = json_data.get('posts', [])
        
        if not posts_from_json:
            print(f"   ⚠️ No se encontraron posts en el JSON")
            return {
                'success': True,
                'saved_count': 0,
                'skipped_count': 0,
                'duplicate_count': 0,
                'invalid_count': 0,
                'details': [],
                'json_meta': meta,
                'stage': 'empty_posts'
            }
        
        print(f"   📊 Posts en JSON: {len(posts_from_json)}")
        print(f"   📋 Meta información: {meta}")
        
        # 4. Extraer información del grupo del JSON si está disponible
        group_info = meta.get('group_info', {})
        if group_info:
            print(f"   🏷️ Grupo info del JSON: {group_info.get('name', 'N/A')} ({group_info.get('slug', 'N/A')})")
        
        # 5. Obtener group_id real de la base de datos si no se proporcionó
        final_group_id = group_id
        if final_group_id is None:
            # Intentar obtener group_id desde la URL en el JSON
            group_url = group_info.get('url')
            if group_url:
                print(f"   🔍 Buscando grupo en BD por URL: {group_url}")
                
                # Importar DatabaseManager para buscar el grupo
                from database.database_manager import DatabaseManager
                db_manager = DatabaseManager()
                
                # Buscar grupo por URL exacta primero
                group_record = db_manager.get_group_by_url(group_url, user_id)
                
                # Si no encuentra por URL exacta, probar variantes comunes
                if not group_record:
                    # Crear variantes de URL para buscar
                    url_variants = []
                    
                    # Si la URL tiene parámetros, probar sin ellos
                    if '?' in group_url:
                        clean_url = group_url.split('?')[0]
                        url_variants.append(clean_url)
                        # También probar con slash final
                        if not clean_url.endswith('/'):
                            url_variants.append(clean_url + '/')
                    else:
                        # Si no tiene parámetros, probar con parámetros comunes
                        url_variants.append(group_url + '?sorting_setting=CHRONOLOGICAL')
                        # También probar sin slash final si lo tiene
                        if group_url.endswith('/'):
                            url_variants.append(group_url.rstrip('/'))
                    
                    print(f"   🔍 Probando variantes de URL: {url_variants}")
                    
                    # Probar cada variante
                    for variant_url in url_variants:
                        group_record = db_manager.get_group_by_url(variant_url, user_id)
                        if group_record:
                            print(f"   ✅ Grupo encontrado con URL variante: {variant_url}")
                            break
                
                if group_record:
                    final_group_id = group_record['id']
                    print(f"   ✅ Grupo encontrado en BD - ID: {final_group_id}, Nombre: {group_record.get('nombre', 'N/A')}")
                else:
                    # El grupo no se encontró en la BD
                    error_msg = f"Grupo no encontrado en BD para URL: {group_url} ni sus variantes"
                    print(f"   ❌ {error_msg}")
                    print(f"   💡 Usa métodos específicos para agregar grupos nuevos.")
                    return {
                        'success': False,
                        'error': error_msg,
                        'stage': 'group_not_found'
                    }
            else:
                error_msg = "No se encontró URL del grupo en el JSON y no se proporcionó group_id"
                print(f"   ❌ {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'stage': 'group_id_resolution'
                }
        
        print(f"   🎯 Group ID final para procesamiento: {final_group_id}")
        
        # 6. Procesar posts a través del flujo real de guardado
        # Los posts del JSON ya están en formato del extractor, así que los pasamos directamente
        print(f"   🔄 Procesando posts a través del flujo real de guardado...")
        
        try:
            save_result = self.save_extracted_posts(
                extracted_posts=posts_from_json,
                group_id=final_group_id,
                user_id=user_id,
                is_test=is_test,
                is_first_run=is_first_run
            )
            
            # 7. Agregar información adicional de test al resultado
            save_result.update({
                'json_source': str(json_file),
                'json_meta': meta,
                'group_info_from_json': group_info,
                'original_posts_in_json': len(posts_from_json),
                'group_id_used': final_group_id,
                'group_id_auto_resolved': group_id is None,
                'test_mode': True,
                'stage': 'completed'
            })
            
            print(f"🧪 TEST: Flujo de guardado completado exitosamente")
            print(f"   📊 Resultado final: {save_result['saved_count']} guardados, {save_result['skipped_count']} omitidos")
            
            return save_result
            
        except Exception as e:
            error_msg = f"Error en flujo de guardado: {str(e)}"
            print(f"   ❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'stage': 'save_flow_error',
                'json_source': str(json_file),
                'json_meta': meta,
                'original_posts_in_json': len(posts_from_json)
            }
