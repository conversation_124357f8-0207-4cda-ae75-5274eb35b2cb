"""
Queue Processor - Orquestador principal del procesamiento de grupos
Maneja el flujo completo: shuffle, estado temporal, procesamiento y finalización
"""

import random
from typing import List, Dict, Any
from group_operations import GroupOperations
from database.database_manager import DatabaseManager


class QueueProcessor:
    """
    Orquestador principal que coordina todo el flujo de procesamiento de grupos
    """

    def __init__(self, user_id: int = 1, page=None):
        self.user_id = user_id
        self.page = page
        self.group_ops = GroupOperations()
        # Lógica de posts removida del núcleo: PostOperations migrado a flow.protos_deprecated
        self.db_manager = DatabaseManager()

        # Estado en memoria para procesamiento
        self.currently_processing = None
        self.processed_groups: set[int] = set()
    
    def process_queue(self) -> None:
        """
        Método principal que ejecuta todo el flujo de procesamiento
        """
        print("🚀 Iniciando Queue Processor...")
        
        # 1. Obtener grupos con in_flow = true
        groups = self.db_manager.get_groups_in_flow(self.user_id)
        print(f"📋 Grupos encontrados con in_flow=true: {len(groups)}")
        
        if not groups:
            print("❌ No hay grupos para procesar")
            return
        
        # 2. Shuffle de grupos
        shuffled_groups = self._shuffle_groups(groups)
        print(f"🔀 Grupos mezclados aleatoriamente: {len(shuffled_groups)}")
        
        # 3. Procesar cada grupo uno por uno
        for i, group in enumerate(shuffled_groups, 1):
            print(f"\n--- Procesando grupo {i}/{len(shuffled_groups)} ---")

            # SOLO PROCESAR EL PRIMER GRUPO REALMENTE
            if i == 1 and self.page:
                print(f"🎯 PROCESANDO PRIMER GRUPO CON NAVEGACIÓN REAL")

                # Buscar un grupo con ID numérico para mayor confiabilidad
                numeric_group = self._find_numeric_group_id(shuffled_groups)
                if numeric_group:
                    print(f"🔢 Usando grupo con ID numérico para mayor confiabilidad")
                    self._process_single_group_real(numeric_group)
                else:
                    print(f"⚠️ No se encontró grupo con ID numérico, usando el primero")
                    self._process_single_group_real(group)
                break  # Solo procesar el primer grupo para observación
            else:
                self._process_single_group(group)
        
        print("\n✅ Queue Processor completado")

        # OBSERVACIÓN REAL: Después de placeholders, observar página actual
        print(f"\n🔍 INICIANDO OBSERVACIÓN REAL DE LA PÁGINA")
        print(f"=" * 60)
        self._perform_real_page_observation()
    
    def _shuffle_groups(self, groups: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Mezcla aleatoriamente la lista de grupos
        """
        shuffled = groups.copy()
        random.shuffle(shuffled)
        print(f"🎲 Aplicando shuffle a {len(groups)} grupos")
        return shuffled
    
    def _process_single_group(self, group: Dict[str, Any]) -> None:
        """
        Procesa un grupo individual (versión núcleo sin extracción de posts).
        
        Nota:
            Toda la lógica de extracción y procesamiento de posts ha sido migrada
            al espacio flow.protos_deprecated mientras se redefine el enfoque.
        """
        group_id = group.get('id')
        group_name = group.get('name', 'Sin nombre')
        
        print(f"🎯 (CORE) Procesando grupo: {group_name} (ID: {group_id})")
        
        try:
            # 1. Marcar como en procesamiento (estado en memoria)
            self.currently_processing = group_id
            print("📝 Marcado en memoria como en procesamiento")

            # 2. Navegar a la página del grupo (best-effort si hay page real configurada en GroupOperations)
            try:
                self.group_ops.navigate_to_group(str(group_id))
                print("🌐 Navegación solicitada a la página del grupo (sin extracción de posts)")
            except Exception as nav_err:
                print(f"⚠️ Navegación no disponible en contexto actual: {nav_err}")

            # 3. (Extracción de posts removida)
            print("ℹ️ Extracción de posts omitida (subdominio migrado)")

            # 4. Marcar como completado
            if group_id is not None:
                self.processed_groups.add(group_id)
            self.currently_processing = None
            print(f"✅ Grupo {group_name} marcado como procesado (sin posts)")

        except Exception as e:
            print(f"❌ Error procesando grupo {group_name}: {str(e)}")
            self.currently_processing = None

    def _process_single_group_real(self, group: Dict[str, Any]) -> None:
        """
        Procesa un grupo individual con navegación REAL para observación
        """
        group_id = group.get('id')
        group_name = group.get('name', 'Sin nombre')
        group_url = group.get('url', '')

        print(f"🎯 Iniciando procesamiento REAL del grupo: {group_name} (ID: {group_id})")
        print(f"🔗 URL del grupo: {group_url}")

        # Extraer el group_id real de la URL de Facebook
        facebook_group_id = self._extract_facebook_group_id(group_url)
        if not facebook_group_id:
            print(f"❌ No se pudo extraer el group_id de Facebook de la URL: {group_url}")
            return

        print(f"🆔 Group ID de Facebook extraído: {facebook_group_id}")

        try:
            # 1. Marcar como en procesamiento
            print("📝 Marcando grupo como en procesamiento (memoria)")
            self.currently_processing = group_id

            # 2. Navegar REALMENTE al grupo
            print("🌐 Navegando REALMENTE a la página del grupo")
            # Establecer la página en group_ops y navegar
            self.group_ops.set_page(self.page)
            navigation_success = self.group_ops.navigate_to_group(facebook_group_id)

            if navigation_success:
                print(f"✅ Navegación exitosa al grupo {facebook_group_id}")

                # 3. OBSERVACIÓN REAL DE LA PÁGINA DEL GRUPO
                print(f"\n🔍 INICIANDO OBSERVACIÓN REAL DEL GRUPO {facebook_group_id}")
                print(f"=" * 60)
                self._perform_real_group_observation(facebook_group_id)

            else:
                print(f"❌ Error en navegación al grupo {facebook_group_id}")

            # 4. Marcar como procesado
            if group_id is not None:
                self.processed_groups.add(group_id)
            self.currently_processing = None
            print(f"🎉 Grupo {group_name} procesado exitosamente")

        except Exception as e:
            print(f"❌ Error procesando grupo {group_name}: {str(e)}")
            self.currently_processing = None

    def _extract_facebook_group_id(self, group_url: str) -> str | None:
        """
        Extrae el group_id de Facebook de una URL de grupo

        Args:
            group_url: URL del grupo de Facebook

        Returns:
            str: Group ID extraído o None si no se puede extraer
        """
        if not group_url or '/groups/' not in group_url:
            return None

        try:
            # Para URLs como https://www.facebook.com/groups/118581375315200/
            parts = group_url.split('/groups/')
            if len(parts) > 1:
                group_id = parts[1].rstrip('/')
                # Remover cualquier parámetro adicional
                if '?' in group_id:
                    group_id = group_id.split('?')[0]
                if '/' in group_id:
                    group_id = group_id.split('/')[0]
                return group_id
        except Exception as e:
            print(f"❌ Error extrayendo group_id de {group_url}: {e}")

        return None

    def _find_numeric_group_id(self, groups: List[Dict[str, Any]]) -> Dict[str, Any] | None:
        """
        Busca un grupo que tenga un ID numérico de Facebook para mayor confiabilidad

        Args:
            groups: Lista de grupos

        Returns:
            Dict: Primer grupo con ID numérico o None si no se encuentra
        """
        for group in groups:
            group_url = group.get('url', '')
            facebook_group_id = self._extract_facebook_group_id(group_url)

            if facebook_group_id and facebook_group_id.isdigit():
                print(f"🔢 Grupo con ID numérico encontrado: {facebook_group_id}")
                return group

        return None

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Obtiene estadísticas del estado de procesamiento
        """
        stats = self.db_manager.get_processing_statistics(self.user_id)
        stats.update({
            "currently_processing": self.currently_processing,
            "processed_in_session": len(self.processed_groups)
        })
        return stats

    def _perform_real_page_observation(self):
        """
        Realiza observación real de la página actual usando Playwright
        para identificar posts y patrones conocidos
        """
        print(f"🎯 OBSERVACIÓN DESDE ABSTRACCIÓN MÁS ALTA")
        print(f"📍 Objetivo: Identificar posts o grupos de posts en página actual")
        print(f"🔍 Métodos: Usar patrones conocidos del facebook-scraper-v2.1.js")
        print(f"")

        # TODO: Aquí se implementará la observación real con self.page
        # Por ahora, mostrar qué se haría

        print(f"📋 PATRONES A EVALUAR:")
        print(f"   1. 🎯 Selector principal: [data-ad-rendering-role='story_message']")
        print(f"   2. 🔍 Contenedores de posts: div[role='article']")
        print(f"   3. 📄 Enlaces de posts: a[href*='/posts/'], a[href*='story.php']")
        print(f"   4. 👤 Autores: a[aria-label], svg[aria-label]")
        print(f"   5. 🖼️ Imágenes: img[src*='scontent']")
        print(f"")

        print(f"🚀 EVALUACIÓN CON PLAYWRIGHT:")
        print(f"   → page.locator('[data-ad-rendering-role=\"story_message\"]').count()")
        print(f"   → page.locator('div[role=\"article\"]').count()")
        print(f"   → page.locator('a[href*=\"/posts/\"]').count()")
        print(f"   → page.evaluate() para obtener contenido HTML")
        print(f"   → page.content() para análisis completo")
        print(f"")

        print(f"📊 RESULTADO ESPERADO:")
        print(f"   ✅ Identificar si hay posts visibles")
        print(f"   ✅ Contar elementos encontrados")
        print(f"   ✅ Evaluar selectores más efectivos")
        print(f"   ✅ Preparar estrategia de extracción")
        print(f"")

        print(f"🎯 PRÓXIMO PASO: Implementar evaluación real con página de Playwright")
        print(f"=" * 60)

    def _perform_real_group_observation(self, group_id: str):
        """
        Realiza observación REAL de la página del grupo usando Playwright
        """
        print(f"🎯 OBSERVACIÓN REAL DEL GRUPO {group_id}")
        print(f"📍 Página actual: {self.page.url if self.page else 'No disponible'}")
        print(f"")

        if not self.page:
            print(f"❌ No hay página de Playwright disponible")
            return

        try:
            print(f"📋 EVALUANDO PATRONES CONOCIDOS:")

            # 1. Selector principal de posts
            story_messages = self.page.locator('[data-ad-rendering-role="story_message"]').count()
            print(f"   1. 🎯 [data-ad-rendering-role='story_message']: {story_messages} elementos")

            # 2. Contenedores de posts
            article_containers = self.page.locator('div[role="article"]').count()
            print(f"   2. 🔍 div[role='article']: {article_containers} elementos")

            # 3. Enlaces de posts
            post_links = self.page.locator('a[href*="/posts/"]').count()
            story_links = self.page.locator('a[href*="story.php"]').count()
            print(f"   3. 📄 Enlaces de posts: {post_links} (/posts/) + {story_links} (story.php)")

            # 4. Autores con aria-label
            aria_links = self.page.locator('a[aria-label]').count()
            aria_svgs = self.page.locator('svg[aria-label]').count()
            print(f"   4. 👤 Autores: {aria_links} (a[aria-label]) + {aria_svgs} (svg[aria-label])")

            # 5. Imágenes de contenido
            content_images = self.page.locator('img[src*="scontent"]').count()
            print(f"   5. 🖼️ Imágenes: {content_images} (img[src*='scontent'])")

            print(f"")
            print(f"📊 RESUMEN DE OBSERVACIÓN:")
            print(f"   ✅ Página cargada: {self.page.url}")
            print(f"   📈 Total elementos detectados: {story_messages + article_containers + post_links + story_links}")
            print(f"   🎯 Selector más prometedor: [data-ad-rendering-role='story_message'] ({story_messages} elementos)")

            if story_messages > 0:
                print(f"   🚀 RECOMENDACIÓN: Usar page.locator('[data-ad-rendering-role=\"story_message\"]') para extracción")
            elif article_containers > 0:
                print(f"   🚀 RECOMENDACIÓN: Usar page.locator('div[role=\"article\"]') como alternativa")
            else:
                print(f"   ⚠️ ADVERTENCIA: No se detectaron posts visibles en la página")

        except Exception as e:
            print(f"❌ Error en observación real: {e}")

        print(f"=" * 60)




if __name__ == "__main__":
    # Ejemplo de uso
    processor = QueueProcessor()
    processor.process_queue()
