#!/usr/bin/env python3
"""
Facebook Login System - Sistema modular de login para Facebook
Contiene todas las clases especializadas en manejo de login y verificación
"""

import os
import sys
import time
import json
import random
from pathlib import Path
from typing import Dict, Optional, Tuple, Any, List
from enum import Enum
from camoufox import Camoufox
from browserforge.fingerprints import Screen
from camoufox.fingerprints import generate_fingerprint
from utils.path_utils import get_config_str, get_session_file, get_fingerprint_file, ensure_results_dir


class LoginState(Enum):
    """Estados posibles del proceso de login"""
    SUCCESS = "Login exitoso, en home"
    FAILED_CREDENTIALS = "Credenciales incorrectas"
    REQUIRES_2FA = "Requiere verificación 2FA"
    ACCOUNT_LOCKED = "Cuenta bloqueada"
    CAPTCHA_REQUIRED = "CAPTCHA requerido"
    UNKNOWN_ERROR = "Error desconocido"
    TIMEOUT = "Timeout en proceso"
    STILL_ON_LOGIN = "Aún en página de login"


class LoginDetector:
    """Detecta elementos y estados de la página de login"""
    
    def __init__(self):
        self.login_selectors = {
            "email": [
                "input[type='email']",
                "input[name='email']", 
                "input[placeholder*='correo']",
                "input[placeholder*='email']",
                "#email"
            ],
            "password": [
                "input[type='password']",
                "input[name='pass']",
                "input[name='password']",
                "#pass"
            ],
            "login_button": [
                "button[type='submit']",
                "button[name='login']",
                "input[type='submit']",
                "[data-testid='royal_login_button']"
            ]
        }
        
        self.home_indicators = [
            "[data-testid='feed']",
            "[role='main']",
            "[role='article']",
            "div[data-pagelet='FeedUnit']"
        ]
        
        self.error_indicators = [
            "[data-testid='login_error']",
            ".login_error_box",
            "[role='alert']"
        ]
    
    def find_login_elements(self, page) -> Dict[str, Optional[Any]]:
        """Encuentra elementos de login en la página"""
        elements = {"email": None, "password": None, "login_button": None}
        
        for element_type, selectors in self.login_selectors.items():
            for selector in selectors:
                try:
                    element = page.locator(selector).first
                    if element.count() > 0:
                        elements[element_type] = element
                        break
                except:
                    continue
        
        return elements
    
    def detect_page_state(self, page) -> Dict[str, Any]:
        """Detecta el estado actual de la página"""
        state = {
            "url": page.url,
            "is_login_page": False,
            "is_home_page": False,
            "has_errors": False,
            "login_elements_found": 0,
            "home_elements_found": 0
        }
        
        # Detectar página de login
        login_elements = self.find_login_elements(page)
        state["login_elements_found"] = sum(1 for el in login_elements.values() if el is not None)
        state["is_login_page"] = state["login_elements_found"] >= 2
        
        # Detectar página de home
        for indicator in self.home_indicators:
            try:
                if page.locator(indicator).count() > 0:
                    state["home_elements_found"] += 1
            except:
                pass
        
        state["is_home_page"] = state["home_elements_found"] > 0
        
        # Detectar errores
        for error_selector in self.error_indicators:
            try:
                if page.locator(error_selector).count() > 0:
                    state["has_errors"] = True
                    break
            except:
                pass
        
        return state


class StateAnalyzer:
    """Analiza estados complejos de la página"""
    
    def __init__(self):
        self.detector = LoginDetector()
    
    def analyze_login_result(self, page, timeout: int = 10) -> Tuple[LoginState, str]:
        """
        Analiza el resultado después de intentar login
        
        Returns:
            Tuple[LoginState, mensaje_descriptivo]
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            state = self.detector.detect_page_state(page)
            
            # Verificar si llegamos al home
            if state["is_home_page"]:
                return LoginState.SUCCESS, "✅ Login exitoso, llegamos al home"
            
            # Verificar errores
            if state["has_errors"]:
                return LoginState.FAILED_CREDENTIALS, "❌ Error de credenciales detectado"
            
            # Verificar si seguimos en login
            if state["is_login_page"]:
                # Buscar indicadores específicos de problemas
                if "checkpoint" in page.url.lower():
                    return LoginState.REQUIRES_2FA, "🔐 Requiere verificación 2FA"
                
                if "captcha" in page.url.lower():
                    return LoginState.CAPTCHA_REQUIRED, "🤖 CAPTCHA requerido"
            
            time.sleep(0.5)
        
        # Si llegamos aquí, timeout
        final_state = self.detector.detect_page_state(page)
        if final_state["is_login_page"]:
            return LoginState.STILL_ON_LOGIN, "⏰ Timeout: aún en página de login"
        else:
            return LoginState.UNKNOWN_ERROR, f"⚠️ Estado desconocido: {page.url}"
    
    def verify_home_page(self, page) -> Dict[str, Any]:
        """Verifica que estamos correctamente en el home"""
        verification = {
            "is_home": False,
            "url_valid": False,
            "articles_count": 0,
            "articles_required": 2,
            "detection_time": time.time()
        }
        
        # Verificar URL
        if "facebook.com" in page.url and not any(x in page.url for x in ["login", "checkpoint", "captcha"]):
            verification["url_valid"] = True
        
        # Contar artículos del feed
        try:
            articles = page.locator("[role='article']")
            verification["articles_count"] = articles.count()
        except:
            pass
        
        # Determinar si es home válido (al menos 1 artículo como pidió el líder)
        verification["is_home"] = (
            verification["url_valid"] and
            verification["articles_count"] >= 1
        )
        
        return verification


class LoginExecutor:
    """Ejecuta acciones de login"""
    
    def __init__(self):
        self.detector = LoginDetector()
    
    def fill_credentials(self, page, email: str, password: str) -> bool:
        """
        Llena credenciales en el formulario de login
        
        Returns:
            True si se llenaron exitosamente
        """
        try:
            elements = self.detector.find_login_elements(page)
            
            if not elements["email"] or not elements["password"]:
                return False
            
            # Llenar email
            elements["email"].clear()
            elements["email"].fill(email)
            time.sleep(random.uniform(0.5, 1.5))
            
            # Llenar password
            elements["password"].clear()
            elements["password"].fill(password)
            time.sleep(random.uniform(0.5, 1.5))
            
            return True
            
        except Exception as e:
            print(f"❌ Error llenando credenciales: {e}")
            return False
    
    def submit_login(self, page) -> bool:
        """
        Envía el formulario de login
        
        Returns:
            True si se envió exitosamente
        """
        try:
            elements = self.detector.find_login_elements(page)
            
            if not elements["login_button"]:
                # Intentar con Enter en password
                password_element = elements.get("password")
                if password_element:
                    password_element.press("Enter")
                    return True
                return False
            
            # Click en botón de login
            elements["login_button"].click()
            return True
            
        except Exception as e:
            print(f"❌ Error enviando login: {e}")
            return False


class FacebookLoginManager:
    """Clase principal que maneja todo el proceso de login"""

    def __init__(self, config_path: Optional[str] = None, headless: bool = False):
        # Usar ruta absoluta si no se especifica
        if config_path is None:
            config_path = get_config_str()
        self.config_path = config_path
        self.config = self._load_config()
        self.headless = headless

        # Inicializar componentes
        self.detector = LoginDetector()
        self.analyzer = StateAnalyzer()
        self.executor = LoginExecutor()

        # Archivos de persistencia usando rutas absolutas
        ensure_results_dir()  # Asegurar que el directorio existe
        self.fingerprint_file = get_fingerprint_file()
        self.session_file = get_session_file()
        self.fingerprint = self._load_persistent_fingerprint()
    
    def _load_config(self) -> Dict:
        """Carga configuración"""
        try:
            config_path = Path(self.config_path)
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error cargando config: {e}")
            return {}
    
    def _load_persistent_fingerprint(self):
        """Carga fingerprint persistente y FUERZA pantalla a 1280x720 como antes"""
        try:
            if self.fingerprint_file.exists():
                with open(self.fingerprint_file, 'r') as f:
                    fingerprint_data = json.load(f)

                print("🔄 Usando fingerprint persistente del home tracker")

                # Cargar el fingerprint completo tal como está
                saved_os = fingerprint_data.get('navigator', {}).get('platform', 'Win32')
                os_type = 'windows' if 'win' in saved_os.lower() else 'linux'

                # Generar con pantalla original primero
                fingerprint = generate_fingerprint(os=os_type)

                # SOBREESCRIBIR solo las dimensiones de pantalla a 1280x720
                fingerprint.screen.width = 1280
                fingerprint.screen.height = 720
                fingerprint.screen.availWidth = 1280
                fingerprint.screen.availHeight = 720
                fingerprint.screen.outerWidth = 1280
                fingerprint.screen.outerHeight = 720
                fingerprint.screen.innerWidth = 1280
                fingerprint.screen.innerHeight = 720

                print(f"📐 Pantalla sobreescrita a: {fingerprint.screen.width}x{fingerprint.screen.height}")
                return fingerprint

        except Exception as e:
            print(f"⚠️ Error cargando fingerprint persistente: {e}")

        print("🆕 Generando nuevo fingerprint (no se encontró persistente)")
        screen = Screen(max_width=1280, max_height=720)
        return generate_fingerprint(os='windows', screen=screen)
    
    def get_credentials(self) -> Tuple[Optional[str], Optional[str]]:
        """Obtiene credenciales de la configuración"""
        facebook_config = self.config.get('facebook_account', {})
        email = facebook_config.get('email')
        password = facebook_config.get('password')

        if not email or not password:
            print("❌ No se encontraron credenciales en config")
            return None, None

        return email, password

    def _create_camoufox_args(self) -> Dict:
        """Crea argumentos para Camoufox con soporte headless"""
        # Configurar headless según el entorno
        headless_mode: Any = self.headless
        if self.headless and os.name == 'posix':  # Linux/Unix
            headless_mode = 'virtual'  # Usar Xvfb en Linux para mejor fingerprinting

        args = {
            'fingerprint': self.fingerprint,
            'headless': headless_mode,
            'geoip': True,
            'os': 'windows',
            'humanize': True,
            'enable_cache': True
        }

        # Agregar sesión si existe
        if self.session_file.exists():
            args['storage_state'] = str(self.session_file)
            print(f"✅ Sesión encontrada: {self.session_file}")
        else:
            print(f"❌ No se encontró sesión: {self.session_file}")

        return args

    def _save_session(self, context):
        """Guarda el estado de la sesión"""
        try:
            ensure_results_dir()  # Asegurar que el directorio existe
            context.storage_state(path=str(self.session_file))
            print(f"💾 Sesión guardada: {self.session_file}")
        except Exception as e:
            print(f"❌ Error guardando sesión: {e}")

    def _take_screenshot(self, page, name: str, timestamp: int):
        """Toma screenshot con timestamp - DESHABILITADO"""
        # Screenshots deshabilitados para optimizar rendimiento
        return None

    def login(self) -> Dict[str, Any]:
        """
        Ejecuta el proceso completo de login

        Returns:
            Dict con resultados del login
        """
        result = {
            'success': False,
            'state': LoginState.UNKNOWN_ERROR,
            'message': '',
            'duration': 0,
            'screenshots': [],
            'home_verification': {}
        }

        start_time = time.time()
        timestamp = int(start_time)

        # Obtener credenciales
        email, password = self.get_credentials()
        if not email or not password:
            result['message'] = "❌ No se encontraron credenciales válidas"
            return result

        print(f"📧 Email: {email}")
        print(f"🔐 Password: {'*' * len(password)}")

        # Crear argumentos de Camoufox
        camoufox_args = self._create_camoufox_args()

        try:
            # Separar argumentos de launch vs new_context
            launch_args = {k: v for k, v in camoufox_args.items() if k != 'storage_state'}
            context_args = {}
            if 'storage_state' in camoufox_args:
                context_args['storage_state'] = camoufox_args['storage_state']

            browser = Camoufox(**launch_args, i_know_what_im_doing=True)
            browser_started = browser.__enter__()

            if context_args:
                print("🔄 Cargando sesión guardada con storageState")
                if hasattr(browser_started, 'new_context'):
                    context = browser_started.new_context(**context_args)
                else:
                    context = browser_started  # Ya es un contexto
            else:
                print("🆕 Creando nueva sesión")
                if hasattr(browser_started, 'new_context'):
                    context = browser_started.new_context()
                else:
                    context = browser_started  # Ya es un contexto

            page = context.new_page()

            # Screenshot inicial
            print("🌐 Navegando a Facebook...")
            page.goto("https://www.facebook.com/", wait_until="domcontentloaded")

            # Usar detectores como criterio de "ya llegamos"
            from utils.page_detectors import page_detectors
            print("🔍 Verificando qué página cargó usando detectores...")

            max_attempts = 6  # 30 segundos total
            for attempt in range(max_attempts):
                detection = page_detectors.detect_current_page(page)
                current_page = detection.get('current_page', 'unknown')

                if current_page in ['home', 'login']:
                    print(f"✅ Página {current_page} detectada (intento {attempt+1})")
                    break

                if attempt < max_attempts - 1:
                    print(f"⏳ Intento {attempt+1}/{max_attempts} - esperando 5s...")
                    time.sleep(5)
            else:
                result['message'] = "❌ Error: No se pudo determinar tipo de página después de 30s"
                return result

            screenshot1 = self._take_screenshot(page, "01_initial", timestamp)
            if screenshot1:
                screenshots = result.get('screenshots', [])
                if isinstance(screenshots, list):
                    screenshots.append(screenshot1)

            # Verificar si ya estamos logueados con sesión guardada (URL + al menos 1 artículo)
            home_verification = self.analyzer.verify_home_page(page)

            if home_verification['is_home']:
                print("✅ Sesión válida detectada - ya logueado")
                result['success'] = True
                result['state'] = LoginState.SUCCESS
                result['message'] = "✅ Login exitoso con sesión guardada"
                result['home_verification'] = home_verification
            else:
                # Proceso de login manual
                print("🔐 Iniciando proceso de login...")

                # Llenar credenciales
                if self.executor.fill_credentials(page, email, password):
                    screenshot2 = self._take_screenshot(page, "02_filled", timestamp)
                    if screenshot2:
                        screenshots = result.get('screenshots', [])
                        if isinstance(screenshots, list):
                            screenshots.append(screenshot2)

                    # Enviar formulario
                    if self.executor.submit_login(page):
                        print("📤 Formulario enviado, esperando resultado...")

                        # Analizar resultado
                        login_state, message = self.analyzer.analyze_login_result(page, timeout=15)

                        screenshot3 = self._take_screenshot(page, "03_after_submit", timestamp)
                        if screenshot3:
                            screenshots = result.get('screenshots', [])
                            if isinstance(screenshots, list):
                                screenshots.append(screenshot3)

                        result['state'] = login_state
                        result['message'] = message

                        if login_state == LoginState.SUCCESS:
                            result['success'] = True
                            home_verification = self.analyzer.verify_home_page(page)
                            result['home_verification'] = home_verification

                            # Guardar sesión exitosa
                            self._save_session(context)
                    else:
                        result['message'] = "❌ No se pudo enviar el formulario de login"
                else:
                    result['message'] = "❌ No se pudieron llenar las credenciales"

            # Screenshot final
            screenshot4 = self._take_screenshot(page, "04_final", timestamp)
            if screenshot4:
                screenshots = result.get('screenshots', [])
                if isinstance(screenshots, list):
                    screenshots.append(screenshot4)

            # Retornar página para uso posterior
            result['page'] = page
            result['context'] = context
            result['browser'] = browser

        except Exception as e:
            result['message'] = f"❌ Error durante ejecución: {e}"
            print(f"❌ Error en login: {e}")

        result['duration'] = time.time() - start_time
        return result


if __name__ == "__main__":
    print("Facebook Login System - Usar como módulo desde flow_solid.py")
