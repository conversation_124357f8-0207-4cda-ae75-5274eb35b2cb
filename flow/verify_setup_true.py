#!/usr/bin/env python3
"""
Script para verificar que is_first_run se inicializa en True
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager

def verify_setup():
    print("🔍 Verificando configuración de setup...")
    
    db = DatabaseManager()
    config = db.get_setup_config()
    
    is_first_run = config.get('is_first_run')
    print(f"is_first_run = {is_first_run}")
    
    if is_first_run == 1 or is_first_run is True:
        print("✅ CORRECTO: is_first_run está establecido en True")
    else:
        print(f"❌ ERROR: is_first_run debería ser True, pero es {is_first_run}")
    
    print(f"Configuración completa: {config}")

if __name__ == "__main__":
    verify_setup()