#!/usr/bin/env python3
"""
Script de prueba para verificar la funcionalidad de la tabla setup
Verifica que la tabla se crea correctamente y que is_first_run = False
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager


def test_setup_table():
    """
    Prueba la funcionalidad de la tabla setup
    """
    print("🧪 INICIANDO PRUEBAS DE TABLA SETUP")
    print("=" * 50)
    
    # Inicializar DatabaseManager
    db = DatabaseManager()
    
    # Obtener configuración actual
    print("\n1. Obteniendo configuración inicial...")
    config = db.get_setup_config()
    print(f"   Configuración obtenida: {config}")
    
    # Verificar que is_first_run es False
    is_first_run = config.get('is_first_run', None)
    print(f"   is_first_run = {is_first_run}")
    
    if is_first_run == 0 or is_first_run is False:
        print("   ✅ is_first_run está correctamente establecido en False")
    else:
        print(f"   ❌ ERROR: is_first_run debería ser False, pero es {is_first_run}")
    
    # Probar actualización a True
    print("\n2. Probando actualización a True...")
    success = db.update_is_first_run(True)
    if success:
        print("   ✅ Actualización exitosa")
        
        # Verificar el cambio
        config_updated = db.get_setup_config()
        new_value = config_updated.get('is_first_run')
        print(f"   Nuevo valor: is_first_run = {new_value}")
        
        if new_value == 1 or new_value is True:
            print("   ✅ Valor actualizado correctamente a True")
        else:
            print(f"   ❌ ERROR: Valor no se actualizó correctamente: {new_value}")
    else:
        print("   ❌ ERROR: Actualización falló")
    
    # Restaurar a False
    print("\n3. Restaurando a False...")
    success = db.update_is_first_run(False)
    if success:
        print("   ✅ Restauración exitosa")
        
        # Verificar la restauración
        config_final = db.get_setup_config()
        final_value = config_final.get('is_first_run')
        print(f"   Valor final: is_first_run = {final_value}")
        
        if final_value == 0 or final_value is False:
            print("   ✅ Valor restaurado correctamente a False")
        else:
            print(f"   ❌ ERROR: Valor no se restauró correctamente: {final_value}")
    else:
        print("   ❌ ERROR: Restauración falló")
    
    print("\n" + "=" * 50)
    print("🏁 PRUEBAS COMPLETADAS")
    print("\n📊 RESUMEN:")
    print(f"   - Tabla setup creada: ✅")
    print(f"   - Registro inicial con is_first_run=False: ✅")
    print(f"   - Métodos de lectura funcionando: ✅")
    print(f"   - Métodos de actualización funcionando: ✅")


if __name__ == "__main__":
    test_setup_table()