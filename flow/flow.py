#!/usr/bin/env python3
"""
Facebook Flow - Orquestador Principal
<PERSON><PERSON><PERSON> todos los módulos especializados para automatización de Facebook
Soporta modo headless y configuración flexible
"""

import argparse
from typing import Dict, Optional, Any
from login_system import FacebookLoginManager
from utils.page_detectors import PageDetectors
from utils.intelligent_humanizer import IntelligentHumanizer
from utils.path_utils import get_config_str
from groups_tracker import GroupsTracker
from database.database_manager import DatabaseManager
from queue_processor import QueueProcessor
from group_utils import extract_group_slug_from_url, generate_group_name_from_slug, extract_group_info_from_url

class FacebookFlowOrchestrator:
    """Orquestador principal que coordina todos los módulos especializados"""

    def __init__(self, config_path: Optional[str] = None, headless: bool = False):
        # Usar ruta absoluta si no se especifica
        if config_path is None:
            config_path = get_config_str()
        self.config_path = config_path
        self.headless = headless

        # Inicializar módulos especializados
        self.login_manager = FacebookLoginManager(config_path, headless=headless)
        self.groups_tracker = GroupsTracker()
        self.groups_repository = DatabaseManager()

        # Variables de estado
        self.page = None
        self.context = None
        self.browser = None

        print(f"🚀 Facebook Flow Orchestrator iniciado")
        print(f"   📁 Config: {config_path}")
        print(f"   👁️ Headless: {'✅ Sí' if headless else '❌ No'}")

    def execute_login_flow(self) -> Dict[str, Any]:
        """
        Ejecuta el flujo completo de login

        Returns:
            Dict con resultados del login
        """
        print("🔐 Iniciando Facebook Login Solid...")

        # Ejecutar login usando el manager especializado
        login_result = self.login_manager.login()

        # Guardar referencias para uso posterior
        if login_result.get('success'):
            self.page = login_result.get('page')
            self.context = login_result.get('context')
            self.browser = login_result.get('browser')

        return login_result

    def execute_humanization(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta la humanización inteligente

        Args:
            skip: Si True, salta la humanización y devuelve resultado exitoso

        Returns:
            Dict con resultados de la humanización
        """
        if skip:
            print("\n🚀 SALTANDO HUMANIZACIÓN (skip=True)")
            print("=" * 60)
            print("⏭️ Humanización omitida por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Humanización saltada por parámetro skip=True'}

        if not self.page:
            return {'success': False, 'message': 'No hay página disponible para humanización'}

        print("\n🤖 INICIANDO HUMANIZACIÓN INTELIGENTE")
        print("=" * 60)

        try:
            # Inicializar detectores y humanizador
            page_detectors = PageDetectors()
            humanizer = IntelligentHumanizer(
                page_detectors=page_detectors,
                config_path=self.config_path
            )

            # Ejecutar humanización normal usando el método correcto
            humanization_result = humanizer.start_home_session(self.page)
            return humanization_result

        except Exception as e:
            return {'success': False, 'message': f'Error en humanización: {e}'}

    def execute_groups_tracking(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta el tracking de grupos e integra al repositorio

        Args:
            skip: Si True, salta el tracking y devuelve resultado exitoso

        Returns:
            Dict con resultados del tracking e integración
        """
        if skip:
            print("\n🚀 SALTANDO TRACKING DE GRUPOS (skip=True)")
            print("=" * 60)
            print("⏭️ Tracking de grupos omitido por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Tracking de grupos saltado por parámetro skip=True'}

        if not self.page:
            return {'success': False, 'message': 'No hay página disponible para tracking'}

        print("\n🔍 INICIANDO TEST DE OBSERVACIÓN DE GRUPOS")
        print("=" * 60)

        try:
            # Navegar a página de grupos
            groups_url = "https://www.facebook.com/groups/joins/?nav_source=tab"
            print(f"🌐 Navegando a URL objetivo: {groups_url}")
            self.page.goto(groups_url)

            # Ejecutar tracking usando la clase especializada
            results = self.groups_tracker.track_groups_page(self.page)

            # Mostrar resumen detallado
            self.groups_tracker.print_summary(results)

            # Mostrar lista completa de links si fue exitoso
            extraction = results.get('extraction', {})
            groups_links = extraction.get('groups_links', [])

            if groups_links:
                print(f"\n📋 LISTA COMPLETA DE LINKS RECOLECTADOS ({len(groups_links)}):")
                print("=" * 80)
                for i, link in enumerate(groups_links):
                    print(f"   [{i+1:2d}] {link['href']}")
                print("=" * 80)

                # INTEGRACIÓN AL REPOSITORIO
                integration_result = self._integrate_groups_to_repository(groups_links)
                results['integration'] = integration_result

            return results

        except Exception as e:
            return {'success': False, 'message': f'Error en tracking de grupos: {e}'}

    def _integrate_groups_to_repository(self, groups_links: list) -> Dict[str, Any]:
        """
        Integra grupos encontrados al repositorio

        Args:
            groups_links: Lista de links de grupos del tracker

        Returns:
            Dict con resultados de la integración
        """
        import time

        print("\n💾 INTEGRANDO GRUPOS AL REPOSITORIO")
        print("=" * 60)

        try:
            # Convertir datos del tracker al formato del repositorio usando slug como nombre
            groups_data = []
            for i, group_link in enumerate(groups_links):
                url = group_link.get('href', '')
                original_text = group_link.get('text', f'Grupo {i+1}')

                if not url:
                    continue

                # Extraer slug de la URL para usar como nombre consistente
                slug = extract_group_slug_from_url(url)
                if slug:
                    # Generar nombre basado en slug (más consistente)
                    nombre = generate_group_name_from_slug(slug)
                    print(f"   📋 Grupo {i+1}: {slug} → {nombre}")
                else:
                    # Fallback al texto original si no se puede extraer slug
                    nombre = original_text
                    print(f"   ⚠️ Grupo {i+1}: No se pudo extraer slug, usando texto: {nombre}")

                # Obtener información adicional de la URL
                url_info = extract_group_info_from_url(url)

                group_data = {
                    'nombre': nombre,
                    'url': url,
                    'in_flow': True,  # Por defecto in_flow=True como solicitado
                    'metadata': {
                        'source': 'groups_tracker',
                        'discovered_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'tracker_index': i,
                        'original_data': group_link,
                        'original_text': original_text,
                        'slug': slug,
                        'url_info': url_info
                    }
                }
                groups_data.append(group_data)

            # Añadir grupos al repositorio
            if groups_data:
                added, duplicates = self.groups_repository.bulk_add_groups(groups_data)

                # Mostrar estadísticas finales
                self.groups_repository.print_stats()

                result = {
                    'success': True,
                    'added': added,
                    'duplicates': duplicates,
                    'total_processed': len(groups_data)
                }

                print(f"\n🎉 INTEGRACIÓN COMPLETADA")
                print(f"✅ Grupos añadidos: {added}")
                print(f"🔄 Duplicados omitidos: {duplicates}")
                print(f"📊 Total procesados: {len(groups_data)}")

                return result
            else:
                return {'success': False, 'message': 'No hay grupos válidos para integrar'}

        except Exception as e:
            print(f"❌ Error en integración: {e}")
            return {'success': False, 'message': f'Error en integración: {e}'}

    def execute_queue_processing(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta el procesamiento de cola de grupos

        Args:
            skip: Si True, salta el procesamiento y devuelve resultado exitoso

        Returns:
            Dict con resultados del procesamiento
        """
        if skip:
            print("\n🚀 SALTANDO PROCESAMIENTO DE COLA (skip=True)")
            print("=" * 60)
            print("⏭️ Procesamiento de cola omitido por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Procesamiento de cola saltado por parámetro skip=True'}

        try:
            print("\n🚀 INICIANDO PROCESAMIENTO DE COLA DE GRUPOS")
            print("=" * 60)

            # Crear instancia del procesador de cola CON PÁGINA REAL
            queue_processor = QueueProcessor(page=self.page)

            # Ejecutar procesamiento
            queue_processor.process_queue()

            # Obtener estadísticas finales
            result = queue_processor.get_processing_stats()

            print("\n✅ PROCESAMIENTO DE COLA COMPLETADO")
            print("=" * 60)

            return result

        except Exception as e:
            print(f"❌ Error en procesamiento de cola: {e}")
            return {'success': False, 'message': f'Error en procesamiento de cola: {e}'}

    def early_exit_for_debug_purposes(self, exit_point: str, results: Optional[Dict[str, Any]] = None) -> bool:
        """
        Salida temprana para propósitos de debug

        Args:
            exit_point: Punto de salida ("after_groups_tracking", "after_login", etc.)
            results: Resultados a mostrar antes de salir

        Returns:
            bool: True si debe salir, False si debe continuar
        """
        print(f"\n🐛 DEBUG EXIT POINT: {exit_point}")
        print("=" * 60)

        if results:
            print("📊 RESULTADOS OBTENIDOS:")
            for key, value in results.items():
                if isinstance(value, dict):
                    print(f"   📋 {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"      {sub_key}: {sub_value}")
                else:
                    print(f"   {key}: {value}")

        print(f"\n⏸️ NAVEGADOR ABIERTO PARA INSPECCIÓN DE DEBUG")
        print("🔍 Puedes inspeccionar el estado actual del navegador")
        print("📋 Presiona Enter para continuar o Ctrl+C para salir...")

        try:
            input()
            return False  # Continuar si presiona Enter
        except KeyboardInterrupt:
            print("\n🛑 Salida forzada por Ctrl+C")
            return True  # Salir si presiona Ctrl+C

    def cleanup(self):
        """Limpia recursos del navegador"""
        try:
            if self.browser:
                self.browser.__exit__(None, None, None)
                print("✅ Cerrando navegador...")
        except Exception as e:
            print(f"❌ Error cerrando navegador: {e}")


def main():
    """Función principal del orquestador con soporte para argumentos"""
    parser = argparse.ArgumentParser(description='Facebook Flow Orchestrator')
    parser.add_argument('--headless', action='store_true',
                       help='Ejecutar en modo headless (sin interfaz gráfica)')
    parser.add_argument('--config', default=None,
                       help='Ruta al archivo de configuración (usa ruta absoluta por defecto)')
    parser.add_argument('--skip-humanization', action='store_true',
                       help='Saltar humanización e ir directo a tareas')

    args = parser.parse_args()

    print("🧪 FACEBOOK FLOW ORCHESTRATOR")
    print("=" * 50)
    print(f"🎛️ Modo headless: {'✅ Activado' if args.headless else '🖥️ Modo Normal'}")
    print(f"📁 Config: {args.config}")
    print(f"🤖 Skip humanización: {'✅ Sí' if args.skip_humanization else '❌ No'}")
    print("=" * 50)

    # Crear orquestador
    orchestrator = FacebookFlowOrchestrator(config_path=args.config, headless=args.headless)

    try:
        # 1. Ejecutar login
        login_result = orchestrator.execute_login_flow()

        # Mostrar resultados del login
        print(f"\n📊 RESULTADO FINAL:")
        print("=" * 50)
        print(f"✅ Éxito: {login_result.get('success', False)}")
        print(f"🎯 Estado: {login_result.get('state', 'N/A')}")
        print(f"💬 Mensaje: {login_result.get('message', 'N/A')}")
        print(f"⏱️ Tiempo: {login_result.get('duration', 0):.2f}s")
        print(f"📸 Screenshots: {len(login_result.get('screenshots', []))}")

        # Mostrar verificación de home si existe
        home_verification = login_result.get('home_verification', {})
        if home_verification:
            print(f"\n🏠 Indicadores de Home:")
            for key, value in home_verification.items():
                if isinstance(value, bool):
                    icon = "✅" if value else "❌"
                    print(f"   {icon} {key}")
                elif key not in ['detection_time']:
                    print(f"   📊 {key}: {value}")

        # 2. Si login exitoso, ejecutar flujo con control de skip interno
        if login_result.get('success'):

            # 3. Ejecutar humanización (usa directamente el flag CLI --skip-humanization)
            humanization_result = orchestrator.execute_humanization(skip=args.skip_humanization)

            # 4. Ejecutar tracking de grupos (ya no existe lógica de posts ni test_single_group)
            groups_result = orchestrator.execute_groups_tracking(skip=False)

            # 5. Procesamiento de cola de grupos (solo si tracking exitoso)
            if groups_result.get('success') and not groups_result.get('skipped'):
                queue_result = orchestrator.execute_queue_processing(skip=False)
            else:
                print("⚠️ No se ejecuta procesamiento de cola por fallo/skip en tracking de grupos")

            # 7. Esperar cierre manual si hay página disponible (solo en modo no-headless)
            if orchestrator.page and not args.headless:
                print("\n⏸️ NAVEGADOR ABIERTO PARA INSPECCIÓN")
                print("=" * 40)
                print("🔍 Puedes inspeccionar el estado actual del navegador")
                input("📋 Presiona Enter cuando quieras cerrar...")
            elif args.headless:
                print("\n🤖 Modo headless: cerrando automáticamente...")
        else:
            print("\n⚠️ Login no exitoso, saltando humanización")

            # DEMO: Simular observación en página de grupo para mostrar funcionalidad
            print("\n🎯 DEMO: Simulando observación en página de grupo")
            print("=" * 50)

            # Crear QueueProcessor para demostración
            from queue_processor import QueueProcessor
            demo_processor = QueueProcessor(user_id=1)
            demo_processor._perform_real_page_observation()

            print("\n📋 DEMO completada - Funcionalidad de observación lista")

    except Exception as e:
        print(f"❌ Error en orquestación: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Limpiar recursos
        orchestrator.cleanup()


if __name__ == "__main__":
    main()
