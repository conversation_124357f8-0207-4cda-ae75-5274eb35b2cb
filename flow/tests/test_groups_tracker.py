#!/usr/bin/env python3
"""
Test simple para Groups Tracker
"""

import sys
import os

# Agregar el directorio flow al path para imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from groups_tracker import GroupsTracker
from camoufox import Camoufox

def test_groups_tracker_simple():
    """Test simple del Groups Tracker usando sesión existente"""
    
    print("🧪 TEST SIMPLE DEL GROUPS TRACKER")
    print("=" * 50)
    
    session_file = "flow/results/facebook_session.json"
    
    if not os.path.exists(session_file):
        print("❌ No se encontró sesión guardada")
        return
    
    try:
        # Crear instancia del tracker
        groups_tracker = GroupsTracker()
        
        # Usar patrón del flow_solid.py
        browser = Camoufox(headless=False, i_know_what_im_doing=True)
        browser_started = browser.__enter__()
        
        context = browser_started.new_context(storage_state=session_file)
        page = context.new_page()
        
        # Navegar a página de grupos
        groups_url = "https://www.facebook.com/groups/joins/?nav_source=tab"
        print(f"🌐 Navegando a: {groups_url}")
        page.goto(groups_url)
        
        # Ejecutar tracking
        results = groups_tracker.track_groups_page(page)
        
        # Mostrar resumen
        groups_tracker.print_summary(results)
        
        # Mostrar lista de links si fue exitoso
        extraction = results.get('extraction', {})
        groups_links = extraction.get('groups_links', [])
        
        if groups_links:
            print(f"\n📋 LISTA COMPLETA DE LINKS ({len(groups_links)}):")
            print("=" * 80)
            for i, link in enumerate(groups_links):
                print(f"   [{i+1:2d}] {link['href']}")
            print("=" * 80)
        
        # Esperar para inspección
        input("\n📋 Presiona Enter para cerrar...")
        
        # Cerrar navegador
        browser.__exit__(None, None, None)
        
        print("✅ Test completado exitosamente")
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_groups_tracker_simple()
