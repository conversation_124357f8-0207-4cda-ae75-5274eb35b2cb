"""
Script de prueba para el Queue Processor
Demuestra el flujo completo con placeholders
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from queue_processor import QueueProcessor


def main():
    """
    Función principal para probar el Queue Processor
    """
    print("=" * 60)
    print("🧪 PRUEBA DEL QUEUE PROCESSOR")
    print("=" * 60)
    print()
    
    # Crear instancia del procesador
    processor = QueueProcessor()
    
    # Mostrar estadísticas iniciales
    print("📊 Estadísticas iniciales:")
    stats = processor.get_processing_stats()
    for key, value in stats.items():
        print(f"    {key}: {value}")
    print()
    
    # Ejecutar el procesamiento completo
    processor.process_queue()
    
    print()
    print("=" * 60)
    print("✅ PRUEBA COMPLETADA")
    print("=" * 60)


if __name__ == "__main__":
    main()
