#!/usr/bin/env python3
"""
Utilidades generales para el proyecto de extracción de Facebook
Funciones auxiliares para procesamiento de URLs, slugs y datos de grupos
"""

import re
from typing import Optional, Dict, Any
from urllib.parse import urlparse, parse_qs


def extract_group_slug_from_url(url: str) -> Optional[str]:
    """
    Extrae el slug/ID del grupo de una URL de Facebook
    
    Soporta múltiples formatos de URL de grupos de Facebook:
    - https://www.facebook.com/groups/1425041927709225/
    - https://www.facebook.com/groups/1425041927709225/?sorting_setting=CHRONOLOGICAL_LISTINGS
    - https://facebook.com/groups/marketingdigital123/
    - https://m.facebook.com/groups/123456789/
    - /groups/1425041927709225/
    
    Args:
        url: URL del grupo de Facebook
        
    Returns:
        str: Slug/ID del grupo o None si no se puede extraer
        
    Examples:
        >>> extract_group_slug_from_url("https://www.facebook.com/groups/1425041927709225/")
        "1425041927709225"
        >>> extract_group_slug_from_url("https://facebook.com/groups/marketingdigital/?ref=share")
        "marketingdigital"
    """
    if not url or not isinstance(url, str):
        return None
    
    try:
        # Limpiar la URL
        url = url.strip()
        
        # Si es una URL relativa, agregarle el dominio
        if url.startswith('/'):
            url = f"https://www.facebook.com{url}"
        
        # Patrones para extraer el slug del grupo
        patterns = [
            # Patrón principal: /groups/SLUG/ o /groups/SLUG?params
            r'/groups/([^/?&#]+)',
            # Patrón alternativo: groups/SLUG al final
            r'groups/([^/?&#]+)$',
            # Patrón para URLs con parámetros
            r'/groups/([^/?&#]+)(?:\?|&|#|$)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                slug = match.group(1)
                # Validar que el slug no esté vacío y sea válido
                if slug and len(slug) > 0 and slug != 'groups':
                    return slug
        
        return None
        
    except Exception as e:
        print(f"⚠️ Error extrayendo slug de URL '{url}': {e}")
        return None


def extract_group_info_from_url(url: str) -> Dict[str, Any]:
    """
    Extrae información completa del grupo desde una URL
    
    Args:
        url: URL del grupo de Facebook
        
    Returns:
        Dict con información extraída:
        - slug: ID/slug del grupo
        - clean_url: URL limpia sin parámetros
        - base_url: URL base para navegación
        - has_sorting: Si tiene parámetros de ordenamiento
        - url_type: Tipo de URL (numeric_id, text_slug, unknown)
    """
    result = {
        'slug': None,
        'clean_url': None,
        'base_url': None,
        'has_sorting': False,
        'url_type': 'unknown',
        'original_url': url
    }
    
    try:
        slug = extract_group_slug_from_url(url)
        if not slug:
            return result
        
        result['slug'] = slug
        
        # Determinar tipo de slug
        if slug.isdigit():
            result['url_type'] = 'numeric_id'
        elif re.match(r'^[a-zA-Z0-9._-]+$', slug):
            result['url_type'] = 'text_slug'
        
        # Construir URLs limpias
        result['base_url'] = f"https://www.facebook.com/groups/{slug}/"
        result['clean_url'] = result['base_url']
        
        # Verificar si tiene parámetros de ordenamiento
        if 'sorting_setting' in url or 'ref=' in url:
            result['has_sorting'] = True
        
        return result
        
    except Exception as e:
        print(f"⚠️ Error extrayendo info de URL '{url}': {e}")
        return result


def normalize_group_url(url: str, add_chronological: bool = True) -> str:
    """
    Normaliza una URL de grupo para uso consistente
    
    Args:
        url: URL original del grupo
        add_chronological: Si agregar parámetro de ordenamiento cronológico
        
    Returns:
        str: URL normalizada
    """
    try:
        info = extract_group_info_from_url(url)
        
        if not info['slug']:
            return url  # Devolver original si no se puede procesar
        
        base_url = info['base_url']
        
        if add_chronological:
            return f"{base_url}?sorting_setting=CHRONOLOGICAL"
        else:
            return base_url
            
    except Exception as e:
        print(f"⚠️ Error normalizando URL '{url}': {e}")
        return url


def generate_group_name_from_slug(slug: str) -> str:
    """
    Genera un nombre consistente para el grupo basado en su slug
    
    Args:
        slug: Slug/ID del grupo
        
    Returns:
        str: Nombre generado para el grupo
    """
    if not slug:
        return "Grupo Sin Nombre"
    
    try:
        # Si es un ID numérico, usar formato "Grupo [ID]"
        if slug.isdigit():
            return f"Grupo {slug}"
        
        # Si es un slug de texto, limpiarlo y formatearlo
        # Reemplazar guiones y puntos con espacios
        clean_name = slug.replace('-', ' ').replace('.', ' ').replace('_', ' ')
        
        # Capitalizar palabras
        clean_name = ' '.join(word.capitalize() for word in clean_name.split())
        
        return clean_name
        
    except Exception as e:
        print(f"⚠️ Error generando nombre para slug '{slug}': {e}")
        return f"Grupo {slug}"


def validate_facebook_group_url(url: str) -> bool:
    """
    Valida si una URL es una URL válida de grupo de Facebook
    
    Args:
        url: URL a validar
        
    Returns:
        bool: True si es una URL válida de grupo de Facebook
    """
    if not url or not isinstance(url, str):
        return False
    
    try:
        # Verificar que contenga 'groups'
        if 'groups' not in url.lower():
            return False
        
        # Verificar que se pueda extraer un slug
        slug = extract_group_slug_from_url(url)
        if not slug:
            return False
        
        # Verificar dominio de Facebook (opcional, puede ser relativa)
        if url.startswith('http'):
            parsed = urlparse(url)
            facebook_domains = ['facebook.com', 'www.facebook.com', 'm.facebook.com']
            if parsed.netloc not in facebook_domains:
                return False
        
        return True
        
    except Exception:
        return False


# Función de prueba para validar las utilidades
def test_group_url_utilities():
    """
    Función de prueba para validar las utilidades de URL de grupos
    """
    print("🧪 PROBANDO UTILIDADES DE URL DE GRUPOS")
    print("=" * 50)
    
    test_urls = [
        "https://www.facebook.com/groups/1425041927709225/?sorting_setting=CHRONOLOGICAL_LISTINGS",
        "https://facebook.com/groups/marketingdigital/",
        "https://m.facebook.com/groups/123456789/",
        "/groups/1425041927709225/",
        "https://www.facebook.com/groups/python.developers/?ref=share",
        "https://invalid-url.com/not-a-group",
        ""
    ]
    
    for url in test_urls:
        print(f"\n🔗 URL: {url}")
        
        # Extraer slug
        slug = extract_group_slug_from_url(url)
        print(f"   📋 Slug: {slug}")
        
        # Extraer info completa
        info = extract_group_info_from_url(url)
        print(f"   📊 Tipo: {info['url_type']}")
        print(f"   🔗 URL limpia: {info['clean_url']}")
        
        # Generar nombre
        if slug:
            name = generate_group_name_from_slug(slug)
            print(f"   📝 Nombre generado: {name}")
        
        # Validar URL
        is_valid = validate_facebook_group_url(url)
        print(f"   ✅ Válida: {is_valid}")


if __name__ == "__main__":
    test_group_url_utilities()
