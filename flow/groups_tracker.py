#!/usr/bin/env python3
"""
Groups Tracker - Extractor de datos de grupos de Facebook
Detecta y extrae información de la página de grupos unidos
"""

import time
import json
import re
from typing import Dict, List, Optional, Any
from utils.observer_utils import ElementObserver



class GroupsPageDetector:
    """Detecta si estamos en la página de grupos de Facebook"""
    
    def __init__(self):
        self.groups_indicators = {
            'url_pattern': r'facebook\.com/groups/joins',
            'title_selector': "span:has-text('Todos los grupos a los que te has unido')",
            'groups_links_selector': "a[aria-label='Ver grupo']"
        }
    
    def is_groups_page(self, page) -> Dict[str, Any]:
        """
        Detecta si estamos en la página de grupos
        
        Returns:
            Dict con información de detección
        """
        detection = {
            'is_groups_page': False,
            'url_match': False,
            'title_found': False,
            'links_found': False,
            'url': page.url,
            'detection_time': time.time()
        }
        
        try:
            # Verificar URL
            if re.search(self.groups_indicators['url_pattern'], page.url):
                detection['url_match'] = True
            
            # Verificar título
            title_elements = page.locator(self.groups_indicators['title_selector'])
            if title_elements.count() > 0:
                detection['title_found'] = True
            
            # Verificar links de grupos
            links_elements = page.locator(self.groups_indicators['groups_links_selector'])
            if links_elements.count() > 0:
                detection['links_found'] = True
            
            # Es página de grupos si tiene al menos URL y título
            detection['is_groups_page'] = detection['url_match'] and detection['title_found']
            
        except Exception as e:
            detection['error'] = str(e)
        
        return detection


class GroupsDataExtractor:
    """Extrae datos específicos de la página de grupos"""
    
    def __init__(self, page):
        self.page = page
        self.observer = ElementObserver(page, target_url=page.url)
        self.groups_data = {
            'extraction_time': time.time(),
            'url': page.url,
            'total_groups_count': 0,
            'groups_links': [],
            'extraction_success': False,
            'extraction_duration': 0
        }
    
    def extract_groups_count(self) -> int:
        """
        Extrae el número total de grupos del título
        
        Returns:
            Número de grupos o 0 si no se encuentra
        """
        title_selector = "span:has-text('Todos los grupos a los que te has unido')"
        title_observation = self.observer.observe_must_be(
            selector=title_selector,
            element_type="heading",
            description="Título con número de grupos",
            timeout_seconds=15
        )
        
        if title_observation['success']:
            fallback_texts = title_observation.get('fallback_texts', [])
            
            for fb in fallback_texts:
                text = fb.get('text', '')
                # Buscar patrón: "Todos los grupos a los que te has unido (28)"
                match = re.search(r'\((\d+)\)', text)
                if match:
                    count = int(match.group(1))
                    print(f"   ✅ Número de grupos extraído: {count}")
                    return count
        
        print("   ❌ No se pudo extraer el número de grupos")
        return 0
    
    def extract_groups_links(self, expected_count: int = 0) -> List[Dict[str, str]]:
        """
        Extrae todos los links de grupos usando scroll progresivo
        
        Args:
            expected_count: Número esperado de grupos para optimizar extracción
            
        Returns:
            Lista de diccionarios con información de links
        """
        groups_links_selector = "a[aria-label='Ver grupo']"
        links_observation = self.observer.observe_must_be(
            selector=groups_links_selector,
            element_type="link",
            description="Links de grupos",
            timeout_seconds=60,  # Más tiempo para scroll progresivo
            use_progressive_scroll=True
        )
        
        collected_links = []
        
        if links_observation['success']:
            collected_links = links_observation.get('collected_links', [])
            
            print(f"   🔗 Links recolectados: {len(collected_links)}")
            print(f"   🎯 Links esperados: {expected_count}")
            
            if expected_count > 0:
                if len(collected_links) >= expected_count:
                    print(f"   ✅ Extracción exitosa: {len(collected_links)} >= {expected_count}")
                else:
                    print(f"   ⚠️ Extracción parcial: {len(collected_links)} < {expected_count}")
        
        return collected_links
    
    def extract_all_data(self) -> Dict[str, Any]:
        """
        Extrae todos los datos de la página de grupos
        
        Returns:
            Dict con todos los datos extraídos
        """
        start_time = time.time()
        
        print("🔍 INICIANDO EXTRACCIÓN DE DATOS DE GRUPOS")
        print("=" * 60)
        
        # Extraer número de grupos
        groups_count = self.extract_groups_count()
        self.groups_data['total_groups_count'] = groups_count
        
        # Extraer links de grupos
        if groups_count > 0:
            groups_links = self.extract_groups_links(groups_count)
            self.groups_data['groups_links'] = groups_links
            
            # Verificar éxito
            if len(groups_links) >= groups_count:
                self.groups_data['extraction_success'] = True
                print(f"✅ EXTRACCIÓN EXITOSA: {len(groups_links)} grupos extraídos")
            else:
                print(f"⚠️ EXTRACCIÓN PARCIAL: {len(groups_links)}/{groups_count} grupos")
        else:
            print("❌ No se pudo determinar el número de grupos")
        
        # Calcular duración
        self.groups_data['extraction_duration'] = time.time() - start_time
        
        return self.groups_data


class GroupsTracker:
    """Tracker principal para la página de grupos de Facebook"""
    
    def __init__(self):
        self.detector = GroupsPageDetector()
        self.results_file = "flow/results/groups_data.json"
    
    def track_groups_page(self, page) -> Dict[str, Any]:
        """
        Rastrea y extrae datos de la página de grupos
        
        Args:
            page: Objeto page de Playwright
            
        Returns:
            Dict con resultados completos
        """
        results = {
            'timestamp': time.time(),
            'detection': {},
            'extraction': {},
            'success': False
        }
        
        try:
            # Detectar página de grupos
            print("🔍 DETECTANDO PÁGINA DE GRUPOS")
            detection = self.detector.is_groups_page(page)
            results['detection'] = detection
            
            if detection['is_groups_page']:
                print("✅ Página de grupos detectada")
                
                # Extraer datos
                extractor = GroupsDataExtractor(page)
                extraction_data = extractor.extract_all_data()
                results['extraction'] = extraction_data
                results['success'] = extraction_data['extraction_success']
                
                # Guardar resultados
                self._save_results(results)
                
            else:
                print("❌ No es una página de grupos válida")
                print(f"   URL match: {detection['url_match']}")
                print(f"   Title found: {detection['title_found']}")
                print(f"   Links found: {detection['links_found']}")
        
        except Exception as e:
            results['error'] = str(e)
            print(f"❌ Error en tracking de grupos: {e}")
        
        return results
    
    def _save_results(self, results: Dict[str, Any]):
        """Guarda resultados en archivo JSON"""
        try:
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"💾 Resultados guardados en: {self.results_file}")
        except Exception as e:
            print(f"❌ Error guardando resultados: {e}")
    
    def print_summary(self, results: Dict[str, Any]):
        """Imprime resumen de resultados"""
        print("\n📊 RESUMEN DE EXTRACCIÓN DE GRUPOS")
        print("=" * 50)
        
        detection = results.get('detection', {})
        extraction = results.get('extraction', {})
        
        print(f"🌐 URL: {detection.get('url', 'N/A')}")
        print(f"✅ Página detectada: {detection.get('is_groups_page', False)}")
        print(f"📊 Total grupos: {extraction.get('total_groups_count', 0)}")
        print(f"🔗 Links extraídos: {len(extraction.get('groups_links', []))}")
        print(f"⏱️ Duración: {extraction.get('extraction_duration', 0):.1f}s")
        print(f"🎯 Éxito: {results.get('success', False)}")
        
        # Mostrar algunos links de ejemplo
        groups_links = extraction.get('groups_links', [])
        if groups_links:
            print(f"\n📋 PRIMEROS 5 GRUPOS EXTRAÍDOS:")
            for i, link in enumerate(groups_links[:5]):
                print(f"   [{i+1}] {link['href']}")
            if len(groups_links) > 5:
                print(f"   ... y {len(groups_links) - 5} grupos más")


def test_groups_tracker():
    """Función de prueba para el Groups Tracker"""
    from camoufox import Camoufox
    import os

    print("🧪 PRUEBA DEL GROUPS TRACKER")
    print("=" * 50)

    # Usar sesión guardada
    session_file = "flow/results/facebook_session.json"

    if not os.path.exists(session_file):
        print("❌ No se encontró sesión guardada")
        return

    try:
        # Usar patrón correcto de Camoufox
        browser = Camoufox(headless=False, i_know_what_im_doing=True)
        browser_started = browser.__enter__()

        # Crear contexto con sesión
        context = browser_started.new_context(storage_state=session_file)
        page = context.new_page()

        # Navegar a página de grupos
        groups_url = "https://www.facebook.com/groups/joins/?nav_source=tab"
        print(f"🌐 Navegando a: {groups_url}")
        page.goto(groups_url)

        # Usar el tracker
        tracker = GroupsTracker()
        results = tracker.track_groups_page(page)

        # Mostrar resumen
        tracker.print_summary(results)

        # Esperar para inspección
        input("\n📋 Presiona Enter para cerrar...")

        # Cerrar navegador
        browser.__exit__(None, None, None)

    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_groups_tracker()
