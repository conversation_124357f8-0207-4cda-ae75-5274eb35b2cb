"""
Group Operations - Operaciones formales para manejo de grupos de Facebook
Clase refactorizada desde proto_group_operations.py

Nota:
    Este módulo ahora soporta degradación suave cuando Playwright no está instalado.
    Si 'playwright' no está disponible, se define una clase stub 'Page' únicamente
    para propósitos de tipado y se omiten operaciones reales de navegación.
"""

from typing import Dict, Any, Optional

# Degradación suave: intentar importar Playwright, si falla usar stub
try:
    from playwright.sync_api import Page  # type: ignore
    _PLAYWRIGHT_AVAILABLE = True
except Exception:
    _PLAYWRIGHT_AVAILABLE = False

    class Page:  # type: ignore
        """Stub mínima de Page usada cuando Playwright no está instalado."""
        pass


class GroupOperations:
    """
    Clase para manejar operaciones relacionadas con grupos de Facebook.
    
    Se asegura de no fallar en entornos donde Playwright no está instalado,
    permitiendo que el núcleo se ejecute (p.ej. tests de cola) sin dependencia dura.
    """
    
    def __init__(self, page: Optional[Page] = None):
        """
        Inicializa las operaciones de grupo.
        
        Args:
            page: Instancia de página de Playwright (opcional)
        """
        if page is not None and not _PLAYWRIGHT_AVAILABLE:
            print("⚠️ Page proporcionada pero Playwright no está disponible - se ignorará.")
        self.page = page
    
    def set_page(self, page: Page) -> None:
        """
        Establece la página de Playwright para las operaciones
        
        Args:
            page: Instancia de página de Playwright
        """
        self.page = page
    
    def navigate_to_group(self, group_id: str) -> bool:
        """
        Navega a un grupo específico de Facebook
        
        Args:
            group_id: ID o slug del grupo
            
        Returns:
            bool: True si la navegación fue exitosa
        """
        if not self.page:
            print("❌ No hay página disponible para navegación")
            return False
            
        try:
            # Construir URL del grupo
            if group_id.isdigit():
                group_url = f"https://www.facebook.com/groups/{group_id}/"
            else:
                group_url = f"https://www.facebook.com/groups/{group_id}/"
            
            print(f"🌐 Navegando al grupo: {group_url}")
            self.page.goto(group_url, wait_until="networkidle", timeout=30000)
            
            # Verificar que la navegación fue exitosa
            current_url = self.page.url
            if "groups" in current_url and group_id in current_url:
                print(f"✅ Navegación exitosa al grupo {group_id}")
                return True
            else:
                print(f"⚠️ URL inesperada después de navegación: {current_url}")
                return False
                
        except Exception as e:
            print(f"❌ Error navegando al grupo {group_id}: {e}")
            return False
    
    def get_group_info(self, group_id: str) -> Dict[str, Any]:
        """
        Extrae información básica del grupo
        
        Args:
            group_id: ID del grupo
            
        Returns:
            Dict con información del grupo
        """
        print(f"📊 Extrayendo información del grupo: {group_id}")
        
        if not self.page:
            print("❌ No hay página disponible para extracción")
            return {
                "id": group_id,
                "name": f"Grupo {group_id}",
                "members_count": 0,
                "type": "unknown",
                "description": "",
                "error": "No page available"
            }
        
        try:
            # Extraer nombre del grupo
            group_name = "Grupo desconocido"
            try:
                # Intentar varios selectores para el nombre del grupo
                name_selectors = [
                    'h1[data-testid="group-name"]',
                    'h1',
                    '[data-testid="group-name"]'
                ]
                
                for selector in name_selectors:
                    name_element = self.page.locator(selector).first
                    if name_element.is_visible():
                        text_content = name_element.text_content()
                        if text_content:
                            group_name = text_content.strip()
                            break
                        
            except Exception as e:
                print(f"⚠️ No se pudo extraer nombre del grupo: {e}")
            
            # Extraer número de miembros (si está disponible)
            members_count = 0
            try:
                # Buscar texto que contenga "miembros" o "members"
                members_text = self.page.locator('text=/\\d+.*miembros|\\d+.*members/i').first
                if members_text.is_visible():
                    text = members_text.text_content()
                    if text:
                        import re
                        match = re.search(r'(\d+)', text.replace(',', '').replace('.', ''))
                        if match:
                            members_count = int(match.group(1))
            except Exception as e:
                print(f"⚠️ No se pudo extraer número de miembros: {e}")
            
            # Determinar tipo de grupo (público/privado)
            group_type = "unknown"
            try:
                # Buscar indicadores de privacidad
                if self.page.locator('text=/privado|private/i').first.is_visible():
                    group_type = "private"
                elif self.page.locator('text=/público|public/i').first.is_visible():
                    group_type = "public"
            except Exception as e:
                print(f"⚠️ No se pudo determinar tipo de grupo: {e}")
            
            group_info = {
                "id": group_id,
                "name": group_name,
                "members_count": members_count,
                "type": group_type,
                "description": "",
                "url": self.page.url
            }
            
            print(f"✅ Información extraída: {group_name} ({members_count} miembros, {group_type})")
            return group_info
            
        except Exception as e:
            print(f"❌ Error extrayendo información del grupo: {e}")
            return {
                "id": group_id,
                "name": f"Grupo {group_id}",
                "members_count": 0,
                "type": "unknown",
                "description": "",
                "error": str(e)
            }
    
    def check_group_accessibility(self, group_id: str) -> bool:
        """
        Verifica si el grupo es accesible (no privado, no bloqueado)
        
        Args:
            group_id: ID del grupo a verificar
            
        Returns:
            bool: True si el grupo es accesible
        """
        if not self.page:
            print("❌ No hay página disponible para verificación")
            return False
            
        try:
            print(f"🔐 Verificando accesibilidad del grupo: {group_id}")
            
            # Verificar si hay mensajes de error
            error_indicators = [
                'text=/no encontrado|not found/i',
                'text=/no disponible|not available/i',
                'text=/privado|private/i',
                'text=/no tienes permiso|no permission/i'
            ]
            
            for indicator in error_indicators:
                if self.page.locator(indicator).first.is_visible():
                    print(f"⚠️ Grupo no accesible: {indicator}")
                    return False
            
            # Verificar si se pueden ver posts
            posts_visible = False
            try:
                posts = self.page.locator('[data-ad-rendering-role="story_message"]').count()
                if posts > 0:
                    posts_visible = True
                    print(f"✅ Grupo accesible: {posts} posts visibles")
                else:
                    print(f"⚠️ No se encontraron posts visibles")
            except Exception as e:
                print(f"⚠️ Error verificando posts: {e}")
            
            return posts_visible
            
        except Exception as e:
            print(f"❌ Error verificando accesibilidad: {e}")
            return False
    
    def wait_for_group_page_load(self, timeout: int = 30) -> bool:
        """
        Espera a que la página del grupo cargue completamente
        
        Args:
            timeout: Tiempo máximo de espera en segundos
            
        Returns:
            bool: True si la página cargó correctamente
        """
        if not self.page:
            print("❌ No hay página disponible para espera")
            return False
            
        try:
            print(f"⏳ Esperando carga de página del grupo (timeout: {timeout}s)")
            
            # Esperar elementos principales del grupo
            self.page.wait_for_selector('h1', timeout=timeout * 1000)
            
            # Esperar que aparezcan posts o contenido
            try:
                self.page.wait_for_selector('[data-ad-rendering-role="story_message"]', timeout=10000)
                print("✅ Posts detectados - página cargada")
                return True
            except:
                # Si no hay posts, verificar otros elementos
                try:
                    self.page.wait_for_selector('[role="main"]', timeout=5000)
                    print("✅ Contenido principal detectado - página cargada")
                    return True
                except:
                    print("⚠️ Página cargada pero sin contenido específico detectado")
                    return True
                    
        except Exception as e:
            print(f"❌ Error esperando carga de página: {e}")
            return False
