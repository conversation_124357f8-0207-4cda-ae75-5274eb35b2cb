"""
Sistema de humanización para Facebook con monitoreo y recovery
"""
import random
import time
from typing import Dict, Any, Optional
from playwright.sync_api import Page

class FacebookHumanizer:
    """Clase para actividades de humanización en Facebook con monitoreo completo"""
    
    def __init__(self, page_detectors=None):
        """
        Inicializar humanizador
        
        Args:
            page_detectors: Instancia de PageDetectors para verificaciones is_home
        """
        self.page_detectors = page_detectors
    
    def monitor_and_recovery(self, page: Page, action_description: str = "acción") -> Dict[str, Any]:
        """
        Sistema de monitoreo y recovery completo
        
        Args:
            page: Página de Playwright
            action_description: Descripción de la acción realizada
            
        Returns:
            Dict con resultados del monitoreo
        """
        print(f"   🔍 Monitoreando después de {action_description}...")
        
        results = {
            'dialogs_detected': 0,
            'url_changed': False,
            'recovery_executed': False,
            'is_home_verified': False,
            'final_url': '',
            'wait_time': 0
        }
        
        # 1. Verificar dialogs
        try:
            dialogs = page.locator('[role="dialog"]').count()
            results['dialogs_detected'] = dialogs
            if dialogs > 0:
                print(f"   🚨 Dialog detectado! Ejecutando escape...")
                page.keyboard.press("Escape")
                page.wait_for_timeout(1000)
                results['recovery_executed'] = True
        except Exception as e:
            print(f"   ⚠️ Error verificando dialogs: {e}")
        
        # 2. Verificar URL actual
        current_url = page.url
        results['final_url'] = current_url
        print(f"   📍 URL actual: {current_url}")
        
        # 3. Detectar URLs problemáticas
        problematic_patterns = ['/photo/', '/posts/', '/watch/', '/groups/', '/events/', '/marketplace/']
        url_changed = any(pattern in current_url for pattern in problematic_patterns)
        
        # 4. Detectar cambios de usuario (slug)
        user_slug_pattern = (current_url.count('/') > 3 and 
                           'facebook.com/' in current_url and 
                           not current_url.endswith('facebook.com/'))
        
        if url_changed or user_slug_pattern:
            print(f"   ⚠️ URL problemática detectada: {current_url}")
            results['url_changed'] = True
            
            # Escape inmediato
            print("   ⌨️ Escape inmediato...")
            page.keyboard.press("Escape")
            page.wait_for_timeout(1000)
            results['recovery_executed'] = True
            
            # Verificar si volvimos
            new_url = page.url
            if new_url != current_url:
                print(f"   ✅ URL cambió después de escape: {new_url}")
                results['final_url'] = new_url
            else:
                print("   ⚠️ Escape no cambió URL, ejecutando go_home...")
                self.go_home(page)
        
        # 5. Espera random y verificación
        wait_time = random.uniform(10, 30)
        results['wait_time'] = wait_time
        print(f"   ⏳ Espera random: {wait_time:.1f}s antes de verificar home...")
        page.wait_for_timeout(int(wait_time * 1000))
        
        # 6. Verificación final is_home
        if self.page_detectors:
            try:
                home_detection = self.page_detectors.is_home(page)
                results['is_home_verified'] = home_detection.get('is_home', False)
                
                if not home_detection.get('is_home', False):
                    print("   🏠 No estamos en home, ejecutando go_home...")
                    self.go_home(page)
                    results['recovery_executed'] = True
                else:
                    print("   ✅ Verificación is_home exitosa")
            except Exception as e:
                print(f"   ⚠️ Error verificando is_home: {e}")
        else:
            print("   🔍 PLACEHOLDER: Verificar is_home()...")
        
        print(f"   ✅ Monitoreo completado para {action_description}")
        return results
    
    def like_with_reactions(self, page: Page) -> Dict[str, Any]:
        """
        Dar like con cartera de reacciones y monitoreo completo
        
        Args:
            page: Página de Playwright
            
        Returns:
            Dict con resultados de la acción
        """
        print("\n❤️ ACTIVIDAD: DAR LIKE CON CARTERA")
        
        results = {
            'success': False,
            'reaction_selected': '',
            'elements_found': 0,
            'monitoring_results': {}
        }
        
        try:
            # Buscar botón de like
            like_buttons = page.locator('span:has-text("Me gusta")').filter(visible=True)
            if like_buttons.count() > 0:
                print(f"   🔍 Encontrados {like_buttons.count()} botones 'Me gusta' visibles")
                
                # Estabilizar página
                print("   📜 Scroll hacia arriba para estabilizar...")
                page.keyboard.press("Home")
                page.wait_for_timeout(1000)
                
                # Primer click para abrir cartera
                first_like = like_buttons.first
                print("   👆 Primer click para abrir cartera...")
                first_like.click(force=True)
                print("   ⏳ Esperando cartera de reacciones...")
                page.wait_for_timeout(2500)
                
                # Analizar reacciones disponibles
                reaction_elements = self._analyze_reactions(page)
                print(f"   📊 Elementos detectados: {len(reaction_elements.get('all_reactions', []))} tipos")
                
                # Seleccionar reacción aleatoria
                if reaction_elements.get('all_reactions'):
                    selected_reaction = random.choice(reaction_elements['all_reactions'])
                    reaction_text = selected_reaction['text']
                    results['reaction_selected'] = reaction_text
                    
                    print(f"   🎲 Seleccionando reacción aleatoria: '{reaction_text}'")
                    print("   ⏳ Esperando antes de seleccionar reacción...")
                    page.wait_for_timeout(1000)
                    
                    # Click en reacción específica
                    reaction_selector = page.locator(f'[aria-label*="{reaction_text}"]').filter(visible=True)
                    results['elements_found'] = reaction_selector.count()
                    
                    if reaction_selector.count() > 0:
                        print(f"   👆 Click en '{reaction_text}'...")
                        reaction_selector.first.hover(force=True)
                        page.wait_for_timeout(500)
                        reaction_selector.first.click(force=True)
                        page.wait_for_timeout(1500)
                        
                        print(f"   ✅ Reacción '{reaction_text}' procesada")
                        results['success'] = True
                        
                        # MONITOREO Y RECOVERY
                        results['monitoring_results'] = self.monitor_and_recovery(page, f"like con '{reaction_text}'")
                        
                    else:
                        print(f"   ❌ No se pudo hacer click en '{reaction_text}'")
                else:
                    print("   ⚠️ No se detectaron reacciones disponibles")
            else:
                print("   ❌ No se encontraron botones de like visibles")
                
        except Exception as e:
            print(f"   ❌ Error dando like: {e}")
            # Monitoreo de emergencia
            try:
                results['monitoring_results'] = self.monitor_and_recovery(page, "error en like")
            except:
                pass
        
        return results
    
    def _analyze_reactions(self, page: Page) -> Dict[str, Any]:
        """Analizar reacciones disponibles en la cartera"""
        return page.evaluate("""
        () => {
            const reactions = {
                like_panels: document.querySelectorAll('[role="dialog"], [role="tooltip"]').length,
                reaction_buttons: document.querySelectorAll('[aria-label*="Me gusta"], [aria-label*="Me encanta"], [aria-label*="Me entristece"]').length,
                all_reactions: []
            };
            
            const reactionTexts = ['Me gusta', 'Me encanta', 'Me entristece', 'Me divierte', 'Me asombra', 'Me enoja'];
            reactionTexts.forEach(text => {
                const elements = document.querySelectorAll(`[aria-label*="${text}"]`);
                if (elements.length > 0) {
                    reactions.all_reactions.push({text: text, count: elements.length});
                }
            });
            
            return reactions;
        }
        """)
    
    def scroll_messages(self, page: Page) -> Dict[str, Any]:
        """
        Scroll en área de mensajes
        
        Args:
            page: Página de Playwright
            
        Returns:
            Dict con resultados de la acción
        """
        print("\n🖱️ ACTIVIDAD: SCROLL EN MENSAJES")
        
        results = {
            'success': False,
            'messages_found': 0,
            'monitoring_results': {}
        }
        
        try:
            # Buscar área de mensajes
            messages_area = page.locator('a[href*="/messages/"]').first
            if messages_area.count() > 0:
                results['messages_found'] = messages_area.count()
                print("   🔍 Área de mensajes encontrada")
                
                print("   🖱️ Hover sobre mensajes...")
                messages_area.hover(force=True)
                page.wait_for_timeout(500)
                
                print("   🎡 Scroll con mousewheel...")
                page.mouse.wheel(delta_x=0, delta_y=300)
                page.wait_for_timeout(1000)
                
                print("   ✅ Scroll completado")
                results['success'] = True
                
                # MONITOREO Y RECOVERY
                results['monitoring_results'] = self.monitor_and_recovery(page, "scroll en mensajes")
                
            else:
                print("   ❌ No se encontró área de mensajes")
                
        except Exception as e:
            print(f"   ❌ Error en scroll: {e}")
            try:
                results['monitoring_results'] = self.monitor_and_recovery(page, "error en scroll")
            except:
                pass
        
        return results

    def click_feed_article(self, page: Page) -> Dict[str, Any]:
        """
        Click en artículo del feed

        Args:
            page: Página de Playwright

        Returns:
            Dict con resultados de la acción
        """
        print("\n📰 ACTIVIDAD: CLICK EN FEED")

        results = {
            'success': False,
            'articles_found': 0,
            'monitoring_results': {}
        }

        try:
            # Buscar artículos en feed
            articles = page.locator('[role="article"]')
            articles_count = articles.count()
            results['articles_found'] = articles_count

            if articles_count > 0:
                print(f"   🔍 Encontrados {articles_count} artículos en feed")

                print("   🖱️ Hover sobre artículo...")
                articles.first.hover(force=True)
                page.wait_for_timeout(500)

                print("   👆 Click en artículo directamente...")
                articles.first.click(force=True)
                page.wait_for_timeout(1000)

                print(f"   ✅ Click en artículo: {page.url}")
                results['success'] = True

                # MONITOREO Y RECOVERY
                results['monitoring_results'] = self.monitor_and_recovery(page, "click en feed")

            else:
                print("   ❌ No se encontraron artículos en feed")

        except Exception as e:
            print(f"   ❌ Error en click feed: {e}")
            try:
                results['monitoring_results'] = self.monitor_and_recovery(page, "error en click feed")
            except:
                pass

        return results

    def go_home(self, page: Page) -> Dict[str, Any]:
        """
        Navegar al home haciendo click en logo de Facebook

        Args:
            page: Página de Playwright

        Returns:
            Dict con resultados de la acción
        """
        print("\n🏠 ACTIVIDAD: GO HOME (CLICK LOGO)")

        results = {
            'success': False,
            'logos_found': 0,
            'url_before': '',
            'url_after': '',
            'is_home_verified': False
        }

        try:
            results['url_before'] = page.url

            # Buscar logos de Facebook
            facebook_logos = page.locator('a[href="/"], a[href="https://www.facebook.com/"], svg[aria-label*="Facebook"]')
            logos_count = facebook_logos.count()
            results['logos_found'] = logos_count

            if logos_count > 0:
                print(f"   🔍 Encontrados {logos_count} logos")

                # Buscar logo clickeable
                clickable_logo = facebook_logos.filter(visible=True).first
                if clickable_logo.count() > 0:
                    print("   🖱️ Hover sobre logo...")
                    clickable_logo.hover(force=True)
                    page.wait_for_timeout(500)

                    print("   👆 Click en logo Facebook...")
                    clickable_logo.click(force=True)
                    page.wait_for_timeout(2000)

                    results['url_after'] = page.url
                    print(f"   ✅ Navegado a: {page.url}")
                    results['success'] = True

                    # Verificar con is_home si está disponible
                    if self.page_detectors:
                        try:
                            home_detection = self.page_detectors.is_home(page)
                            results['is_home_verified'] = home_detection.get('is_home', False)

                            if home_detection.get('is_home', False):
                                print("   ✅ Verificación is_home exitosa")
                            else:
                                print("   ⚠️ is_home indica que no estamos en home")
                        except Exception as e:
                            print(f"   ⚠️ Error verificando is_home: {e}")
                    else:
                        print("   🔍 PLACEHOLDER: Verificar con is_home()...")
                        print("   📋 TODO: Implementar page_detectors.is_home(page)")
                        print("   📋 TODO: Confirmar que estamos en home correctamente")

                else:
                    print("   ❌ No se encontró logo clickeable")
            else:
                print("   ❌ No se encontraron logos")

        except Exception as e:
            print(f"   ❌ Error en go home: {e}")

        return results

    def run_humanization_sequence(self, page: Page, activities: list = None) -> Dict[str, Any]:
        """
        Ejecutar secuencia completa de humanización

        Args:
            page: Página de Playwright
            activities: Lista de actividades a ejecutar ['like', 'scroll', 'feed', 'home']
                       Si es None, ejecuta todas

        Returns:
            Dict con resultados de todas las actividades
        """
        if activities is None:
            activities = ['like', 'scroll', 'feed', 'home']

        print("🤖 SECUENCIA DE HUMANIZACIÓN CON MONITOREO")
        print("=" * 50)

        results = {
            'activities_executed': [],
            'total_success': 0,
            'total_errors': 0,
            'results': {}
        }

        activity_map = {
            'like': self.like_with_reactions,
            'scroll': self.scroll_messages,
            'feed': self.click_feed_article,
            'home': self.go_home
        }

        for activity in activities:
            if activity in activity_map:
                try:
                    print(f"\n🔄 Ejecutando actividad: {activity}")
                    activity_result = activity_map[activity](page)

                    activities_executed = results.get('activities_executed', [])
                    if isinstance(activities_executed, list):
                        activities_executed.append(activity)

                    activity_results = results.get('results', {})
                    if isinstance(activity_results, dict):
                        activity_results[activity] = activity_result

                    if activity_result.get('success', False):
                        total_success = results.get('total_success', 0)
                        if isinstance(total_success, int):
                            results['total_success'] = total_success + 1
                    else:
                        total_errors = results.get('total_errors', 0)
                        if isinstance(total_errors, int):
                            results['total_errors'] = total_errors + 1

                except Exception as e:
                    print(f"   ❌ Error ejecutando {activity}: {e}")
                    total_errors = results.get('total_errors', 0)
                    if isinstance(total_errors, int):
                        results['total_errors'] = total_errors + 1

                    activity_results = results.get('results', {})
                    if isinstance(activity_results, dict):
                        activity_results[activity] = {'success': False, 'error': str(e)}
            else:
                print(f"   ⚠️ Actividad desconocida: {activity}")

        print(f"\n🎉 Secuencia completada!")
        print(f"   ✅ Exitosas: {results['total_success']}")
        print(f"   ❌ Errores: {results['total_errors']}")

        return results
