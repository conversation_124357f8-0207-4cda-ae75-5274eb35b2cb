"""
Flow Utils Package (núcleo ligero)

Objetivo:
  - Evitar que import utils dispare dependencias pesadas (Playwright / humanization)
  - Proveer utilidades de path inmediatamente
  - Exponer componentes avanzados solo si sus dependencias están disponibles

Estrategia:
  - Importaciones condicionales envueltas en try/except
  - Si Playwright u otras dependencias no están instaladas, el núcleo no falla
  - Los tests de cola y base de datos pueden ejecutarse sin Playwright

NOTA:
  - Para usar IntelligentHumanizer o PageDetectors directamente, importar sus módulos:
        from utils.intelligent_humanizer import IntelligentHumanizer
        from utils.page_detectors import PageDetectors
  - Este patrón minimiza fallos en entornos minimalistas.
"""

from .path_utils import (
    get_project_root,
    get_config_path,
    get_config_str,
    get_results_path,
    get_session_file,
    get_session_str,
    get_fingerprint_file,
    get_fingerprint_str,
    get_database_path,
    get_database_str,
    ensure_results_dir
)

ElementObserver = None  # type: ignore
PageDetectors = None    # type: ignore
IntelligentHumanizer = None  # type: ignore

# Importaciones opcionales (silenciosas) - sólo se cargarán si las dependencias existen
try:
    from .observer_utils import ElementObserver  # noqa: E402
except Exception:
    pass

try:
    from .page_detectors import PageDetectors  # noqa: E402
except Exception:
    pass

try:
    from .intelligent_humanizer import IntelligentHumanizer  # noqa: E402
except Exception:
    pass

__all__ = [
    # Componentes opcionales (pueden ser None si dependencias faltan)
    'ElementObserver',
    'PageDetectors',
    'IntelligentHumanizer',
    # Utilidades siempre disponibles
    'get_project_root',
    'get_config_path',
    'get_config_str',
    'get_results_path',
    'get_session_file',
    'get_session_str',
    'get_fingerprint_file',
    'get_fingerprint_str',
    'get_database_path',
    'get_database_str',
    'ensure_results_dir'
]
