"""
Path Utils - Utilidades para manejo centralizado de rutas del proyecto
Resuelve rutas absolutas basadas en la estructura del proyecto
"""

from pathlib import Path
from typing import Union


def get_project_root() -> Path:
    """
    Obtiene la ruta raíz del proyecto (donde está la carpeta profiles/)
    
    Returns:
        Path: Ruta absoluta al directorio raíz del proyecto
    """
    # Desde cualquier archivo en flow/, la raíz está un nivel arriba
    current_file = Path(__file__)
    flow_dir = current_file.parent.parent  # flow/utils/path_utils.py -> flow/
    project_root = flow_dir.parent  # flow/ -> project_root/
    return project_root.resolve()


def get_config_path(profile: str = "default") -> Path:
    """
    Obtiene la ruta absoluta al archivo de configuración
    
    Args:
        profile: Nombre del perfil (default: "default")
        
    Returns:
        Path: Ruta absoluta al config.json
    """
    return get_project_root() / "profiles" / profile / "config.json"


def get_results_path() -> Path:
    """
    Obtiene la ruta absoluta al directorio de resultados
    
    Returns:
        Path: Ruta absoluta al directorio flow/results/
    """
    return get_project_root() / "flow" / "results"


def get_session_file() -> Path:
    """
    Obtiene la ruta absoluta al archivo de sesión de Facebook
    
    Returns:
        Path: Ruta absoluta al facebook_session.json
    """
    return get_results_path() / "facebook_session.json"


def get_fingerprint_file() -> Path:
    """
    Obtiene la ruta absoluta al archivo de fingerprint persistente
    
    Returns:
        Path: Ruta absoluta al persistent_fingerprint.json
    """
    return get_results_path() / "persistent_fingerprint.json"


def get_database_path() -> Path:
    """
    Obtiene la ruta absoluta a la base de datos
    
    Returns:
        Path: Ruta absoluta al groups.db
    """
    return get_results_path() / "groups.db"


def ensure_results_dir() -> Path:
    """
    Asegura que el directorio de resultados existe y lo retorna
    
    Returns:
        Path: Ruta absoluta al directorio de resultados
    """
    results_dir = get_results_path()
    results_dir.mkdir(parents=True, exist_ok=True)
    return results_dir


# Funciones de conveniencia para compatibilidad con código existente
def get_config_str(profile: str = "default") -> str:
    """Retorna la ruta de config como string"""
    return str(get_config_path(profile))


def get_session_str() -> str:
    """Retorna la ruta de sesión como string"""
    return str(get_session_file())


def get_fingerprint_str() -> str:
    """Retorna la ruta de fingerprint como string"""
    return str(get_fingerprint_file())


def get_database_str() -> str:
    """Retorna la ruta de base de datos como string"""
    return str(get_database_path())
