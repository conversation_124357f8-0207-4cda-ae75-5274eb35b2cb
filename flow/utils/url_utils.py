"""
URL Utilities - Funciones de utilidad para manejo de URLs
"""

from typing import Dict, Optional


def ensure_url_parameters(url: str, params: Optional[Dict[str, str]] = None) -> str:
    """
    Asegura que la URL tenga los parámetros necesarios.
    
    Args:
        url: URL original
        params: Dict con parámetros a asegurar
        
    Returns:
        URL con parámetros
    """
    if params is None:
        params = {"sorting_setting": "CHRONOLOGICAL"}
        
    # Si la URL ya tiene parámetros
    if '?' in url:
        base_url, query_string = url.split('?', 1)
        # Parsear parámetros existentes
        existing_params = {}
        if query_string:
            for item in query_string.split('&'):
                if '=' in item:
                    k, v = item.split('=', 1)
                    existing_params[k] = v
        
        # Actualizar con nuevos parámetros
        existing_params.update(params)
        
        # Reconstruir URL
        new_query = '&'.join([f"{k}={v}" for k, v in existing_params.items()])
        return f"{base_url}?{new_query}"
    else:
        # Si no tiene parámetros, añadirlos
        new_query = '&'.join([f"{k}={v}" for k, v in params.items()])
        return f"{url}?{new_query}"


def ensure_chronological_sorting(url: str) -> str:
    """
    Asegura que la URL tenga el parámetro de ordenación cronológica.
    
    Args:
        url: URL del grupo de Facebook
        
    Returns:
        URL con parámetro sorting_setting=CHRONOLOGICAL
    """
    return ensure_url_parameters(url, {"sorting_setting": "CHRONOLOGICAL"})
