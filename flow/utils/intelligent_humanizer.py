"""
Sistema de humanización inteligente con patrones randomizados y configuración
"""
import json
import random
import time
from typing import Dict, Any, List
from playwright.sync_api import Page
from .humanization import FacebookHumanizer
from .path_utils import get_config_str

class IntelligentHumanizer(FacebookHumanizer):
    """Humanizador inteligente con patrones randomizados y configuración"""
    
    def __init__(self, page_detectors=None, config_path=None):
        """
        Inicializar humanizador inteligente
        
        Args:
            page_detectors: Instancia de PageDetectors
            config_path: Ruta al archivo de configuración
        """
        super().__init__(page_detectors)
        # Usar ruta absoluta si no se especifica
        if config_path is None:
            config_path = get_config_str()
        self.config_path = config_path
        self.config = self._load_config()
        self.session_start_time = None
        self.session_end_time = None
        self.like_used_in_session = False  # Control de 1 like por sesión
        
    def _load_config(self) -> Dict[str, Any]:
        """Cargar configuración desde JSON"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('humanization', {})
        except Exception as e:
            print(f"⚠️ Error cargando config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Configuración por defecto si no se puede cargar"""
        return {
            "home_time": {"min_minutes": 5, "max_minutes": 15},
            "probabilities": {"like_probability": 0.25, "scroll_probability": 0.8, "feed_click_probability": 0.15},
            "scroll_config": {"min_rounds": 1, "max_rounds": 4, "min_duration_seconds": 2, "max_duration_seconds": 8, "scroll_distance_min": 200, "scroll_distance_max": 600},
            "timing": {"action_delay_min": 3, "action_delay_max": 12, "between_activities_min": 15, "between_activities_max": 45},
            "patterns": {"available_activities": ["scroll", "like", "feed_click", "pause"], "pattern_weights": {"scroll": 0.4, "like": 0.25, "feed_click": 0.15, "pause": 0.2}}
        }
    
    def start_home_session(self, page: Page) -> Dict[str, Any]:
        """
        Iniciar sesión de humanización en home con tiempo aleatorio
        
        Args:
            page: Página de Playwright
            
        Returns:
            Dict con resultados de la sesión
        """
        print("🤖 INICIANDO SESIÓN DE HUMANIZACIÓN INTELIGENTE")
        print("=" * 60)
        
        # Calcular tiempo de sesión
        home_time_config = self.config.get('home_time', {})
        min_minutes = home_time_config.get('min_minutes', 5)
        max_minutes = home_time_config.get('max_minutes', 15)
        session_duration_minutes = random.uniform(min_minutes, max_minutes)
        
        self.session_start_time = time.time()
        self.session_end_time = self.session_start_time + (session_duration_minutes * 60)
        self.like_used_in_session = False  # Reset para nueva sesión

        print(f"⏰ Duración de sesión: {session_duration_minutes:.1f} minutos")
        print(f"🏁 Sesión terminará a las: {time.strftime('%H:%M:%S', time.localtime(self.session_end_time))}")
        print(f"❤️ Like disponible: 1 por sesión")
        
        session_results = {
            'session_duration_minutes': session_duration_minutes,
            'activities_performed': [],
            'total_actions': 0,
            'session_completed': False,
            'next_activity': None
        }
        
        # Ejecutar actividades hasta que termine el tiempo
        while time.time() < self.session_end_time:
            remaining_time = (self.session_end_time - time.time()) / 60
            print(f"\n⏱️ Tiempo restante: {remaining_time:.1f} minutos")
            
            # Seleccionar actividad aleatoria
            activity = self._select_random_activity()
            print(f"🎲 Actividad seleccionada: {activity}")
            
            # Ejecutar actividad
            activity_result = self._execute_activity(page, activity)
            activities_performed = session_results.get('activities_performed', [])
            if isinstance(activities_performed, list):
                activities_performed.append({
                    'activity': activity,
                    'timestamp': time.time(),
                    'result': activity_result
                })

            total_actions = session_results.get('total_actions', 0)
            if isinstance(total_actions, int):
                session_results['total_actions'] = total_actions + 1
            
            # Delay entre actividades
            if time.time() < self.session_end_time:
                delay = self._get_between_activities_delay()
                print(f"⏳ Pausa entre actividades: {delay:.1f}s")
                time.sleep(delay)
        
        session_results['session_completed'] = True
        session_results['next_activity'] = 'get_groups'
        
        print(f"\n🎉 Sesión completada!")
        print(f"   📊 Total actividades: {session_results['total_actions']}")
        print(f"   ⏭️ Siguiente actividad: {session_results['next_activity']}")
        
        return session_results
    
    def _select_random_activity(self) -> str:
        """Seleccionar actividad aleatoria basada en pesos"""
        patterns_config = self.config.get('patterns', {})
        activities = patterns_config.get('available_activities', ['scroll', 'like', 'feed_click', 'pause'])
        weights = patterns_config.get('pattern_weights', {})
        
        # Crear lista ponderada
        weighted_activities = []
        for activity in activities:
            weight = weights.get(activity, 0.1)
            weighted_activities.extend([activity] * int(weight * 100))
        
        return random.choice(weighted_activities)

    def ensure_global_time_is_enough(self, session_duration_minutes: float) -> None:
        """Verificar si el tiempo global es suficiente para todas las actividades en sus tiempos máximos"""
        print(f"\n⏱️ VERIFICACIÓN DE TIEMPO GLOBAL")
        print("=" * 50)

        # Obtener configuraciones de tiempo
        timing = self.config.get('timing', {})
        scroll_config = self.config.get('scroll_config', {})

        # Calcular tiempos máximos por actividad
        max_times = {
            'scroll': {
                'rounds': scroll_config.get('max_rounds', 5),
                'time_per_round': timing.get('scroll_delay_max', 3000) / 1000,  # ms a segundos
                'monitoring': timing.get('monitoring_wait_max', 30000) / 1000,
                'pause_after': timing.get('activity_pause_max', 15000) / 1000
            },
            'like': {
                'execution': 10,  # tiempo estimado para like con reacciones
                'monitoring': timing.get('monitoring_wait_max', 30000) / 1000,
                'pause_after': timing.get('activity_pause_max', 15000) / 1000
            },
            'feed_click': {
                'execution': 5,  # tiempo estimado para click
                'monitoring': timing.get('monitoring_wait_max', 30000) / 1000,
                'pause_after': timing.get('activity_pause_max', 15000) / 1000
            },
            'pause': {
                'reading': timing.get('reading_pause_max', 60000) / 1000,  # ms a segundos
                'pause_after': timing.get('activity_pause_max', 15000) / 1000
            }
        }

        # Calcular tiempo mínimo necesario (asumiendo que todas las actividades se ejecuten)
        total_max_time = 0

        print("📊 Tiempos máximos por actividad:")
        for activity, times in max_times.items():
            activity_total = sum(times.values())
            total_max_time += activity_total
            print(f"   {activity}: {activity_total:.1f}s")

        total_max_minutes = total_max_time / 60
        print(f"\n⏱️ Tiempo total máximo necesario: {total_max_minutes:.1f} minutos")
        print(f"⏱️ Tiempo global configurado: {session_duration_minutes:.1f} minutos")

        if session_duration_minutes < total_max_minutes:
            deficit = total_max_minutes - session_duration_minutes
            print(f"\n⚠️ TIEMPO INSUFICIENTE!")
            print(f"❌ Déficit: {deficit:.1f} minutos")
            print(f"💡 Mínimo recomendado: {total_max_minutes:.1f} minutos")
            print(f"\n🔒 BLOQUEADO TEMPORALMENTE")
            print("=" * 40)
            print("⚠️ El tiempo global no es suficiente para ejecutar todas las actividades")
            print("⚠️ en sus tiempos máximos. Esto puede causar comportamiento acelerado.")
            print(f"⚠️ Se necesitan al menos {total_max_minutes:.1f} minutos.")
            print("\n📋 Opciones:")
            print("   1. Presiona Enter para continuar de todos modos")
            print("   2. Cancela (Ctrl+C) y ajusta la configuración")

            try:
                input("\n⏳ Presiona Enter para continuar o Ctrl+C para cancelar...")
                print("✅ Continuando con tiempo insuficiente...")
            except KeyboardInterrupt:
                print("\n❌ Cancelado por el usuario")
                raise
        else:
            surplus = session_duration_minutes - total_max_minutes
            print(f"✅ Tiempo suficiente! Margen: {surplus:.1f} minutos")

    def _execute_activity(self, page: Page, activity: str) -> Dict[str, Any]:
        """Ejecutar actividad específica con probabilidades"""
        print(f"\n🔄 Ejecutando: {activity}")
        
        if activity == 'scroll':
            return self._execute_scroll_activity(page)
        elif activity == 'like':
            return self._execute_like_activity(page)
        elif activity == 'feed_click':
            return self._execute_feed_click_activity(page)
        elif activity == 'pause':
            return self._execute_pause_activity(page)
        else:
            print(f"   ⚠️ Actividad desconocida: {activity}")
            return {'success': False, 'reason': 'unknown_activity'}
    
    def _execute_scroll_activity(self, page: Page) -> Dict[str, Any]:
        """Ejecutar actividad de scroll con configuración"""
        probabilities = self.config.get('probabilities', {})
        scroll_probability = probabilities.get('scroll_probability', 0.8)
        
        if random.random() > scroll_probability:
            print(f"   🎲 Scroll omitido (probabilidad: {scroll_probability})")
            return {'success': False, 'reason': 'probability_skip'}
        
        scroll_config = self.config.get('scroll_config', {})
        rounds = random.randint(
            scroll_config.get('min_rounds', 1),
            scroll_config.get('max_rounds', 4)
        )
        
        print(f"   📜 Ejecutando {rounds} rondas de scroll")
        
        for i in range(rounds):
            print(f"   📜 Ronda {i+1}/{rounds}")
            
            # Scroll con distancia aleatoria
            distance = random.randint(
                scroll_config.get('scroll_distance_min', 200),
                scroll_config.get('scroll_distance_max', 600)
            )
            
            page.mouse.wheel(delta_x=0, delta_y=distance)
            
            # Duración aleatoria
            duration = random.uniform(
                scroll_config.get('min_duration_seconds', 2),
                scroll_config.get('max_duration_seconds', 8)
            )
            time.sleep(duration)
        
        # Monitoreo después del scroll
        monitoring_results = self.monitor_and_recovery(page, f"scroll ({rounds} rondas)")
        
        return {
            'success': True,
            'rounds': rounds,
            'monitoring_results': monitoring_results
        }
    
    def _execute_like_activity(self, page: Page) -> Dict[str, Any]:
        """Ejecutar actividad de like con probabilidad 1/5 (20%) por loop"""
        if self.like_used_in_session:
            print(f"   🎲 Like omitido (ya usado en esta sesión)")
            return {'success': False, 'reason': 'already_used_in_session'}

        # Aplicar probabilidad de like (1/5 = 20%)
        probabilities = self.config.get('probabilities', {})
        like_probability = probabilities.get('like_probability', 0.2)

        if random.random() > like_probability:
            print(f"   🎲 Like omitido por probabilidad ({like_probability*100:.0f}% = 1/{int(1/like_probability)})")
            return {'success': False, 'reason': 'probability_skip'}

        print("   ❤️ Ejecutando like...")
        result = self.like_with_reactions(page)

        if result.get('success', False):
            self.like_used_in_session = True
            print(f"   ✅ Like usado - no más likes en esta sesión")

        return result
    
    def _execute_feed_click_activity(self, page: Page) -> Dict[str, Any]:
        """Ejecutar actividad de click en feed con probabilidad"""
        probabilities = self.config.get('probabilities', {})
        feed_probability = probabilities.get('feed_click_probability', 0.15)
        
        if random.random() > feed_probability:
            print(f"   🎲 Feed click omitido (probabilidad: {feed_probability})")
            return {'success': False, 'reason': 'probability_skip'}
        
        print("   📰 Ejecutando click en feed...")
        return self.click_feed_article(page)
    
    def _execute_pause_activity(self, page: Page) -> Dict[str, Any]:
        """Ejecutar pausa (simular lectura)"""
        timing_config = self.config.get('timing', {})
        pause_duration = random.uniform(
            timing_config.get('between_activities_min', 15),
            timing_config.get('between_activities_max', 45)
        )
        
        print(f"   ⏸️ Pausa de lectura: {pause_duration:.1f}s")
        time.sleep(pause_duration)
        
        return {
            'success': True,
            'pause_duration': pause_duration
        }
    
    def _get_between_activities_delay(self) -> float:
        """Obtener delay entre actividades"""
        timing_config = self.config.get('timing', {})
        return random.uniform(
            timing_config.get('action_delay_min', 3),
            timing_config.get('action_delay_max', 12)
        )
    
    def get_groups(self) -> Dict[str, Any]:
        """Placeholder para siguiente actividad"""
        print("\n🔄 TRANSICIÓN A SIGUIENTE ACTIVIDAD")
        print("=" * 40)
        print("📋 PLACEHOLDER: get_groups()")
        print("   ➡️ Ahora debería ir a buscar grupos")

        return {
            'success': True,
            'message': 'Ahora debería ir a buscar grupos',
            'next_step': 'groups_navigation'
        }

    def await_for_user_end(self) -> None:
        """Esperar a que el usuario presione Enter para cerrar"""
        print("\n⏸️ NAVEGADOR ABIERTO PARA INSPECCIÓN")
        print("=" * 40)
        print("🔍 Puedes inspeccionar el estado actual del navegador")
        print("📋 Presiona Enter cuando quieras cerrar...")

        try:
            input()
            print("✅ Cerrando navegador...")
        except KeyboardInterrupt:
            print("\n⚠️ Interrupción detectada, cerrando...")

    def skip_now_for_test_purposes(self, page: Page, test_name: str = "test") -> bool:
        """
        Saltar humanización para ir directamente a test
        Verifica que estemos en home y luego permite saltar

        Args:
            page: Página actual
            test_name: Nombre del test a ejecutar

        Returns:
            bool: True si se puede saltar, False si no
        """
        print(f"\n🚀 SKIP PARA TEST: {test_name}")
        print("=" * 50)

        # Verificar que estemos en home
        from .page_detectors import PageDetectors
        page_detectors = PageDetectors()
        home_detection = page_detectors.is_home(page)

        if home_detection['is_home']:
            print("✅ Home confirmado - se puede saltar humanización")
            print(f"🏠 URL: {home_detection['details']['url']}")
            print(f"📊 Articles: {home_detection['details']['articles_count']}")
            print(f"🎯 Saltando a test: {test_name}")
            return True
        else:
            print("❌ No estamos en home - no se puede saltar")
            print(f"📍 URL actual: {page.url}")
            return False
