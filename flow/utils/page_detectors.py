#!/usr/bin/env python3
"""
Page Detectors - Sistema modular para detectar en qué página estamos
Incluye detectores para: login, home, groups, etc.
"""

import time
from typing import Dict, Optional, Any

class PageDetectors:
    """Detectores modulares para diferentes páginas de Facebook"""
    
    def __init__(self):
        self.detection_cache = {}
        self.cache_timeout = 30  # 30 segundos
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Verifica si el cache es válido"""
        if cache_key not in self.detection_cache:
            return False
        
        cached_time = self.detection_cache[cache_key].get('timestamp', 0)
        return (time.time() - cached_time) < self.cache_timeout
    
    def _cache_result(self, cache_key: str, result: bool, details: Dict = None) -> bool:
        """Guarda resultado en cache"""
        self.detection_cache[cache_key] = {
            'result': result,
            'details': details or {},
            'timestamp': time.time()
        }
        return result
    
    def is_home(self, page) -> Dict[str, Any]:
        """
        Detecta si estamos en el HOME de Facebook
        
        Criterios:
        - URL contiene facebook.com (sin login)
        - Al menos 1 elemento [role='article'] presente
        
        Returns:
            Dict con resultado y detalles
        """
        cache_key = f"is_home_{id(page)}"
        
        if self._is_cache_valid(cache_key):
            cached = self.detection_cache[cache_key]
            return {
                'is_home': cached['result'],
                'details': cached['details'],
                'cached': True
            }
        
        try:
            # Verificar URL
            current_url = page.url
            url_valid = (
                'facebook.com' in current_url.lower() and 
                'login' not in current_url.lower()
            )
            
            # Verificar presencia de articles (posts)
            articles_count = 0
            if url_valid:
                try:
                    articles = page.locator("[role='article']")
                    articles_count = articles.count()
                except:
                    articles_count = 0
            
            # Criterio de éxito
            is_home_page = url_valid and articles_count >= 1
            
            details = {
                'url': current_url,
                'url_valid': url_valid,
                'articles_count': articles_count,
                'articles_required': 1,
                'detection_time': time.time()
            }
            
            # Cache y retorno
            self._cache_result(cache_key, is_home_page, details)
            
            return {
                'is_home': is_home_page,
                'details': details,
                'cached': False
            }
            
        except Exception as e:
            error_details = {
                'error': str(e),
                'url': getattr(page, 'url', 'unknown'),
                'detection_time': time.time()
            }
            
            self._cache_result(cache_key, False, error_details)
            
            return {
                'is_home': False,
                'details': error_details,
                'cached': False
            }
    
    def is_login(self, page) -> Dict[str, Any]:
        """
        Detecta si estamos en la página de LOGIN
        
        Criterios:
        - URL contiene 'login' o redirige a login
        - Presencia de campos de email/password
        """
        cache_key = f"is_login_{id(page)}"
        
        if self._is_cache_valid(cache_key):
            cached = self.detection_cache[cache_key]
            return {
                'is_login': cached['result'],
                'details': cached['details'],
                'cached': True
            }
        
        try:
            current_url = page.url
            url_indicates_login = 'login' in current_url.lower()
            
            # Buscar campos de login
            email_field = False
            password_field = False
            
            try:
                email_field = page.locator("input[type='email'], input[name*='email']").count() > 0
                password_field = page.locator("input[type='password']").count() > 0
            except:
                pass
            
            is_login_page = url_indicates_login or (email_field and password_field)
            
            details = {
                'url': current_url,
                'url_indicates_login': url_indicates_login,
                'email_field_present': email_field,
                'password_field_present': password_field,
                'detection_time': time.time()
            }
            
            self._cache_result(cache_key, is_login_page, details)
            
            return {
                'is_login': is_login_page,
                'details': details,
                'cached': False
            }
            
        except Exception as e:
            error_details = {
                'error': str(e),
                'url': getattr(page, 'url', 'unknown'),
                'detection_time': time.time()
            }
            
            self._cache_result(cache_key, False, error_details)
            
            return {
                'is_login': False,
                'details': error_details,
                'cached': False
            }
    
    def is_groups(self, page) -> Dict[str, Any]:
        """
        Detecta si estamos en la página de GROUPS
        
        Criterios:
        - URL contiene 'groups'
        - Presencia de elementos específicos de grupos
        """
        cache_key = f"is_groups_{id(page)}"
        
        if self._is_cache_valid(cache_key):
            cached = self.detection_cache[cache_key]
            return {
                'is_groups': cached['result'],
                'details': cached['details'],
                'cached': True
            }
        
        try:
            current_url = page.url
            url_indicates_groups = 'groups' in current_url.lower()
            
            # Buscar elementos específicos de grupos
            groups_elements = 0
            try:
                groups_elements = page.locator("[aria-label*='Groups'], [data-testid*='group']").count()
            except:
                pass
            
            is_groups_page = url_indicates_groups or groups_elements > 0
            
            details = {
                'url': current_url,
                'url_indicates_groups': url_indicates_groups,
                'groups_elements': groups_elements,
                'detection_time': time.time()
            }
            
            self._cache_result(cache_key, is_groups_page, details)
            
            return {
                'is_groups': is_groups_page,
                'details': details,
                'cached': False
            }
            
        except Exception as e:
            error_details = {
                'error': str(e),
                'url': getattr(page, 'url', 'unknown'),
                'detection_time': time.time()
            }
            
            self._cache_result(cache_key, False, error_details)
            
            return {
                'is_groups': False,
                'details': error_details,
                'cached': False
            }

    def is_group_page(self, page) -> Dict[str, Any]:
        """
        Detecta si estamos en la página de UN grupo específico (no listado)

        Criterios simplificados:
        - URL: facebook.com/groups/[slug]/
        - Al menos 1 artículo presente
        """
        cache_key = f"is_group_page_{id(page)}"

        if self._is_cache_valid(cache_key):
            cached = self.detection_cache[cache_key]
            return {
                'is_group_page': cached['result'],
                'details': cached['details'],
                'cached': True
            }

        try:
            current_url = page.url

            # Verificar que URL sea de grupo específico (groups/slug/)
            url_is_group = '/groups/' in current_url and current_url.count('/') >= 4

            # Verificar presencia de artículos (posts del grupo)
            articles_count = 0
            try:
                articles_count = page.locator("[role='article']").count()
            except:
                pass

            # Criterio simplificado: URL de grupo + al menos 1 artículo
            is_group_page = url_is_group and articles_count >= 1

            details = {
                'url_is_group': url_is_group,
                'articles_count': articles_count,
                'current_url': current_url,
                'simplified_criteria': True
            }

            self._cache_result(cache_key, is_group_page, details)
            return {
                'is_group_page': is_group_page,
                'details': details,
                'cached': False
            }

        except Exception as e:
            error_details = {
                'error': str(e),
                'url': getattr(page, 'url', 'unknown'),
                'detection_time': time.time()
            }

            self._cache_result(cache_key, False, error_details)
            return {
                'is_group_page': False,
                'details': error_details,
                'cached': False
            }
    
    def detect_current_page(self, page) -> Dict[str, Any]:
        """
        Detecta automáticamente en qué página estamos
        
        Returns:
            Dict con el tipo de página detectado y detalles
        """
        detections = {
            'login': self.is_login(page),
            'home': self.is_home(page),
            'groups': self.is_groups(page)
        }
        
        # Determinar página actual
        current_page = 'unknown'
        for page_type, detection in detections.items():
            if detection[f'is_{page_type}']:
                current_page = page_type
                break
        
        return {
            'current_page': current_page,
            'detections': detections,
            'url': getattr(page, 'url', 'unknown'),
            'detection_time': time.time()
        }
    
    def clear_cache(self):
        """Limpia el cache de detecciones"""
        self.detection_cache.clear()

# Instancia global para uso fácil
page_detectors = PageDetectors()



