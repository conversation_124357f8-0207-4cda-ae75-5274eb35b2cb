#!/usr/bin/env python3
"""
Observer Utils - Mini framework de observación de elementos
Sistema centralizado para observar y monitorear elementos de Facebook
"""

import time
import json
from typing import Dict, List, Any, Optional

# Fallback condicional para permitir que el núcleo funcione sin Playwright instalado.
try:
    from playwright.sync_api import Page  # type: ignore
    _PLAYWRIGHT_AVAILABLE = True
except Exception:
    _PLAYWRIGHT_AVAILABLE = False

    class _LocatorStub:
        """Stub de Locator para modo degradado sin Playwright."""
        def count(self): return 0
        def nth(self, i: int): return self
        def is_visible(self): return False
        def is_enabled(self): return False
        def text_content(self): return ""
        def evaluate(self, script): return None
        def get_attribute(self, name): return None

    class Page:  # type: ignore
        """Stub mínima de Page cuando Playwright no está instalado.
        
        Proporciona sólo los atributos/métodos usados por ElementObserver en modo seguro.
        """
        def __init__(self):
            self.url = "about:blank"
        def goto(self, url: str):
            self.url = url
        def wait_for_timeout(self, ms: int):
            pass
        def locator(self, selector: str):
            return _LocatorStub()
        def evaluate(self, script: str):
            return None

class ElementObserver:
    """Mini framework para observar elementos de Facebook"""

    def __init__(self, page: Page, target_url: Optional[str] = None):
        self.page = page
        self.target_url = target_url or page.url
        self.observations: List[Dict[str, Any]] = []
        self.start_time = time.time()

        # Navegar a URL objetivo si es diferente a la actual
        if not _PLAYWRIGHT_AVAILABLE:
            print("⚠️ Playwright no disponible - ElementObserver en modo degradado (no se realizarán observaciones reales).")
        if target_url and target_url != page.url:
            print(f"🌐 Navegando a URL objetivo: {target_url}")
            try:
                page.goto(target_url)
                page.wait_for_timeout(2000)  # Esperar carga
            except Exception:
                # En modo degradado o fallo silencioso
                pass

    def observe_element(self, selector: str, element_type: str, description: str = "") -> Dict[str, Any]:
        """
        Observar un elemento específico
        
        Args:
            selector: Selector CSS/XPath del elemento
            element_type: Tipo de elemento (button, link, input, etc.)
            description: Descripción del elemento
            
        Returns:
            Dict con información del elemento observado
        """
        observation = {
            'timestamp': time.time(),
            'elapsed_time': time.time() - self.start_time,
            'selector': selector,
            'element_type': element_type,
            'description': description,
            'url': self.page.url,
            'found': False,
            'count': 0,
            'visible_count': 0,
            'clickable_count': 0,
            'elements_info': []
        }
        
        try:
            # Buscar elementos
            elements = self.page.locator(selector)
            count = elements.count()
            observation['found'] = count > 0
            observation['count'] = count
            
            # Analizar elementos encontrados
            if count > 0:
                for i in range(min(count, 5)):  # Máximo 5 elementos
                    try:
                        element = elements.nth(i)
                        is_visible = element.is_visible()
                        is_enabled = element.is_enabled()
                        
                        if is_visible:
                            visible_count = observation.get('visible_count', 0)
                            if isinstance(visible_count, int):
                                observation['visible_count'] = visible_count + 1

                        if is_visible and is_enabled:
                            clickable_count = observation.get('clickable_count', 0)
                            if isinstance(clickable_count, int):
                                observation['clickable_count'] = clickable_count + 1
                        
                        # Información detallada del elemento
                        text_content = element.text_content()
                        tag_name = element.evaluate("el => el.tagName")
                        classes = element.get_attribute('class')
                        aria_label = element.get_attribute('aria-label')

                        element_info = {
                            'index': i,
                            'visible': is_visible,
                            'enabled': is_enabled,
                            'text': text_content[:100] if text_content and is_visible else "",
                            'tag_name': tag_name if tag_name and is_visible else "",
                            'classes': classes[:100] if classes and is_visible else "",
                            'aria_label': aria_label if aria_label and is_visible else ""
                        }
                        elements_info = observation.get('elements_info', [])
                        if isinstance(elements_info, list):
                            elements_info.append(element_info)
                        
                    except Exception as e:
                        elements_info = observation.get('elements_info', [])
                        if isinstance(elements_info, list):
                            elements_info.append({
                                'index': i,
                                'error': str(e)
                            })
                        
        except Exception as e:
            observation['error'] = str(e)
        
        # Guardar observación
        self.observations.append(observation)
        
        return observation
    
    def monitor_changes(self, duration_seconds: int, check_interval: float = 1.0) -> List[Dict[str, Any]]:
        """
        Monitorear cambios en la página por un tiempo determinado
        
        Args:
            duration_seconds: Duración del monitoreo en segundos
            check_interval: Intervalo entre verificaciones en segundos
            
        Returns:
            Lista de cambios detectados
        """
        changes = []
        start_time = time.time()
        initial_url = self.page.url
        
        print(f"🔍 Iniciando monitoreo por {duration_seconds}s...")
        
        while (time.time() - start_time) < duration_seconds:
            current_time = time.time()
            current_url = self.page.url
            
            # Detectar cambio de URL
            if current_url != initial_url:
                change = {
                    'timestamp': current_time,
                    'elapsed_time': current_time - self.start_time,
                    'type': 'url_change',
                    'from_url': initial_url,
                    'to_url': current_url,
                    'description': f"URL cambió de {initial_url} a {current_url}"
                }
                changes.append(change)
                initial_url = current_url
                print(f"   🔄 URL cambió: {current_url}")
            
            # Detectar dialogs/modals
            try:
                dialogs = self.page.locator("[role='dialog'], .modal, [data-testid*='modal']")
                dialog_count = dialogs.count()
                
                if dialog_count > 0:
                    change = {
                        'timestamp': current_time,
                        'elapsed_time': current_time - self.start_time,
                        'type': 'dialog_detected',
                        'count': dialog_count,
                        'description': f"Detectados {dialog_count} dialogs/modals"
                    }
                    changes.append(change)
                    print(f"   🚨 Dialog detectado: {dialog_count} elementos")
                    
            except Exception as e:
                pass
            
            # Esperar antes de siguiente verificación
            time.sleep(check_interval)
        
        print(f"✅ Monitoreo completado. {len(changes)} cambios detectados")
        return changes
    
    def observe_critical_elements(self) -> Dict[str, Any]:
        """
        Observar elementos críticos de Facebook
        
        Returns:
            Dict con observaciones de elementos críticos
        """
        critical_elements = {
            'like_buttons': {
                'selector': "[aria-label*='Me gusta'], [data-testid*='like']",
                'type': 'button',
                'description': 'Botones de Me gusta'
            },
            'articles': {
                'selector': "[role='article']",
                'type': 'container',
                'description': 'Posts/artículos del feed'
            },
            'navigation': {
                'selector': "[role='navigation']",
                'type': 'navigation',
                'description': 'Elementos de navegación'
            },
            'dialogs': {
                'selector': "[role='dialog']",
                'type': 'modal',
                'description': 'Dialogs/modales'
            },
            'messenger_icons': {
                'selector': "[aria-label*='Messenger'], [data-testid*='messenger']",
                'type': 'link',
                'description': 'Iconos de Messenger'
            }
        }
        
        results = {}
        print("🔍 Observando elementos críticos...")
        
        for element_name, config in critical_elements.items():
            print(f"   📊 {config['description']}...")
            observation = self.observe_element(
                config['selector'],
                config['type'],
                config['description']
            )
            results[element_name] = observation
            print(f"      ✅ Encontrados: {observation['count']} ({observation['visible_count']} visibles)")
        
        return results

    def observe_must_be(self, selector: str, element_type: str, description: str = "",
                       timeout_seconds: int = 30, check_interval: float = 0.5,
                       use_progressive_scroll: bool = False) -> Dict[str, Any]:
        """
        Observar elementos que DEBEN existir - esperar cambios en DOM hasta que aparezcan
        Para elementos que el líder ha inspeccionado y confirmado que existen

        Args:
            selector: Selector CSS/XPath del elemento que debe existir
            element_type: Tipo de elemento (button, link, input, etc.)
            description: Descripción del elemento
            timeout_seconds: Tiempo máximo de espera en segundos
            check_interval: Intervalo entre verificaciones en segundos
            use_progressive_scroll: Si True, hace scroll progresivo para cargar contenido dinámico

        Returns:
            Dict con información del elemento y proceso de espera
        """
        observation = {
            'timestamp': time.time(),
            'elapsed_time': time.time() - self.start_time,
            'selector': selector,
            'element_type': element_type,
            'description': description,
            'url': self.page.url,
            'must_exist': True,
            'found': False,
            'wait_time': 0,
            'attempts': 0,
            'final_count': 0,
            'final_visible_count': 0,
            'dom_changes_detected': [],
            'scroll_attempts': 0,
            'progressive_scroll': use_progressive_scroll,
            'success': False,
            'collected_links': [],  # Para recolectar links progresivamente
            'max_collected': 0      # Máximo número de links recolectados
        }

        print(f"⏳ Esperando elemento OBLIGATORIO: {description}")
        print(f"   🎯 Selector: {selector}")
        print(f"   ⏱️ Timeout: {timeout_seconds}s")

        start_wait = time.time()

        while (time.time() - start_wait) < timeout_seconds:
            attempts = observation.get('attempts', 0)
            if isinstance(attempts, int):
                observation['attempts'] = attempts + 1
            current_time = time.time()
            observation['wait_time'] = current_time - start_wait

            try:
                # Verificar si el elemento existe ahora
                elements = self.page.locator(selector)
                count = elements.count()

                # Si usamos scroll progresivo, recolectar links progresivamente
                if use_progressive_scroll and count > 0:
                    current_links = []
                    for i in range(min(count, 50)):  # Recolectar hasta 50 links
                        try:
                            element = elements.nth(i)
                            if element.is_visible():
                                href = element.get_attribute('href')
                                text = element.text_content()
                                if href:
                                    link_info = {
                                        'href': href,
                                        'text': text[:100] if text else '',
                                        'index': i,
                                        'collected_at_attempt': observation['attempts']
                                    }
                                    current_links.append(link_info)
                        except:
                            pass

                    # Actualizar colección de links
                    if current_links:
                        observation['collected_links'] = current_links
                        max_collected = observation.get('max_collected', 0)
                        if isinstance(max_collected, int):
                            observation['max_collected'] = max(max_collected, len(current_links))
                        attempts = observation.get('attempts', 0)
                        if isinstance(attempts, int) and attempts % 5 == 0:  # Log cada 5 intentos
                            print(f"   🔗 Recolectados {len(current_links)} links en intento {attempts}")

                if count > 0:
                    # Elemento encontrado - verificar visibilidad
                    visible_count = 0
                    fallback_texts = []

                    for i in range(min(count, 3)):  # Verificar primeros 3
                        try:
                            element = elements.nth(i)
                            if element.is_visible():
                                visible_count += 1
                                # Guardar texto para fallback
                                try:
                                    text = element.text_content()
                                    fallback_texts.append({
                                        'index': i,
                                        'text': text,
                                        'element': element
                                    })
                                except:
                                    pass
                        except:
                            pass

                    observation['found'] = True
                    observation['final_count'] = count
                    observation['final_visible_count'] = visible_count
                    observation['fallback_texts'] = fallback_texts
                    observation['success'] = True

                    print(f"   ✅ Elemento encontrado! Count: {count}, Visible: {visible_count}")
                    print(f"   ⏱️ Tiempo de espera: {observation['wait_time']:.1f}s")

                    # Mostrar textos de fallback
                    if fallback_texts:
                        print(f"   📋 Textos disponibles para fallback:")
                        for i, fb in enumerate(fallback_texts):
                            if isinstance(fb, dict) and 'text' in fb:
                                text_value = fb['text']
                                if isinstance(text_value, str):
                                    print(f"      [{i}] {text_value[:100]}")

                    # Si no usamos scroll progresivo, terminar inmediatamente
                    if not use_progressive_scroll:
                        break
                    # Si usamos scroll progresivo, terminar cuando tengamos suficientes links recolectados
                    max_collected = observation.get('max_collected', 0)
                    if isinstance(max_collected, int) and max_collected >= 25:  # Si hemos recolectado suficientes links
                        print(f"   🎯 Terminando: {max_collected} links recolectados")
                        break
                    attempts = observation.get('attempts', 0)
                    if isinstance(attempts, int) and attempts >= 40:
                        # Límite máximo de intentos para evitar bucles infinitos
                        print(f"   ⏰ Límite de intentos alcanzado: {attempts}")
                        break

                # Scroll progresivo más frecuente y sutil
                attempts = observation.get('attempts', 0)
                if use_progressive_scroll and isinstance(attempts, int) and attempts % 2 == 0:  # Cada 2 intentos (1 segundo)
                    scroll_attempts = observation.get('scroll_attempts', 0)
                    if isinstance(scroll_attempts, int):
                        observation['scroll_attempts'] = scroll_attempts + 1

                    try:
                        # Estrategia de scroll para hacer elementos visibles
                        scroll_attempts = observation.get('scroll_attempts', 0)
                        scroll_cycle = scroll_attempts % 6 if isinstance(scroll_attempts, int) else 0

                        if scroll_cycle == 1:
                            # Scroll hacia abajo gradual
                            print(f"   ⬇️ Scroll progresivo: hacia abajo gradual")
                            self.page.evaluate("window.scrollBy(0, 400)")

                        elif scroll_cycle == 2:
                            # Scroll hacia abajo más
                            print(f"   ⬇️ Scroll progresivo: hacia abajo medio")
                            self.page.evaluate("window.scrollBy(0, 600)")

                        elif scroll_cycle == 3:
                            # Scroll hacia arriba para hacer visibles elementos cargados
                            print(f"   ⬆️ Scroll progresivo: hacia arriba para visibilidad")
                            self.page.evaluate("window.scrollBy(0, -300)")

                        elif scroll_cycle == 4:
                            # Scroll hacia abajo grande
                            print(f"   ⬇️ Scroll progresivo: hacia abajo grande")
                            self.page.evaluate("window.scrollBy(0, 800)")

                        elif scroll_cycle == 5:
                            # Scroll hasta el final para forzar carga completa
                            print(f"   🔄 Scroll progresivo: hasta el final para carga completa")
                            self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")

                        else:
                            # Volver al inicio para revisar elementos cargados
                            print(f"   🔝 Scroll progresivo: volver al inicio")
                            self.page.evaluate("window.scrollTo(0, 0)")

                        # Esperar tiempo variable según el tipo de scroll
                        if scroll_cycle in [3, 5, 0]:  # Scrolls especiales necesitan más tiempo
                            self.page.wait_for_timeout(3000)  # 3 segundos
                        else:
                            self.page.wait_for_timeout(2000)  # 2 segundos

                    except Exception as e:
                        print(f"   ❌ Error en scroll sutil: {e}")

                # Detectar cambios en DOM mientras esperamos
                try:
                    # Verificar si hay nuevos elementos apareciendo
                    all_elements = self.page.locator("*").count()
                    attempts = observation.get('attempts', 0)
                    scroll_attempts = observation.get('scroll_attempts', 0)
                    dom_change = {
                        'timestamp': current_time,
                        'total_elements': all_elements,
                        'attempt': attempts,
                        'scroll_attempts': scroll_attempts
                    }
                    dom_changes = observation.get('dom_changes_detected', [])
                    if isinstance(dom_changes, list):
                        dom_changes.append(dom_change)

                    if isinstance(attempts, int) and attempts % 10 == 0:  # Log cada 5 segundos aprox
                        print(f"   🔄 Intento {attempts}: {all_elements} elementos en DOM, {scroll_attempts} scrolls")

                except Exception as e:
                    pass

            except Exception as e:
                observation['error'] = str(e)
                print(f"   ❌ Error en intento {observation['attempts']}: {e}")

            # Esperar antes de siguiente verificación
            time.sleep(check_interval)

        # Resultado final
        if not observation['success']:
            observation['final_count'] = 0
            observation['final_visible_count'] = 0
            print(f"   ❌ ELEMENTO NO ENCONTRADO después de {timeout_seconds}s")
            print(f"   ⚠️ Este elemento DEBE existir según inspección manual")

        # Guardar observación
        self.observations.append(observation)

        return observation

    def get_observations(self) -> List[Dict[str, Any]]:
        """Retornar todas las observaciones recolectadas"""
        return self.observations
    
    def get_summary(self) -> Dict[str, Any]:
        """
        Obtener resumen de todas las observaciones
        
        Returns:
            Dict con resumen estadístico
        """
        if not self.observations:
            return {'total_observations': 0, 'message': 'No hay observaciones'}
        
        summary = {
            'total_observations': len(self.observations),
            'duration_seconds': time.time() - self.start_time,
            'elements_found': 0,
            'elements_visible': 0,
            'elements_clickable': 0,
            'element_types': {},
            'urls_observed': set(),
            'errors': 0
        }
        
        for obs in self.observations:
            if obs.get('found', False):
                count = obs.get('count', 0)
                visible_count = obs.get('visible_count', 0)
                clickable_count = obs.get('clickable_count', 0)

                if isinstance(count, int):
                    elements_found = summary.get('elements_found', 0)
                    if isinstance(elements_found, int):
                        summary['elements_found'] = elements_found + count
                if isinstance(visible_count, int):
                    elements_visible = summary.get('elements_visible', 0)
                    if isinstance(elements_visible, int):
                        summary['elements_visible'] = elements_visible + visible_count
                if isinstance(clickable_count, int):
                    elements_clickable = summary.get('elements_clickable', 0)
                    if isinstance(elements_clickable, int):
                        summary['elements_clickable'] = elements_clickable + clickable_count

            element_type = obs.get('element_type', 'unknown')
            element_types = summary.get('element_types', {})
            if isinstance(element_types, dict):
                element_types[element_type] = element_types.get(element_type, 0) + 1

            urls_observed = summary.get('urls_observed', set())
            if isinstance(urls_observed, set):
                urls_observed.add(obs.get('url', 'unknown'))

            if 'error' in obs:
                errors = summary.get('errors', 0)
                if isinstance(errors, int):
                    summary['errors'] = errors + 1
        
        urls_observed = summary.get('urls_observed', set())
        if isinstance(urls_observed, set):
            summary['urls_observed'] = list(urls_observed)
        
        return summary
    
    def print_summary(self):
        """Imprimir resumen de observaciones"""
        summary = self.get_summary()
        
        print("\n📊 RESUMEN DE OBSERVACIONES")
        print("=" * 50)
        print(f"⏱️ Duración: {summary['duration_seconds']:.1f}s")
        print(f"📊 Total observaciones: {summary['total_observations']}")
        print(f"🔍 Elementos encontrados: {summary['elements_found']}")
        print(f"👁️ Elementos visibles: {summary['elements_visible']}")
        print(f"👆 Elementos clickeables: {summary['elements_clickable']}")
        print(f"❌ Errores: {summary['errors']}")
        
        if summary['element_types']:
            print("\n📋 Tipos de elementos:")
            for element_type, count in summary['element_types'].items():
                print(f"   {element_type}: {count}")
        
        if summary['urls_observed']:
            print("\n🌐 URLs observadas:")
            for url in summary['urls_observed']:
                print(f"   {url}")
