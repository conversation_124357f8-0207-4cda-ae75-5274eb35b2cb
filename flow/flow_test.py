#!/usr/bin/env python3
# Testing purposes currently being evalueted
"""
Facebook Flow - Orquestador de Grupos
Implementa el flujo completo del orquestador de grupos con:
- Consulta de grupos activos (in_flow = True)
- Randomización de grupos
- Cola temporal en memoria
- Manejo de is_first_run desde BD
- Límite de 10 posts en first run
- Resumen final y actualización de is_first_run

IMPORTANTE (Política interna de pruebas):
- La sección de LOGIN + CARGA DE SESIÓN + CREACIÓN DE CONTEXTO/NAVEGADOR es infraestructura estable que reutilizamos para mantener huella / fingerprint, cookies, storage_state y orden de inicialización CONSISTENTES.
- Motivo técnico y de riesgo: si alteramos fingerprint (UA, viewport, orden de inicialización), storage_state, secuencia de reapertura o el patrón base de creación de browser/context/page, Facebook puede interpretar “saltos” de entorno (como si iniciáramos sesión desde otros dispositivos / ubicaciones) y generar fricción: reautenticaciones, checkpoints o incluso soft bans.
- Por eso NO cambiamos esos datos base durante pruebas normales; solo se tocan para mantenimiento o corrección puntual cuando algo falla a nivel login.
- Los experimentos, refactors y tests se enfocan EXCLUSIVAMENTE DESPUÉS del login exitoso (extracción de posts: observer / queue / comparación).
- Si se necesita instrumentar (nuevas métricas, asserts, hooks), hacerlo como capas externas post-login sin alterar: cookies cargadas, user agent, viewport base, orden de creación de browser/context/page ni timeout base.
- Objetivo: estabilidad + reproducibilidad → misma sesión controlada = datos comparables y menor riesgo de detección.

Resumen rápido:
(1) Login estable (fingerprint + session bootstrap) → NO MODIFICAR en pruebas (solo mantener / corregir si falla).
(2) Zona de pruebas: extracción (QueueProcessor / tracking / comparación) → aquí iteramos rápido.
"""

import argparse
import time
import json
import random
from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List, Callable, Tuple
from login_system import FacebookLoginManager
from utils.page_detectors import PageDetectors
from utils.intelligent_humanizer import IntelligentHumanizer
from utils.path_utils import get_config_str
from groups_tracker import GroupsTracker
from database.database_manager import DatabaseManager
from queue_processor import QueueProcessor
from group_utils import extract_group_slug_from_url, generate_group_name_from_slug, extract_group_info_from_url
from facebook_post_extractor import FacebookPostExtractor
from post_repository import PostRepository


class GroupsOrchestrator:
    """
    Orquestador de grupos que maneja el flujo completo:
    1. Obtener grupos activos (in_flow = True)
    2. Randomizar grupos
    3. Crear cola temporal en memoria
    4. Procesar posts con manejo de is_first_run
    5. Actualizar is_first_run al finalizar
    """

    def __init__(self, user_id: int = 1):
        """
        Inicializa el orquestador de grupos

        Args:
            user_id: ID del usuario (default 1)
        """
        self.user_id = user_id
        self.db_manager = DatabaseManager()
        self.post_repository = PostRepository(self.db_manager.db_path)

        # Cola temporal en memoria
        self.groups_queue: List[Dict[str, Any]] = []
        self.processed_groups: List[Dict[str, Any]] = []

        print(f"🎯 Orquestador de Grupos inicializado para usuario {user_id}")

    def clear_all_posts(self) -> int:
        """
        Borra todos los posts para empezar limpio

        Returns:
            Número de posts eliminados
        """
        print("🧹 Borrando todos los posts existentes...")
        deleted_count = self.db_manager.clear_all_posts(self.user_id)
        print(f"✅ {deleted_count} posts eliminados")
        return deleted_count

    def run_dry_run(self) -> Dict[str, Any]:
        """
        Ejecuta un dry run mostrando el flujo sin procesar posts reales

        Returns:
            Dict con resultados del dry run
        """
        print("\n🏃‍♂️ INICIANDO DRY RUN DEL ORQUESTADOR")
        print("=" * 60)

        # 1. Obtener configuración is_first_run
        setup_config = self.db_manager.get_setup_config()
        is_first_run = setup_config.get('is_first_run', 1) == 1

        print(f"🔍 Configuración actual:")
        print(f"   is_first_run: {is_first_run}")
        print(f"   Límite de posts: {'10 (first run)' if is_first_run else 'Sin límite'}")

        # 2. Obtener grupos activos
        active_groups = self.db_manager.get_groups_in_flow(self.user_id)
        print(f"\n📋 Grupos activos encontrados: {len(active_groups)}")

        if not active_groups:
            return {
                'success': False,
                'message': 'No hay grupos activos (in_flow = True)',
                'is_first_run': is_first_run
            }

        # 3. Mostrar grupos antes del shuffle
        print(f"\n📝 Grupos antes del shuffle:")
        for i, group in enumerate(active_groups):
            print(f"   [{i+1}] {group['nombre']} (ID: {group['id']})")

        # 4. Aplicar shuffle
        shuffled_groups = active_groups.copy()
        random.shuffle(shuffled_groups)

        print(f"\n🔀 Grupos después del shuffle:")
        for i, group in enumerate(shuffled_groups):
            print(f"   [{i+1}] {group['nombre']} (ID: {group['id']})")

        # 5. Crear cola temporal
        groups_queue = []
        for group in shuffled_groups:
            queue_item = {
                'grupo': group['url'],
                'is_processed': False,
                'id': group['id'],
                'nombre': group['nombre']
            }
            groups_queue.append(queue_item)

        print(f"\n🗂️ Cola temporal creada:")
        for i, item in enumerate(groups_queue):
            print(f"   [{i+1}] {item['nombre']}")
            print(f"       URL: {item['grupo']}")
            print(f"       ID: {item['id']}")
            print(f"       Procesado: {item['is_processed']}")

        # 6. Simular procesamiento
        print(f"\n🎭 SIMULANDO PROCESAMIENTO (DRY RUN):")
        print("-" * 50)

        total_posts_simulated = 0
        for i, item in enumerate(groups_queue):
            # Simular posts obtenidos (ficticio)
            if is_first_run:
                posts_simulated = min(10, random.randint(5, 15))  # Máximo 10 en first run
            else:
                posts_simulated = random.randint(3, 20)  # Sin límite en runs posteriores

            total_posts_simulated += posts_simulated

            print(f"   🎯 Procesando grupo {i+1}/{len(groups_queue)}: {item['nombre']}")
            print(f"      Posts obtenidos (ficticio): {posts_simulated}")
            print(f"      Estado: {'✅ Completado' if True else '❌ Error'}")

            # Marcar como procesado
            item['is_processed'] = True

        # 7. Simular cambio de is_first_run
        new_is_first_run = False
        print(f"\n🔄 SIMULANDO ACTUALIZACIÓN DE CONFIGURACIÓN:")
        print(f"   is_first_run: {is_first_run} → {new_is_first_run}")

        # 8. Mostrar resumen
        print(f"\n📊 RESUMEN DEL DRY RUN:")
        print("=" * 40)
        print(f"✅ Grupos procesados: {len(groups_queue)}")
        print(f"📄 Posts totales (ficticio): {total_posts_simulated}")
        print(f"🔄 is_first_run cambiaría a: {new_is_first_run}")
        print(f"⏱️ Modo: {'First Run (límite 10 posts)' if is_first_run else 'Run normal'}")

        return {
            'success': True,
            'groups_processed': len(groups_queue),
            'total_posts_simulated': total_posts_simulated,
            'is_first_run_before': is_first_run,
            'is_first_run_after': new_is_first_run,
            'groups_queue': groups_queue
        }

    def run_real_orchestration(self, page=None) -> Dict[str, Any]:
        """
        Ejecuta el flujo real del orquestador con extracción de posts

        Args:
            page: Página de Playwright para navegación (opcional)

        Returns:
            Dict con resultados del procesamiento real
        """
        print("\n🚀 INICIANDO ORQUESTACIÓN REAL")
        print("=" * 60)

        # 1. Obtener configuración is_first_run
        setup_config = self.db_manager.get_setup_config()
        is_first_run = setup_config.get('is_first_run', 1) == 1

        print(f"🔍 Configuración actual:")
        print(f"   is_first_run: {is_first_run}")
        print(f"   Límite de posts: {'10 (first run)' if is_first_run else 'Sin límite'}")

        # 2. Obtener grupos activos
        active_groups = self.db_manager.get_groups_in_flow(self.user_id)
        print(f"\n📋 Grupos activos encontrados: {len(active_groups)}")

        if not active_groups:
            return {
                'success': False,
                'message': 'No hay grupos activos (in_flow = True)',
                'is_first_run': is_first_run
            }

        # 3. Aplicar shuffle
        shuffled_groups = active_groups.copy()
        random.shuffle(shuffled_groups)

        print(f"🔀 Grupos randomizados: {len(shuffled_groups)}")

        # 4. Crear cola temporal en memoria
        self.groups_queue = []
        for group in shuffled_groups:
            queue_item = {
                'grupo': group['url'],
                'is_processed': False,
                'id': group['id'],
                'nombre': group['nombre']
            }
            self.groups_queue.append(queue_item)

        print(f"🗂️ Cola temporal creada con {len(self.groups_queue)} grupos")

        # 5. Procesar cada grupo
        total_posts_extracted = 0
        processing_results = []

        for i, queue_item in enumerate(self.groups_queue):
            print(f"\n--- Procesando grupo {i+1}/{len(self.groups_queue)} ---")
            print(f"🎯 Grupo: {queue_item['nombre']}")
            print(f"🔗 URL: {queue_item['grupo']}")

            try:
                # Procesar grupo individual
                result = self._process_single_group_real(queue_item, page, is_first_run)
                processing_results.append(result)

                if result.get('success'):
                    posts_count = result.get('posts_extracted', 0)
                    total_posts_extracted += posts_count
                    queue_item['is_processed'] = True
                    print(f"✅ Grupo procesado: {posts_count} posts extraídos")
                else:
                    print(f"❌ Error procesando grupo: {result.get('message', 'Error desconocido')}")

            except Exception as e:
                print(f"❌ Error inesperado procesando grupo: {e}")
                processing_results.append({
                    'success': False,
                    'message': f'Error inesperado: {e}',
                    'group_id': queue_item['id']
                })

        # 6. Actualizar is_first_run a False al finalizar
        if is_first_run:
            print(f"\n🔄 Actualizando is_first_run a False...")
            update_success = self.db_manager.update_is_first_run(False)
            if update_success:
                print("✅ is_first_run actualizado correctamente")
            else:
                print("❌ Error actualizando is_first_run")

        # 7. Mostrar resumen final
        processed_count = sum(1 for item in self.groups_queue if item['is_processed'])

        print(f"\n📊 RESUMEN FINAL DE ORQUESTACIÓN:")
        print("=" * 50)
        print(f"✅ Grupos procesados: {processed_count}/{len(self.groups_queue)}")
        print(f"📄 Posts totales extraídos: {total_posts_extracted}")
        print(f"🔄 is_first_run actualizado: {is_first_run} → False")
        print(f"⏱️ Modo ejecutado: {'First Run (límite 10 posts)' if is_first_run else 'Run normal'}")

        return {
            'success': True,
            'groups_processed': processed_count,
            'total_groups': len(self.groups_queue),
            'total_posts_extracted': total_posts_extracted,
            'is_first_run_before': is_first_run,
            'is_first_run_after': False,
            'processing_results': processing_results,
            'groups_queue': self.groups_queue
        }

    def _process_single_group_real(self, queue_item: Dict[str, Any], page=None, is_first_run: bool = False) -> Dict[str, Any]:
        """
        Procesa un grupo individual extrayendo posts reales

        Args:
            queue_item: Item de la cola con información del grupo
            page: Página de Playwright para navegación
            is_first_run: Si es primera ejecución (límite 10 posts)

        Returns:
            Dict con resultados del procesamiento
        """
        group_id = queue_item['id']
        group_name = queue_item['nombre']
        group_url = queue_item['grupo']

        print(f"🎯 Procesando grupo: {group_name} (ID: {group_id})")

        if not page:
            return {
                'success': False,
                'message': 'No hay página disponible para navegación',
                'group_id': group_id
            }

        try:
            # 1. Navegar al grupo
            print(f"🌐 Navegando a: {group_url}")
            page.goto(group_url)
            page.wait_for_selector('[role="feed"]', timeout=15000)

            # 2. Configurar extractor según is_first_run
            max_posts = 10 if is_first_run else 50  # Límite según configuración
            extractor = FacebookPostExtractor(is_test=True, test_max_posts=max_posts)

            print(f"📄 Límite de posts: {max_posts} ({'first run' if is_first_run else 'run normal'})")

            # 3. Obtener limit_post si existe
            limit_post = self.post_repository.retrieve_limit_post(group_id)
            if limit_post:
                print(f"🎯 Limit post encontrado para detener extracción")

            # 4. Extraer posts usando el método correcto para flujo real
            extraction_result = extractor.run_test_extraction(
                page=page,
                wait_seconds=60,  # Timeout de 60 segundos
                poll_interval=3.0,
                group_info={
                    'url': group_url,
                    'name': group_name,
                    'id': group_id
                },
                limit_post=limit_post
            )

            # 5. Guardar posts en BD
            posts = extraction_result.get('posts', [])
            if posts:
                save_result = self.post_repository.save_extracted_posts(
                    extracted_posts=posts,
                    group_id=group_id,
                    user_id=self.user_id,
                    is_test=False,  # No limitar posts en BD, ya están limitados por extractor
                    is_first_run=is_first_run
                )

                posts_saved = save_result.get('saved_count', 0)
                print(f"💾 Posts guardados en BD: {posts_saved}")

                return {
                    'success': True,
                    'posts_extracted': len(posts),
                    'posts_saved': posts_saved,
                    'group_id': group_id,
                    'group_name': group_name
                }
            else:
                return {
                    'success': True,
                    'posts_extracted': 0,
                    'posts_saved': 0,
                    'group_id': group_id,
                    'group_name': group_name,
                    'message': 'No se extrajeron posts'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'Error procesando grupo: {e}',
                'group_id': group_id,
                'group_name': group_name
            }


class FacebookFlowOrchestrator:
    """Orquestador principal que coordina todos los módulos especializados"""

    def __init__(self, config_path: Optional[str] = None, headless: bool = False):
        # Usar ruta absoluta si no se especifica
        if config_path is None:
            config_path = get_config_str()
        self.config_path = config_path
        self.headless = headless

        # Inicializar módulos especializados
        self.login_manager = FacebookLoginManager(config_path, headless=headless)
        self.groups_tracker = GroupsTracker()
        self.groups_repository = DatabaseManager()

        # Variables de estado
        self.page = None
        self.context = None
        self.browser = None

        print(f"🚀 Facebook Flow Orchestrator iniciado")
        print(f"   📁 Config: {config_path}")
        print(f"   👁️ Headless: {'✅ Sí' if headless else '❌ No'}")

    def execute_login_flow(self) -> Dict[str, Any]:
        """
        Ejecuta el flujo completo de login

        Returns:
            Dict con resultados del login
        """
        print("🔐 Iniciando Facebook Login Solid...")

        # Ejecutar login usando el manager especializado
        login_result = self.login_manager.login()

        # Guardar referencias para uso posterior
        if login_result.get('success'):
            self.page = login_result.get('page')
            self.context = login_result.get('context')
            self.browser = login_result.get('browser')

        return login_result

    def execute_humanization(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta la humanización inteligente

        Args:
            skip: Si True, salta la humanización y devuelve resultado exitoso

        Returns:
            Dict con resultados de la humanización
        """
        if skip:
            print("\n🚀 SALTANDO HUMANIZACIÓN (skip=True)")
            print("=" * 60)
            print("⏭️ Humanización omitida por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Humanización saltada por parámetro skip=True'}

        if not self.page:
            return {'success': False, 'message': 'No hay página disponible para humanización'}

        print("\n🤖 INICIANDO HUMANIZACIÓN INTELIGENTE")
        print("=" * 60)

        try:
            # Inicializar detectores y humanizador
            page_detectors = PageDetectors()
            humanizer = IntelligentHumanizer(
                page_detectors=page_detectors,
                config_path=self.config_path
            )

            # Ejecutar humanización normal usando el método correcto
            humanization_result = humanizer.start_home_session(self.page)
            return humanization_result

        except Exception as e:
            return {'success': False, 'message': f'Error en humanización: {e}'}

    def execute_groups_tracking(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta el tracking de grupos e integra al repositorio

        Args:
            skip: Si True, salta el tracking y devuelve resultado exitoso

        Returns:
            Dict con resultados del tracking e integración
        """
        if skip:
            print("\n🚀 SALTANDO TRACKING DE GRUPOS (skip=True)")
            print("=" * 60)
            print("⏭️ Tracking de grupos omitido por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Tracking de grupos saltado por parámetro skip=True'}

        if not self.page:
            return {'success': False, 'message': 'No hay página disponible para tracking'}

        print("\n🔍 INICIANDO TEST DE OBSERVACIÓN DE GRUPOS")
        print("=" * 60)

        try:
            # Navegar a página de grupos
            groups_url = "https://www.facebook.com/groups/joins/?nav_source=tab"
            print(f"🌐 Navegando a URL objetivo: {groups_url}")
            self.page.goto(groups_url)

            # Ejecutar tracking usando la clase especializada
            results = self.groups_tracker.track_groups_page(self.page)

            # Mostrar resumen detallado
            self.groups_tracker.print_summary(results)

            # Mostrar lista completa de links si fue exitoso
            extraction = results.get('extraction', {})
            groups_links = extraction.get('groups_links', [])

            if groups_links:
                print(f"\n📋 LISTA COMPLETA DE LINKS RECOLECTADOS ({len(groups_links)}):")
                print("=" * 80)
                for i, link in enumerate(groups_links):
                    print(f"   [{i+1:2d}] {link['href']}")
                print("=" * 80)

                # INTEGRACIÓN AL REPOSITORIO
                integration_result = self._integrate_groups_to_repository(groups_links)
                results['integration'] = integration_result

            return results

        except Exception as e:
            return {'success': False, 'message': f'Error en tracking de grupos: {e}'}

    def _integrate_groups_to_repository(self, groups_links: list) -> Dict[str, Any]:
        """
        Integra grupos encontrados al repositorio

        Args:
            groups_links: Lista de links de grupos del tracker

        Returns:
            Dict con resultados de la integración
        """
        import time

        print("\n💾 INTEGRANDO GRUPOS AL REPOSITORIO")
        print("=" * 60)

        try:
            # Convertir datos del tracker al formato del repositorio usando slug como nombre
            groups_data = []
            for i, group_link in enumerate(groups_links):
                url = group_link.get('href', '')
                original_text = group_link.get('text', f'Grupo {i+1}')

                if not url:
                    continue

                # Extraer slug de la URL para usar como nombre consistente
                slug = extract_group_slug_from_url(url)
                if slug:
                    # Generar nombre basado en slug (más consistente)
                    nombre = generate_group_name_from_slug(slug)
                    print(f"   📋 Grupo {i+1}: {slug} → {nombre}")
                else:
                    # Fallback al texto original si no se puede extraer slug
                    nombre = original_text
                    print(f"   ⚠️ Grupo {i+1}: No se pudo extraer slug, usando texto: {nombre}")

                # Obtener información adicional de la URL
                url_info = extract_group_info_from_url(url)

                group_data = {
                    'nombre': nombre,
                    'url': url,
                    'in_flow': True,  # Por defecto in_flow=True como solicitado
                    'metadata': {
                        'source': 'groups_tracker',
                        'discovered_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'tracker_index': i,
                        'original_data': group_link,
                        'original_text': original_text,
                        'slug': slug,
                        'url_info': url_info
                    }
                }
                groups_data.append(group_data)

            # Añadir grupos al repositorio
            if groups_data:
                added, duplicates = self.groups_repository.bulk_add_groups(groups_data)

                # Mostrar estadísticas finales
                self.groups_repository.print_stats()

                result = {
                    'success': True,
                    'added': added,
                    'duplicates': duplicates,
                    'total_processed': len(groups_data)
                }

                print(f"\n🎉 INTEGRACIÓN COMPLETADA")
                print(f"✅ Grupos añadidos: {added}")
                print(f"🔄 Duplicados omitidos: {duplicates}")
                print(f"📊 Total procesados: {len(groups_data)}")

                return result
            else:
                return {'success': False, 'message': 'No hay grupos válidos para integrar'}

        except Exception as e:
            print(f"❌ Error en integración: {e}")
            return {'success': False, 'message': f'Error en integración: {e}'}

    def execute_queue_processing(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta el procesamiento de cola de grupos

        Args:
            skip: Si True, salta el procesamiento y devuelve resultado exitoso

        Returns:
            Dict con resultados del procesamiento
        """
        if skip:
            print("\n🚀 SALTANDO PROCESAMIENTO DE COLA (skip=True)")
            print("=" * 60)
            print("⏭️ Procesamiento de cola omitido por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Procesamiento de cola saltado por parámetro skip=True'}

        try:
            print("\n🚀 INICIANDO PROCESAMIENTO DE COLA DE GRUPOS")
            print("=" * 60)

            # Crear instancia del procesador de cola CON PÁGINA REAL
            queue_processor = QueueProcessor(page=self.page)

            # Ejecutar procesamiento
            queue_processor.process_queue()

            # Obtener estadísticas finales
            result = queue_processor.get_processing_stats()

            print("\n✅ PROCESAMIENTO DE COLA COMPLETADO")
            print("=" * 60)

            return result

        except Exception as e:
            print(f"❌ Error en procesamiento de cola: {e}")
            return {'success': False, 'message': f'Error en procesamiento de cola: {e}'}

    def test_post_group_extraction(self, group_url: str) -> Dict[str, Any]:
        """
        Test de extracción de posts de un grupo específico usando FacebookPostExtractor
        con humanización integrada

        Args:
            group_url: URL del grupo a extraer

        Returns:
            Dict con resultados de la extracción
        """
        print(f"\n🎯 TEST: EXTRACCIÓN DE POSTS DE GRUPO")
        print("=" * 60)
        print(f"🌐 URL objetivo: {group_url}")
        
        if not self.page:
            return {'success': False, 'message': 'No hay página disponible para la extracción'}
            
        try:
            # 1. Navegar al grupo específico
            print(f"\n🚀 Navegando al grupo...")
            self.page.goto(group_url)
            print(f"   ✅ Navegación completada")
            print(f"   📍 URL actual: {self.page.url}")
            
            # 2. Extraer información del grupo desde la URL
            group_info = extract_group_info_from_url(group_url)
            slug = extract_group_slug_from_url(group_url)
            group_name = generate_group_name_from_slug(slug) if slug else "Grupo desconocido"
            
            print(f"\n📋 Información del grupo:")
            print(f"   📝 Nombre: {group_name}")
            print(f"   🏷️ Slug: {slug}")
            print(f"   🔗 Info URL: {group_info}")
            
            # 3. Esperar a que la página se cargue completamente
            print(f"\n⏳ Esperando carga de la página...")
            self.page.wait_for_selector('[role="feed"]', timeout=15000)
            print(f"   ✅ Feed detectado")
            
            # 4. Aplicar humanización básica usando la clase disponible
            if hasattr(self, 'page_detectors') or True:  # Siempre ejecutar para el test
                try:
                    from utils.humanization import FacebookHumanizer
                    humanizer = FacebookHumanizer()
                    
                    print(f"\n🤖 Aplicando humanización previa...")
                    # Scroll suave inicial
                    self.page.evaluate("window.scrollTo(0, window.innerHeight * 0.5)")
                    self.page.wait_for_timeout(2000)
                    print(f"   ✅ Humanización aplicada")
                except Exception as e:
                    print(f"   ⚠️ Error en humanización: {e}")
            
            # 5. Obtener limit post de BD antes de extraer
            print(f"\n🔍 VERIFICANDO LIMIT POST EN BASE DE DATOS")
            print("-" * 50)

            # Normalizar URL para búsqueda (remover parámetros)
            from urllib.parse import urlparse
            parsed_url = urlparse(group_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
            if not base_url.endswith('/'):
                base_url += '/'

            print(f"   🔗 URL original: {group_url}")
            print(f"   🔗 URL base para búsqueda: {base_url}")

            # Obtener o crear grupo para tener group_id
            existing_group = self.groups_repository.get_group_by_url(base_url)
            if existing_group:
                group_id_for_limit = existing_group['id']
                print(f"   📋 Grupo existente: {existing_group['nombre']} (ID: {group_id_for_limit})")
            else:
                print(f"   ℹ️ Grupo no existe en BD, no hay limit post")
                group_id_for_limit = None

            # Obtener limit post si existe el grupo
            limit_post = None
            if group_id_for_limit:
                post_repo = PostRepository(self.groups_repository.db_path)
                limit_post = post_repo.retrieve_limit_post(group_id_for_limit)

                if limit_post:
                    print(f"   🎯 Limit post encontrado:")
                    print(f"      Autor: {limit_post.get('author', 'Sin autor')[:30]}")
                    print(f"      Contenido: {limit_post.get('content', 'Sin contenido')[:60]}...")
                    print(f"      Grupo ID: {limit_post.get('group_id')}")
                else:
                    print(f"   ℹ️ No hay limit post para este grupo")

            # 6. Crear extractor en modo test e iniciar extracción
            print(f"\n🔍 INICIANDO EXTRACCIÓN DE POSTS (MODO TEST)")
            print("-" * 50)

            extractor = FacebookPostExtractor(is_test=True, test_max_posts=15)
            
            # Parámetros para el test: extracción de hasta 15 posts con timeout de 90 segundos
            extraction_result = extractor.run_test_extraction(
                page=self.page,
                wait_seconds=90,  # Timeout de 90 segundos
                poll_interval=3.0,  # Verificar cada 3 segundos
                group_info={
                    'url': group_url,
                    'name': group_name,
                    'slug': slug,
                    'extracted_info': group_info
                },
                limit_post=limit_post  # Pasar limit_post para detener extracción
            )
            
            # 6. Procesar y mostrar resultados
            print(f"\n📊 RESULTADOS DE LA EXTRACCIÓN")
            print("=" * 60)
            
            if extraction_result.get('success', True):  # Si no hay 'success', asumimos que funcionó
                posts = extraction_result.get('posts', [])
                estadisticas = extraction_result.get('estadisticas', {})
                
                print(f"✅ Extracción completada exitosamente")
                print(f"📄 Posts extraídos: {len(posts)}")
                print(f"⏱️ Tiempo total: {estadisticas.get('tiempoTotal', 'N/A')}s")
                print(f"🔄 Scrolls realizados: {estadisticas.get('scrollsRealizados', 'N/A')}")
                print(f"👆 Clicks 'Ver más': {estadisticas.get('clicksVerMas', 'N/A')}")
                
                # Mostrar archivo JSON generado
                json_path = extraction_result.get('test_json_path')
                if json_path:
                    print(f"💾 Archivo JSON generado: {json_path}")

                # 7. GUARDAR POSTS EN BASE DE DATOS
                print(f"\n💾 GUARDANDO POSTS EN BASE DE DATOS")
                print("-" * 50)

                if posts:
                    try:
                        # Inicializar PostRepository
                        post_repo = PostRepository(self.groups_repository.db_path)

                        # Obtener o crear grupo en BD
                        existing_group = self.groups_repository.get_group_by_url(group_url)
                        if existing_group:
                            group_id = existing_group['id']
                            print(f"   📋 Grupo existente encontrado: {existing_group['nombre']}")
                        else:
                            group_id = self.groups_repository.add_group(
                                user_id=1,
                                nombre=group_name,
                                url=group_url,
                                in_flow=True,  # Marcar como activo para flujo
                                metadata={'slug': slug, 'extracted_info': group_info}
                            )
                            print(f"   ✅ Nuevo grupo creado: {group_name}")

                        print(f"   📋 Grupo ID en BD: {group_id}")
                        print(f"   📄 Posts a procesar: {len(posts)}")

                        # LOGGING DETALLADO: Confirmar parámetros antes de guardar
                        print(f"   🔍 LOGGING: Parámetros para save_extracted_posts:")
                        print(f"      - Posts a procesar: {len(posts)}")
                        print(f"      - Group ID: {group_id}")
                        print(f"      - User ID: 1")
                        print(f"      - is_test: False")
                        print(f"      - is_first_run: False (debería verificar duplicados BD)")

                        # Guardar posts usando PostRepository con thresholds dinámicos
                        save_result = post_repo.save_extracted_posts(
                            extracted_posts=posts,
                            group_id=group_id,
                            user_id=1,  # Usuario por defecto para tests
                            is_test=False,  # No limitar posts en flujo real
                            is_first_run=False  # Verificar duplicados contra BD
                        )

                        print(f"   ✅ Posts guardados: {save_result.get('saved_count', 0)}")
                        print(f"   ⏭️ Posts omitidos: {save_result.get('skipped_count', 0)}")
                        print(f"   🔍 Duplicados detectados: {save_result.get('duplicate_count', 0)}")
                        print(f"   ❌ Posts inválidos: {save_result.get('invalid_count', 0)}")

                        # Agregar info de BD al resultado
                        db_info = {
                            'group_id': group_id,
                            'saved_count': save_result.get('saved_count', 0),
                            'skipped_count': save_result.get('skipped_count', 0),
                            'duplicate_count': save_result.get('duplicate_count', 0),
                            'invalid_count': save_result.get('invalid_count', 0)
                        }

                    except Exception as e:
                        print(f"   ❌ Error guardando en BD: {e}")
                        db_info = {'error': str(e)}
                else:
                    print(f"   ⚠️ No hay posts para guardar")
                    db_info = {'message': 'no_posts_to_save'}

                # Mostrar muestra de posts extraídos
                if posts:
                    print(f"\n📋 MUESTRA DE POSTS EXTRAÍDOS:")
                    print("-" * 40)
                    for i, post in enumerate(posts[:3]):  # Mostrar solo los primeros 3
                        autor = post.get('nombreAutor', 'Sin autor')[:30]
                        contenido = post.get('contenidoPost', 'Sin contenido')[:80]
                        print(f"   [{i+1}] {autor}: {contenido}...")

                    if len(posts) > 3:
                        print(f"   ... y {len(posts) - 3} posts más")
                
                return {
                    'success': True,
                    'posts_count': len(posts),
                    'group_info': {
                        'url': group_url,
                        'name': group_name,
                        'slug': slug
                    },
                    'extraction_stats': estadisticas,
                    'json_file': json_path,
                    'database_info': db_info if 'db_info' in locals() else {},
                    'posts_sample': posts[:5] if posts else []  # Muestra de los primeros 5 posts
                }
            else:
                error_msg = extraction_result.get('error', 'Error desconocido en la extracción')
                print(f"❌ Error en la extracción: {error_msg}")
                return {
                    'success': False,
                    'message': f'Error en extracción: {error_msg}',
                    'group_info': {
                        'url': group_url,
                        'name': group_name,
                        'slug': slug
                    }
                }
                
        except Exception as e:
            print(f"❌ Error durante el test de extracción: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'message': f'Error durante el test: {e}',
                'group_info': {'url': group_url}
            }

    def early_exit_for_debug_purposes(self, exit_point: str, results: Optional[Dict[str, Any]] = None) -> bool:
        """
        Salida temprana para propósitos de debug

        Args:
            exit_point: Punto de salida ("after_groups_tracking", "after_login", etc.)
            results: Resultados a mostrar antes de salir

        Returns:
            bool: True si debe salir, False si debe continuar
        """
        print(f"\n🐛 DEBUG EXIT POINT: {exit_point}")
        print("=" * 60)

        if results:
            print("📊 RESULTADOS OBTENIDOS:")
            for key, value in results.items():
                if isinstance(value, dict):
                    print(f"   📋 {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"      {sub_key}: {sub_value}")
                else:
                    print(f"   {key}: {value}")

        print(f"\n⏸️ NAVEGADOR ABIERTO PARA INSPECCIÓN DE DEBUG")
        print("🔍 Puedes inspeccionar el estado actual del navegador")
        print("📋 Presiona Enter para continuar o Ctrl+C para salir...")

        try:
            input()
            return False  # Continuar si presiona Enter
        except KeyboardInterrupt:
            print("\n🛑 Salida forzada por Ctrl+C")
            return True  # Salir si presiona Ctrl+C


    def cleanup(self):
        """Limpia recursos del navegador"""
        try:
            if self.browser:
                self.browser.__exit__(None, None, None)
                print("✅ Cerrando navegador...")
        except Exception as e:
            print(f"❌ Error cerrando navegador: {e}")


def main():
    """Función principal del orquestador con soporte para argumentos. Incluye nuevo orquestador de grupos."""
    parser = argparse.ArgumentParser(description='Facebook Flow Orchestrator - Orquestador de Grupos')
    parser.add_argument('--headless', action='store_true',
                       help='Ejecutar en modo headless (sin interfaz gráfica)')
    parser.add_argument('--config', default=None,
                       help='Ruta al archivo de configuración (usa ruta absoluta por defecto)')
    parser.add_argument('--skip-humanization', action='store_true',
                       help='Saltar humanización e ir directo a tareas')
    parser.add_argument('--dry-run', action='store_true',
                       help='Ejecutar solo dry run sin procesar posts reales')
    parser.add_argument('--clear-posts', action='store_true',
                       help='Borrar todos los posts antes de ejecutar')

    args = parser.parse_args()

    print("🎯 FACEBOOK FLOW ORCHESTRATOR - ORQUESTADOR DE GRUPOS")
    print("=" * 60)
    print(f"🎛️ Modo headless: {'✅ Activado' if args.headless else '🖥️ Modo Normal'}")
    print(f"📁 Config: {args.config}")
    print(f"🤖 Skip humanización: {'✅ Sí' if args.skip_humanization else '❌ No'}")
    print(f"🏃‍♂️ Dry run: {'✅ Sí' if args.dry_run else '❌ No'}")
    print(f"🧹 Clear posts: {'✅ Sí' if args.clear_posts else '❌ No'}")
    print("=" * 60)

    # Crear orquestador de grupos
    groups_orchestrator = GroupsOrchestrator(user_id=1)

    # Borrar posts si se solicita
    if args.clear_posts:
        groups_orchestrator.clear_all_posts()

    # Si es dry run, ejecutar solo simulación
    if args.dry_run:
        print("\n🏃‍♂️ EJECUTANDO DRY RUN")
        dry_run_result = groups_orchestrator.run_dry_run()

        if dry_run_result.get('success'):
            print("\n✅ DRY RUN COMPLETADO EXITOSAMENTE")
        else:
            print(f"\n❌ DRY RUN FALLÓ: {dry_run_result.get('message')}")

        return

    # Para flujo real, necesitamos login
    print("\n🔐 INICIANDO FLUJO REAL CON LOGIN")

    # Crear orquestador de Facebook para login
    orchestrator = FacebookFlowOrchestrator(config_path=args.config, headless=args.headless)

    try:
        # 1. Ejecutar login
        login_result = orchestrator.execute_login_flow()

        # Mostrar resultados del login
        print(f"\n📊 RESULTADO DEL LOGIN:")
        print("=" * 50)
        print(f"✅ Éxito: {login_result.get('success', False)}")
        print(f"🎯 Estado: {login_result.get('state', 'N/A')}")
        print(f"💬 Mensaje: {login_result.get('message', 'N/A')}")
        print(f"⏱️ Tiempo: {login_result.get('duration', 0):.2f}s")
        print(f"📸 Screenshots: {len(login_result.get('screenshots', []))}")

        # Mostrar verificación de home si existe
        home_verification = login_result.get('home_verification', {})
        if home_verification:
            print(f"\n🏠 Indicadores de Home:")
            for key, value in home_verification.items():
                if isinstance(value, bool):
                    icon = "✅" if value else "❌"
                    print(f"   {icon} {key}")
                elif key not in ['detection_time']:
                    print(f"   📊 {key}: {value}")

        # 2. Si login exitoso, ejecutar orquestador de grupos
        if login_result.get('success'):
            print(f"\n🎯 LOGIN EXITOSO - INICIANDO ORQUESTADOR DE GRUPOS")

            # Ejecutar orquestador real con la página del navegador
            orchestration_result = groups_orchestrator.run_real_orchestration(page=orchestrator.page)

            if orchestration_result.get('success'):
                print(f"\n🎉 ORQUESTACIÓN COMPLETADA EXITOSAMENTE")
                print(f"✅ Grupos procesados: {orchestration_result.get('groups_processed')}")
                print(f"📄 Posts extraídos: {orchestration_result.get('total_posts_extracted')}")
            else:
                print(f"\n❌ ORQUESTACIÓN FALLÓ: {orchestration_result.get('message')}")

            # Esperar cierre manual si hay página disponible (solo en modo no-headless)
            if orchestrator.page and not args.headless:
                print("\n⏸️ NAVEGADOR ABIERTO PARA INSPECCIÓN")
                print("=" * 40)
                print("🔍 Puedes inspeccionar el estado actual del navegador")
                input("📋 Presiona Enter cuando quieras cerrar...")
            elif args.headless:
                print("\n🤖 Modo headless: cerrando automáticamente...")
        else:
            print("\n⚠️ Login no exitoso, no se puede ejecutar orquestador")
            print("💡 Sugerencia: Verificar credenciales y conexión")

    except Exception as e:
        print(f"❌ Error en orquestación: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Limpiar recursos
        orchestrator.cleanup()


if __name__ == "__main__":
    main()
