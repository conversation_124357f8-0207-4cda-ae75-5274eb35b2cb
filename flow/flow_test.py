#!/usr/bin/env python3
# Testing purposes currently being evalueted
"""
Facebook Flow - Orquestador Principal
<PERSON><PERSON><PERSON> todos los módulos especializados para automatización de Facebook
Soporta modo headless y configuración flexible

IMPORTANTE (Política interna de pruebas):
- La sección de LOGIN + CARGA DE SESIÓN + CREACIÓN DE CONTEXTO/NAVEGADOR es infraestructura estable que reutilizamos para mantener huella / fingerprint, cookies, storage_state y orden de inicialización CONSISTENTES.
- Motivo técnico y de riesgo: si alteramos fingerprint (UA, viewport, orden de inicialización), storage_state, secuencia de reapertura o el patrón base de creación de browser/context/page, Facebook puede interpretar “saltos” de entorno (como si iniciáramos sesión desde otros dispositivos / ubicaciones) y generar fricción: reautenticaciones, checkpoints o incluso soft bans.
- Por eso NO cambiamos esos datos base durante pruebas normales; solo se tocan para mantenimiento o corrección puntual cuando algo falla a nivel login.
- Los experimentos, refactors y tests se enfocan EXCLUSIVAMENTE DESPUÉS del login exitoso (extracción de posts: observer / queue / comparación).
- Si se necesita instrumentar (nuevas métricas, asserts, hooks), hacerlo como capas externas post-login sin alterar: cookies cargadas, user agent, viewport base, orden de creación de browser/context/page ni timeout base.
- Objetivo: estabilidad + reproducibilidad → misma sesión controlada = datos comparables y menor riesgo de detección.

Resumen rápido:
(1) Login estable (fingerprint + session bootstrap) → NO MODIFICAR en pruebas (solo mantener / corregir si falla).
(2) Zona de pruebas: extracción (QueueProcessor / tracking / comparación) → aquí iteramos rápido.
"""

import argparse
import time
import json
from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List, Callable, Tuple
from login_system import FacebookLoginManager
from utils.page_detectors import PageDetectors
from utils.intelligent_humanizer import IntelligentHumanizer
from utils.path_utils import get_config_str
from groups_tracker import GroupsTracker
from database.database_manager import DatabaseManager
from queue_processor import QueueProcessor
from group_utils import extract_group_slug_from_url, generate_group_name_from_slug, extract_group_info_from_url
from facebook_post_extractor import FacebookPostExtractor
from post_repository import PostRepository

class FacebookFlowOrchestrator:
    """Orquestador principal que coordina todos los módulos especializados"""

    def __init__(self, config_path: Optional[str] = None, headless: bool = False):
        # Usar ruta absoluta si no se especifica
        if config_path is None:
            config_path = get_config_str()
        self.config_path = config_path
        self.headless = headless

        # Inicializar módulos especializados
        self.login_manager = FacebookLoginManager(config_path, headless=headless)
        self.groups_tracker = GroupsTracker()
        self.groups_repository = DatabaseManager()

        # Variables de estado
        self.page = None
        self.context = None
        self.browser = None

        print(f"🚀 Facebook Flow Orchestrator iniciado")
        print(f"   📁 Config: {config_path}")
        print(f"   👁️ Headless: {'✅ Sí' if headless else '❌ No'}")

    def execute_login_flow(self) -> Dict[str, Any]:
        """
        Ejecuta el flujo completo de login

        Returns:
            Dict con resultados del login
        """
        print("🔐 Iniciando Facebook Login Solid...")

        # Ejecutar login usando el manager especializado
        login_result = self.login_manager.login()

        # Guardar referencias para uso posterior
        if login_result.get('success'):
            self.page = login_result.get('page')
            self.context = login_result.get('context')
            self.browser = login_result.get('browser')

        return login_result

    def execute_humanization(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta la humanización inteligente

        Args:
            skip: Si True, salta la humanización y devuelve resultado exitoso

        Returns:
            Dict con resultados de la humanización
        """
        if skip:
            print("\n🚀 SALTANDO HUMANIZACIÓN (skip=True)")
            print("=" * 60)
            print("⏭️ Humanización omitida por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Humanización saltada por parámetro skip=True'}

        if not self.page:
            return {'success': False, 'message': 'No hay página disponible para humanización'}

        print("\n🤖 INICIANDO HUMANIZACIÓN INTELIGENTE")
        print("=" * 60)

        try:
            # Inicializar detectores y humanizador
            page_detectors = PageDetectors()
            humanizer = IntelligentHumanizer(
                page_detectors=page_detectors,
                config_path=self.config_path
            )

            # Ejecutar humanización normal usando el método correcto
            humanization_result = humanizer.start_home_session(self.page)
            return humanization_result

        except Exception as e:
            return {'success': False, 'message': f'Error en humanización: {e}'}

    def execute_groups_tracking(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta el tracking de grupos e integra al repositorio

        Args:
            skip: Si True, salta el tracking y devuelve resultado exitoso

        Returns:
            Dict con resultados del tracking e integración
        """
        if skip:
            print("\n🚀 SALTANDO TRACKING DE GRUPOS (skip=True)")
            print("=" * 60)
            print("⏭️ Tracking de grupos omitido por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Tracking de grupos saltado por parámetro skip=True'}

        if not self.page:
            return {'success': False, 'message': 'No hay página disponible para tracking'}

        print("\n🔍 INICIANDO TEST DE OBSERVACIÓN DE GRUPOS")
        print("=" * 60)

        try:
            # Navegar a página de grupos
            groups_url = "https://www.facebook.com/groups/joins/?nav_source=tab"
            print(f"🌐 Navegando a URL objetivo: {groups_url}")
            self.page.goto(groups_url)

            # Ejecutar tracking usando la clase especializada
            results = self.groups_tracker.track_groups_page(self.page)

            # Mostrar resumen detallado
            self.groups_tracker.print_summary(results)

            # Mostrar lista completa de links si fue exitoso
            extraction = results.get('extraction', {})
            groups_links = extraction.get('groups_links', [])

            if groups_links:
                print(f"\n📋 LISTA COMPLETA DE LINKS RECOLECTADOS ({len(groups_links)}):")
                print("=" * 80)
                for i, link in enumerate(groups_links):
                    print(f"   [{i+1:2d}] {link['href']}")
                print("=" * 80)

                # INTEGRACIÓN AL REPOSITORIO
                integration_result = self._integrate_groups_to_repository(groups_links)
                results['integration'] = integration_result

            return results

        except Exception as e:
            return {'success': False, 'message': f'Error en tracking de grupos: {e}'}

    def _integrate_groups_to_repository(self, groups_links: list) -> Dict[str, Any]:
        """
        Integra grupos encontrados al repositorio

        Args:
            groups_links: Lista de links de grupos del tracker

        Returns:
            Dict con resultados de la integración
        """
        import time

        print("\n💾 INTEGRANDO GRUPOS AL REPOSITORIO")
        print("=" * 60)

        try:
            # Convertir datos del tracker al formato del repositorio usando slug como nombre
            groups_data = []
            for i, group_link in enumerate(groups_links):
                url = group_link.get('href', '')
                original_text = group_link.get('text', f'Grupo {i+1}')

                if not url:
                    continue

                # Extraer slug de la URL para usar como nombre consistente
                slug = extract_group_slug_from_url(url)
                if slug:
                    # Generar nombre basado en slug (más consistente)
                    nombre = generate_group_name_from_slug(slug)
                    print(f"   📋 Grupo {i+1}: {slug} → {nombre}")
                else:
                    # Fallback al texto original si no se puede extraer slug
                    nombre = original_text
                    print(f"   ⚠️ Grupo {i+1}: No se pudo extraer slug, usando texto: {nombre}")

                # Obtener información adicional de la URL
                url_info = extract_group_info_from_url(url)

                group_data = {
                    'nombre': nombre,
                    'url': url,
                    'in_flow': True,  # Por defecto in_flow=True como solicitado
                    'metadata': {
                        'source': 'groups_tracker',
                        'discovered_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'tracker_index': i,
                        'original_data': group_link,
                        'original_text': original_text,
                        'slug': slug,
                        'url_info': url_info
                    }
                }
                groups_data.append(group_data)

            # Añadir grupos al repositorio
            if groups_data:
                added, duplicates = self.groups_repository.bulk_add_groups(groups_data)

                # Mostrar estadísticas finales
                self.groups_repository.print_stats()

                result = {
                    'success': True,
                    'added': added,
                    'duplicates': duplicates,
                    'total_processed': len(groups_data)
                }

                print(f"\n🎉 INTEGRACIÓN COMPLETADA")
                print(f"✅ Grupos añadidos: {added}")
                print(f"🔄 Duplicados omitidos: {duplicates}")
                print(f"📊 Total procesados: {len(groups_data)}")

                return result
            else:
                return {'success': False, 'message': 'No hay grupos válidos para integrar'}

        except Exception as e:
            print(f"❌ Error en integración: {e}")
            return {'success': False, 'message': f'Error en integración: {e}'}

    def execute_queue_processing(self, skip: bool = False) -> Dict[str, Any]:
        """
        Ejecuta el procesamiento de cola de grupos

        Args:
            skip: Si True, salta el procesamiento y devuelve resultado exitoso

        Returns:
            Dict con resultados del procesamiento
        """
        if skip:
            print("\n🚀 SALTANDO PROCESAMIENTO DE COLA (skip=True)")
            print("=" * 60)
            print("⏭️ Procesamiento de cola omitido por configuración interna")
            return {'success': True, 'skipped': True, 'message': 'Procesamiento de cola saltado por parámetro skip=True'}

        try:
            print("\n🚀 INICIANDO PROCESAMIENTO DE COLA DE GRUPOS")
            print("=" * 60)

            # Crear instancia del procesador de cola CON PÁGINA REAL
            queue_processor = QueueProcessor(page=self.page)

            # Ejecutar procesamiento
            queue_processor.process_queue()

            # Obtener estadísticas finales
            result = queue_processor.get_processing_stats()

            print("\n✅ PROCESAMIENTO DE COLA COMPLETADO")
            print("=" * 60)

            return result

        except Exception as e:
            print(f"❌ Error en procesamiento de cola: {e}")
            return {'success': False, 'message': f'Error en procesamiento de cola: {e}'}

    def test_post_group_extraction(self, group_url: str) -> Dict[str, Any]:
        """
        Test de extracción de posts de un grupo específico usando FacebookPostExtractor
        con humanización integrada

        Args:
            group_url: URL del grupo a extraer

        Returns:
            Dict con resultados de la extracción
        """
        print(f"\n🎯 TEST: EXTRACCIÓN DE POSTS DE GRUPO")
        print("=" * 60)
        print(f"🌐 URL objetivo: {group_url}")
        
        if not self.page:
            return {'success': False, 'message': 'No hay página disponible para la extracción'}
            
        try:
            # 1. Navegar al grupo específico
            print(f"\n🚀 Navegando al grupo...")
            self.page.goto(group_url)
            print(f"   ✅ Navegación completada")
            print(f"   📍 URL actual: {self.page.url}")
            
            # 2. Extraer información del grupo desde la URL
            group_info = extract_group_info_from_url(group_url)
            slug = extract_group_slug_from_url(group_url)
            group_name = generate_group_name_from_slug(slug) if slug else "Grupo desconocido"
            
            print(f"\n📋 Información del grupo:")
            print(f"   📝 Nombre: {group_name}")
            print(f"   🏷️ Slug: {slug}")
            print(f"   🔗 Info URL: {group_info}")
            
            # 3. Esperar a que la página se cargue completamente
            print(f"\n⏳ Esperando carga de la página...")
            self.page.wait_for_selector('[role="feed"]', timeout=15000)
            print(f"   ✅ Feed detectado")
            
            # 4. Aplicar humanización básica usando la clase disponible
            if hasattr(self, 'page_detectors') or True:  # Siempre ejecutar para el test
                try:
                    from utils.humanization import FacebookHumanizer
                    humanizer = FacebookHumanizer()
                    
                    print(f"\n🤖 Aplicando humanización previa...")
                    # Scroll suave inicial
                    self.page.evaluate("window.scrollTo(0, window.innerHeight * 0.5)")
                    self.page.wait_for_timeout(2000)
                    print(f"   ✅ Humanización aplicada")
                except Exception as e:
                    print(f"   ⚠️ Error en humanización: {e}")
            
            # 5. Obtener limit post de BD antes de extraer
            print(f"\n🔍 VERIFICANDO LIMIT POST EN BASE DE DATOS")
            print("-" * 50)

            # Normalizar URL para búsqueda (remover parámetros)
            from urllib.parse import urlparse
            parsed_url = urlparse(group_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
            if not base_url.endswith('/'):
                base_url += '/'

            print(f"   🔗 URL original: {group_url}")
            print(f"   🔗 URL base para búsqueda: {base_url}")

            # Obtener o crear grupo para tener group_id
            existing_group = self.groups_repository.get_group_by_url(base_url)
            if existing_group:
                group_id_for_limit = existing_group['id']
                print(f"   📋 Grupo existente: {existing_group['nombre']} (ID: {group_id_for_limit})")
            else:
                print(f"   ℹ️ Grupo no existe en BD, no hay limit post")
                group_id_for_limit = None

            # Obtener limit post si existe el grupo
            limit_post = None
            if group_id_for_limit:
                post_repo = PostRepository(self.groups_repository.db_path)
                limit_post = post_repo.retrieve_limit_post(group_id_for_limit)

                if limit_post:
                    print(f"   🎯 Limit post encontrado:")
                    print(f"      Autor: {limit_post.get('author', 'Sin autor')[:30]}")
                    print(f"      Contenido: {limit_post.get('content', 'Sin contenido')[:60]}...")
                    print(f"      Grupo ID: {limit_post.get('group_id')}")
                else:
                    print(f"   ℹ️ No hay limit post para este grupo")

            # 6. Crear extractor en modo test e iniciar extracción
            print(f"\n🔍 INICIANDO EXTRACCIÓN DE POSTS (MODO TEST)")
            print("-" * 50)

            extractor = FacebookPostExtractor(is_test=True, test_max_posts=15)
            
            # Parámetros para el test: extracción de hasta 15 posts con timeout de 90 segundos
            extraction_result = extractor.run_test_extraction(
                page=self.page,
                wait_seconds=90,  # Timeout de 90 segundos
                poll_interval=3.0,  # Verificar cada 3 segundos
                group_info={
                    'url': group_url,
                    'name': group_name,
                    'slug': slug,
                    'extracted_info': group_info
                },
                limit_post=limit_post  # Pasar limit_post para detener extracción
            )
            
            # 6. Procesar y mostrar resultados
            print(f"\n📊 RESULTADOS DE LA EXTRACCIÓN")
            print("=" * 60)
            
            if extraction_result.get('success', True):  # Si no hay 'success', asumimos que funcionó
                posts = extraction_result.get('posts', [])
                estadisticas = extraction_result.get('estadisticas', {})
                
                print(f"✅ Extracción completada exitosamente")
                print(f"📄 Posts extraídos: {len(posts)}")
                print(f"⏱️ Tiempo total: {estadisticas.get('tiempoTotal', 'N/A')}s")
                print(f"🔄 Scrolls realizados: {estadisticas.get('scrollsRealizados', 'N/A')}")
                print(f"👆 Clicks 'Ver más': {estadisticas.get('clicksVerMas', 'N/A')}")
                
                # Mostrar archivo JSON generado
                json_path = extraction_result.get('test_json_path')
                if json_path:
                    print(f"💾 Archivo JSON generado: {json_path}")

                # 7. GUARDAR POSTS EN BASE DE DATOS
                print(f"\n💾 GUARDANDO POSTS EN BASE DE DATOS")
                print("-" * 50)

                if posts:
                    try:
                        # Inicializar PostRepository
                        post_repo = PostRepository(self.groups_repository.db_path)

                        # Obtener o crear grupo en BD
                        existing_group = self.groups_repository.get_group_by_url(group_url)
                        if existing_group:
                            group_id = existing_group['id']
                            print(f"   📋 Grupo existente encontrado: {existing_group['nombre']}")
                        else:
                            group_id = self.groups_repository.add_group(
                                user_id=1,
                                nombre=group_name,
                                url=group_url,
                                in_flow=True,  # Marcar como activo para flujo
                                metadata={'slug': slug, 'extracted_info': group_info}
                            )
                            print(f"   ✅ Nuevo grupo creado: {group_name}")

                        print(f"   📋 Grupo ID en BD: {group_id}")
                        print(f"   📄 Posts a procesar: {len(posts)}")

                        # LOGGING DETALLADO: Confirmar parámetros antes de guardar
                        print(f"   🔍 LOGGING: Parámetros para save_extracted_posts:")
                        print(f"      - Posts a procesar: {len(posts)}")
                        print(f"      - Group ID: {group_id}")
                        print(f"      - User ID: 1")
                        print(f"      - is_test: False")
                        print(f"      - is_first_run: False (debería verificar duplicados BD)")

                        # Guardar posts usando PostRepository con thresholds dinámicos
                        save_result = post_repo.save_extracted_posts(
                            extracted_posts=posts,
                            group_id=group_id,
                            user_id=1,  # Usuario por defecto para tests
                            is_test=False,  # No limitar posts en flujo real
                            is_first_run=False  # Verificar duplicados contra BD
                        )

                        print(f"   ✅ Posts guardados: {save_result.get('saved_count', 0)}")
                        print(f"   ⏭️ Posts omitidos: {save_result.get('skipped_count', 0)}")
                        print(f"   🔍 Duplicados detectados: {save_result.get('duplicate_count', 0)}")
                        print(f"   ❌ Posts inválidos: {save_result.get('invalid_count', 0)}")

                        # Agregar info de BD al resultado
                        db_info = {
                            'group_id': group_id,
                            'saved_count': save_result.get('saved_count', 0),
                            'skipped_count': save_result.get('skipped_count', 0),
                            'duplicate_count': save_result.get('duplicate_count', 0),
                            'invalid_count': save_result.get('invalid_count', 0)
                        }

                    except Exception as e:
                        print(f"   ❌ Error guardando en BD: {e}")
                        db_info = {'error': str(e)}
                else:
                    print(f"   ⚠️ No hay posts para guardar")
                    db_info = {'message': 'no_posts_to_save'}

                # Mostrar muestra de posts extraídos
                if posts:
                    print(f"\n📋 MUESTRA DE POSTS EXTRAÍDOS:")
                    print("-" * 40)
                    for i, post in enumerate(posts[:3]):  # Mostrar solo los primeros 3
                        autor = post.get('nombreAutor', 'Sin autor')[:30]
                        contenido = post.get('contenidoPost', 'Sin contenido')[:80]
                        print(f"   [{i+1}] {autor}: {contenido}...")

                    if len(posts) > 3:
                        print(f"   ... y {len(posts) - 3} posts más")
                
                return {
                    'success': True,
                    'posts_count': len(posts),
                    'group_info': {
                        'url': group_url,
                        'name': group_name,
                        'slug': slug
                    },
                    'extraction_stats': estadisticas,
                    'json_file': json_path,
                    'database_info': db_info if 'db_info' in locals() else {},
                    'posts_sample': posts[:5] if posts else []  # Muestra de los primeros 5 posts
                }
            else:
                error_msg = extraction_result.get('error', 'Error desconocido en la extracción')
                print(f"❌ Error en la extracción: {error_msg}")
                return {
                    'success': False,
                    'message': f'Error en extracción: {error_msg}',
                    'group_info': {
                        'url': group_url,
                        'name': group_name,
                        'slug': slug
                    }
                }
                
        except Exception as e:
            print(f"❌ Error durante el test de extracción: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'message': f'Error durante el test: {e}',
                'group_info': {'url': group_url}
            }

    def early_exit_for_debug_purposes(self, exit_point: str, results: Optional[Dict[str, Any]] = None) -> bool:
        """
        Salida temprana para propósitos de debug

        Args:
            exit_point: Punto de salida ("after_groups_tracking", "after_login", etc.)
            results: Resultados a mostrar antes de salir

        Returns:
            bool: True si debe salir, False si debe continuar
        """
        print(f"\n🐛 DEBUG EXIT POINT: {exit_point}")
        print("=" * 60)

        if results:
            print("📊 RESULTADOS OBTENIDOS:")
            for key, value in results.items():
                if isinstance(value, dict):
                    print(f"   📋 {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"      {sub_key}: {sub_value}")
                else:
                    print(f"   {key}: {value}")

        print(f"\n⏸️ NAVEGADOR ABIERTO PARA INSPECCIÓN DE DEBUG")
        print("🔍 Puedes inspeccionar el estado actual del navegador")
        print("📋 Presiona Enter para continuar o Ctrl+C para salir...")

        try:
            input()
            return False  # Continuar si presiona Enter
        except KeyboardInterrupt:
            print("\n🛑 Salida forzada por Ctrl+C")
            return True  # Salir si presiona Ctrl+C


    def cleanup(self):
        """Limpia recursos del navegador"""
        try:
            if self.browser:
                self.browser.__exit__(None, None, None)
                print("✅ Cerrando navegador...")
        except Exception as e:
            print(f"❌ Error cerrando navegador: {e}")


def main():
    """Función principal del orquestador con soporte para argumentos. Incluye modo robusto de extracción de posts."""
    parser = argparse.ArgumentParser(description='Facebook Flow Orchestrator')
    parser.add_argument('--headless', action='store_true',
                       help='Ejecutar en modo headless (sin interfaz gráfica)')
    parser.add_argument('--config', default=None,
                       help='Ruta al archivo de configuración (usa ruta absoluta por defecto)')
    parser.add_argument('--skip-humanization', action='store_true',
                       help='Saltar humanización e ir directo a tareas')

    args = parser.parse_args()

    print("🧪 FACEBOOK FLOW ORCHESTRATOR")
    print("=" * 50)
    print(f"🎛️ Modo headless: {'✅ Activado' if args.headless else '🖥️ Modo Normal'}")
    print(f"📁 Config: {args.config}")
    print(f"🤖 Skip humanización: {'✅ Sí' if args.skip_humanization else '❌ No'}")
    print("=" * 50)

    # Crear orquestador
    orchestrator = FacebookFlowOrchestrator(config_path=args.config, headless=args.headless)

    try:
        # 1. Ejecutar login
        login_result = orchestrator.execute_login_flow()

        # Mostrar resultados del login
        print(f"\n📊 RESULTADO FINAL:")
        print("=" * 50)
        print(f"✅ Éxito: {login_result.get('success', False)}")
        print(f"🎯 Estado: {login_result.get('state', 'N/A')}")
        print(f"💬 Mensaje: {login_result.get('message', 'N/A')}")
        print(f"⏱️ Tiempo: {login_result.get('duration', 0):.2f}s")
        print(f"📸 Screenshots: {len(login_result.get('screenshots', []))}")

        # Mostrar verificación de home si existe
        home_verification = login_result.get('home_verification', {})
        if home_verification:
            print(f"\n🏠 Indicadores de Home:")
            for key, value in home_verification.items():
                if isinstance(value, bool):
                    icon = "✅" if value else "❌"
                    print(f"   {icon} {key}")
                elif key not in ['detection_time']:
                    print(f"   📊 {key}: {value}")

        # 2. Si login exitoso, ejecutar flujo con control de skip interno
        if login_result.get('success'):

             # ================================================================
            # CONFIGURACIÓN DE SKIP PARA DEBUG/TEST
            # ----------------------------------------------------------------
            # NOTA: Esta sección SOLO controla ramas de prueba después del login.
            #       NO introducir aquí cambios que alteren el flujo base de login
            #       (ver docstring superior). Todo lo que ocurre antes de llegar
            #       a este bloque es infraestructura estable y reutilizable.
            #
            # Modificar estos valores según el test que quieras hacer:
            skip_humanization = True       # Saltar humanización para ir directo a grupos
            skip_groups_tracking = True    # Saltar tracking (vamos directo a un grupo específico)
            skip_queue_processing = False  # NO saltar queue processing (queremos extraer posts)
            early_exit_after_groups = False  # No hacer early exit (queremos ver extracción de posts)
            test_single_group = True       # TEST: Navegar a un grupo específico hardcodeado
            # ================================================================

            print(f"\n🎛️ CONFIGURACIÓN DE SKIP PARA TEST:")
            print(f"   🤖 Skip humanización: {'✅ SÍ' if skip_humanization else '❌ NO'}")
            print(f"   🔍 Skip groups tracking: {'✅ SÍ' if skip_groups_tracking else '❌ NO'}")
            print(f"   🚀 Skip queue processing: {'✅ SÍ' if skip_queue_processing else '❌ NO'}")
            print(f"   🐛 Early exit after groups: {'✅ SÍ' if early_exit_after_groups else '❌ NO'}")
            print(f"   🎯 Test single group: {'✅ SÍ' if test_single_group else '❌ NO'}")
            print("=" * 60)

            # 3. Ejecutar humanización (con skip configurable)
            humanization_result = orchestrator.execute_humanization(skip=skip_humanization)

            # 4. Ejecutar tracking de grupos (con skip configurable)
            groups_result = orchestrator.execute_groups_tracking(skip=skip_groups_tracking)

            # 5. Early exit para debug si está configurado
            if early_exit_after_groups and groups_result.get('success') and not groups_result.get('skipped'):
                print(f"\n🐛 EARLY EXIT ACTIVADO - Inspeccionando resultados del tracking")
                should_exit = orchestrator.early_exit_for_debug_purposes("after_groups_tracking", groups_result)
                if should_exit:
                    return

            # 6. TEST: Navegar directamente a un grupo específico para extraer posts
            if test_single_group and orchestrator.page:
                print(f"\n🎯 TEST: NAVEGANDO A GRUPO ESPECÍFICO PARA EXTRACCIÓN DE POSTS")
                print("=" * 60)

                # Grupo hardcodeado para test - puedes cambiar esta URL por la del grupo que quieras probar
                test_group_url = "https://www.facebook.com/groups/wordpressmexico/?sorting_setting=CHRONOLOGICAL"

                print(f"🎯 Grupo objetivo: {test_group_url}")

                # Ejecutar test de extracción usando la nueva función
                extraction_result = orchestrator.test_post_group_extraction(test_group_url)
                
                # Mostrar resumen final del test
                print(f"\n🎉 TEST DE EXTRACCIÓN COMPLETADO")
                print("=" * 60)
                if extraction_result.get('success'):
                    print(f"✅ Resultado: EXITOSO")
                    print(f"📄 Posts extraídos: {extraction_result.get('posts_count', 0)}")
                    json_file = extraction_result.get('json_file')
                    if json_file:
                        print(f"💾 Archivo guardado: {json_file}")
                else:
                    print(f"❌ Resultado: FALLÓ")
                    print(f"💬 Mensaje: {extraction_result.get('message', 'Sin detalles')}")

            # 7. Ejecutar procesamiento de cola (con skip configurable) - SOLO SI NO ES TEST
            elif not test_single_group and groups_result.get('success') and not skip_queue_processing:
                queue_result = orchestrator.execute_queue_processing(skip=skip_queue_processing)
            elif skip_queue_processing:
                print("🚀 Saltando procesamiento de cola por configuración (skip_queue_processing=True)")
            else:
                print("⚠️ Saltando procesamiento de cola debido a error en tracking")

            # 7. Esperar cierre manual si hay página disponible (solo en modo no-headless)
            if orchestrator.page and not args.headless:
                print("\n⏸️ NAVEGADOR ABIERTO PARA INSPECCIÓN")
                print("=" * 40)
                print("🔍 Puedes inspeccionar el estado actual del navegador")
                input("📋 Presiona Enter cuando quieras cerrar...")
            elif args.headless:
                print("\n🤖 Modo headless: cerrando automáticamente...")
        else:
            print("\n⚠️ Login no exitoso, saltando humanización")

            # DEMO: Simular observación en página de grupo para mostrar funcionalidad
            print("\n🎯 DEMO: Simulando observación en página de grupo")
            print("=" * 50)

            # Crear QueueProcessor para demostración
            from queue_processor import QueueProcessor
            demo_processor = QueueProcessor(user_id=1)
            demo_processor._perform_real_page_observation()

            print("\n📋 DEMO completada - Funcionalidad de observación lista")

    except Exception as e:
        print(f"❌ Error en orquestación: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Limpiar recursos
        orchestrator.cleanup()


if __name__ == "__main__":
    main()
