#!/bin/bash
# Script de desarrollo para Facebook Flow

echo "🚀 FACEBOOK FLOW - HERRAMIENTAS DE DESARROLLO"
echo "=============================================="

case "$1" in
    "check")
        echo "🔍 Verificando tipos con mypy..."
        ./check_types.sh
        ;;
    "test")
        echo "🧪 Ejecutando tests..."
        python flow/flow.py --skip-humanization
        ;;
    "test-headless")
        echo "🤖 Ejecutando tests en modo headless..."
        python flow/flow.py --headless --skip-humanization
        ;;
    "lint")
        echo "🧹 Verificando código con mypy..."
        mypy flow/
        ;;
    "clean")
        echo "🧽 Limpiando cache..."
        rm -rf flow/__pycache__ flow/utils/__pycache__
        echo "✅ Cache limpiado"
        ;;
    "structure")
        echo "📁 Estructura del proyecto:"
        tree flow/ -I "__pycache__|*.pyc|results"
        ;;
    "help"|*)
        echo "📋 Comandos disponibles:"
        echo "   ./dev.sh check        - Verificar tipos (resumen)"
        echo "   ./dev.sh test         - Ejecutar test completo (modo normal)"
        echo "   ./dev.sh test-headless - Ejecutar test en modo headless"
        echo "   ./dev.sh lint         - Verificar tipos (completo)"
        echo "   ./dev.sh clean        - Limpiar cache"
        echo "   ./dev.sh structure    - Mostrar estructura"
        echo "   ./dev.sh help         - Mostrar esta ayuda"
        ;;
esac
