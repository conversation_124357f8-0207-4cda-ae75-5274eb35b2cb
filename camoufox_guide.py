#!/usr/bin/env python3
"""
Guía completa de uso de Camoufox con GeoIP y Fingerprints
Basada en la información obtenida de DeepWiki
"""

from camoufox import Camoufox
from browserforge.fingerprints import Screen
import time


def basic_example():
    """Ejemplo básico de uso de Camoufox"""
    print("=== Ejemplo Básico ===")
    
    with Camoufox() as browser:
        page = browser.new_page()
        page.goto("https://httpbin.org/ip")
        content = page.content()
        print(f"Respuesta básica obtenida exitosamente")
        print(f"Longitud del contenido: {len(content)} caracteres")


def geoip_example():
    """Ejemplo con GeoIP habilitado"""
    print("\n=== Ejemplo con GeoIP ===")
    
    with Camoufox(geoip=True) as browser:
        page = browser.new_page()
        page.goto("https://httpbin.org/ip")
        content = page.content()
        print(f"Navegación con GeoIP exitosa")
        
        # Verificar geolocalización
        page.goto("https://ipinfo.io/json")
        geo_content = page.content()
        print(f"Información de geolocalización obtenida exitosamente")

def fingerprint_example():
    """Ejemplo con fingerprints personalizados"""
    print("\n=== Ejemplo con Fingerprints Personalizados ===")
    
    with Camoufox(
        os="linux",  # Sistema operativo específico
        screen=Screen(max_width=1920, max_height=1080),  # Pantalla restringida
        window=(1200, 800),  # Tamaño de ventana fijo
        geoip=True,  # Habilitar geolocalización
        humanize=True,  # Movimiento humanizado del cursor
        block_webrtc=True,  # Bloquear WebRTC
        locale="es-ES"  # Configuración regional
    ) as browser:
        page = browser.new_page()
        

        # Verificar User Agent
        page.goto("https://httpbin.org/user-agent")
        user_agent = page.evaluate("() => navigator.userAgent")
        print(f"User Agent: {user_agent}")
        
        # Verificar información del navegador
        page.goto("https://httpbin.org/headers")
        print(f"Headers del navegador obtenidos exitosamente")

def proxy_example():
    """Ejemplo con proxy (comentado - requiere proxy válido)"""
    print("\n=== Ejemplo con Proxy (Comentado) ===")
    print("Para usar proxy, descomenta y configura con un proxy válido:")
    print("""
    with Camoufox(
        proxy={
            "server": "http://your_proxy_server:port",
            "username": "your_username",
            "password": "your_password"
        },
        geoip=True
    ) as browser:
        page = browser.new_page()

        page.goto("https://httpbin.org/ip")
        print(f"IP a través de proxy obtenida exitosamente")
    """)

def advanced_fingerprint_example():
    """Ejemplo avanzado con múltiples opciones de fingerprint"""
    print("\n=== Ejemplo Avanzado de Fingerprints ===")
    
    with Camoufox(
        os="windows",  # Sistema operativo Windows
        screen=Screen(max_width=1920, max_height=1080),
        humanize=True,  # Comportamiento humano
        block_webgl=False,  # Permitir WebGL para fingerprinting más realista
        block_webrtc=True,  # Bloquear WebRTC por privacidad
        locale="en-US",
        # Opciones adicionales
        headless=False,  # Navegador visible (cambia a True para modo headless)
    ) as browser:
        page = browser.new_page()
        
        # Test de detección de bot
        page.goto("https://bot.sannysoft.com/")
        print("Navegando a test de detección de bots...")
        time.sleep(3)  # Esperar a que cargue la página
        
        # Verificar si se detecta como bot
        title = page.title()
        print(f"Título de la página: {title}")

def main():
    """Función principal que ejecuta todos los ejemplos"""
    print("🦊 Guía Completa de Camoufox")
    print("=" * 50)
    
    try:
        basic_example()
        geoip_example()
        fingerprint_example()
        proxy_example()
        advanced_fingerprint_example()
        
        print("\n✅ Todos los ejemplos ejecutados exitosamente!")
        print("\n📚 Información adicional:")
        print("- Documentación: https://camoufox.com/python")
        print("- GitHub: https://github.com/daijro/camoufox")
        print("- Para usar proxies, configura credenciales válidas")
        print("- GeoIP requiere conexión a internet para funcionar")
        
    except Exception as e:
        print(f"❌ Error durante la ejecución: {e}")
        print("Asegúrate de que Camoufox esté correctamente instalado:")
        print("1. pip install -U camoufox[geoip]")
        print("2. python3 -m camoufox fetch")

if __name__ == "__main__":
    main()
