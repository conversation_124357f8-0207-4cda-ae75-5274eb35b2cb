# Approved by the leader

# Plan de Implementación Detallado para Refactor de Scraping

Este documento describe el diseño técnico para reintroducir las funcionalidades operativas perdidas en el sistema de scraping de Facebook, respetando la arquitectura actual.

---

## Tarea 1: <PERSON><PERSON><PERSON> `<PERSON>Manager`

**Objetivo**: Implementar un bucle de scroll controlado para cargar dinámicamente el contenido de la página antes de la extracción, reemplazando la carga estática actual.

### 1. Diseño de la Clase `ScrollManager`

**Ubicación**: Se creará un nuevo archivo en `flow/utils/scroll_manager.py`.

**Estructura de la Clase**:

```python
# flow/utils/scroll_manager.py
import time
import random
from typing import Dict, Any
from playwright.sync_api import Page

class ScrollManager:
    """
    Gestiona el scroll dinámico y progresivo de la página para cargar contenido.
    """

    def __init__(self, page: Page, config: Dict[str, Any]):
        """
        Inicializa el ScrollManager.

        Args:
            page: La instancia de la página de Playwright.
            config: Diccionario de configuración con parámetros de scroll.
                    Ej: {'max_scrolls': 10, 'delay_range': (1.5, 2.5), 'increment_px': 800}
        """
        self.page = page
        self.config = config
        self.post_selector = '[data-ad-rendering-role="story_message"]'

    def _get_post_count(self) -> int:
        """Cuenta el número de posts visibles en la página."""
        return self.page.locator(self.post_selector).count()

    def scroll_to_load_content(self) -> Dict[str, Any]:
        """
        Ejecuta el bucle de scroll para cargar posts. El bucle se detiene si se
        alcanza el máximo de scrolls o si el número de posts deja de aumentar.
        """
        max_scrolls = self.config.get('max_scrolls', 10)
        delay_min, delay_max = self.config.get('delay_range', (1.5, 2.5))
        scroll_increment = self.config.get('increment_px', 800)

        initial_post_count = self._get_post_count()
        print(f"🌀 [ScrollManager] Posts iniciales: {initial_post_count}")

        for i in range(max_scrolls):
            previous_post_count = self._get_post_count()

            # Ejecutar scroll
            scroll_script = f"window.scrollBy(0, {scroll_increment});"
            self.page.evaluate(scroll_script)

            # Espera adaptativa
            delay = random.uniform(delay_min, delay_max)
            print(f"🌀 [ScrollManager] Scroll {i + 1}/{max_scrolls}, esperando {delay:.2f}s...")
            time.sleep(delay)

            current_post_count = self._get_post_count()

            if current_post_count == previous_post_count:
                print(f"🌀 [ScrollManager] No se encontraron nuevos posts después del scroll. Deteniendo.")
                break

        final_post_count = self._get_post_count()
        new_posts_loaded = final_post_count - initial_post_count

        stats = {
            'scrolls_performed': i + 1,
            'initial_posts': initial_post_count,
            'final_posts': final_post_count,
            'new_posts_loaded': new_posts_loaded
        }

        print(f"🌀 [ScrollManager] Finalizado. Posts totales: {final_post_count} ({new_posts_loaded} nuevos cargados).")
        return stats
```

### 2. Integración en `SimpleObserver`

**Ubicación**: Se modificará el método `observe_group_with_page` en `flow/simple_observer.py`.

**Diagrama de Flujo (Mermaid)**:

```mermaid
graph TD
    A[Inicio: observe_group_with_page] --> B(Navegar al grupo);
    B --> C(Verificar carga de página);
    C --> D{Instanciar y usar ScrollManager};
    D --> E(Ejecutar scroll_to_load_content);
    E --> F[Extraer información del grupo];
    F --> G[Obtener posts almacenados];
    G --> H[Extraer posts con PostExtractor];
    H --> I[Guardar en BD];
    I --> J[Fin];
```

**Modificaciones en el código**:

```python
# flow/simple_observer.py

# ... (importaciones existentes)
from flow.utils.scroll_manager import ScrollManager # 1. Importar la nueva clase

class SimpleObserver:
    # ... (código existente)

    def observe_group_with_page(self, page: Page, group_url: str) -> Dict[str, Any]:
        # ... (código existente hasta la navegación)

        try:
            # 1. Navegar al grupo
            print(f"🌐 Navegando al grupo...")
            page.goto(group_url, wait_until="domcontentloaded")
            print(f"✅ DOMContentLoaded alcanzado")

            # 2. Reemplazar el time.sleep() por el ScrollManager
            # -- INICIO DE CAMBIO --
            scroll_config = {
                'max_scrolls': 10,
                'delay_range': (1.5, 2.5),
                'increment_px': 800
            }
            scroll_manager = ScrollManager(page, scroll_config)
            scroll_stats = scroll_manager.scroll_to_load_content()
            results['scroll_session'] = scroll_stats
            # -- FIN DE CAMBIO --

            # 3. Verificar que el grupo cargó correctamente
            self._verify_group_page_loaded(page, results)
            # ... (el resto del flujo continúa igual)

```

### 3. Conflictos y Estrategia de Transición

- **Conflicto**: El `time.sleep(3)` en la línea 60 de `flow/simple_observer.py` se vuelve obsoleto.
- **Solución**: Será reemplazado por la lógica de scroll del `ScrollManager`, que es más inteligente y efectiva.
- **Riesgo**: Mínimo. La extracción (`PostExtractor`) se mantiene intacta. Simplemente operará sobre un DOM con más elementos, lo cual es el comportamiento deseado. El `max_posts=10` en la llamada a `extract_posts_one_by_one_with_expansion` actuará como un límite de seguridad en esta primera etapa, evitando procesar una cantidad abrumadora de posts. Este límite se hará configurable en una tarea posterior.

---

## Tarea 2: Planificar Corrección de Errores Pylance

**Objetivo**: Diseñar la solución para los errores de `type-hinting` y uso incorrecto de la API de Playwright, mejorando la calidad y fiabilidad del código.

### 1. Solución de Errores de `Type Hinting`

**Problema**: Múltiples funciones usan `None` como valor por defecto para parámetros cuyo tipo no está marcado como opcional.

**Solución General**: Importar `Optional` de `typing` y envolver los tipos correspondientes.

**Archivos Afectados y Cambios Específicos**:

- **`flow/utils/page_detectors.py`**:
  - **Línea 25**: Cambiar `details: Dict = None` por `details: Optional[Dict] = None`.
- **`flow/post_comparison.py`**:
  - **Línea 26, 74, 115**: Cambiar `threshold: float = None` por `threshold: Optional[float] = None`.
  - **Línea 73, 233**: Cambiar `stored_posts: List[Dict[str, Any]] = None` por `stored_posts: Optional[List[Dict[str, Any]]] = None`.
- **`flow/utils/post_utils.py`**:
  - **Línea 62**: Cambiar `stored_posts: List[Dict[str, Any]] = None` por `stored_posts: Optional[List[Dict[str, Any]]] = None`.
- **`flow/utils/humanization.py`**:
  - **Línea 398**: Cambiar `activities: list = None` por `activities: Optional[list] = None`.
- **`flow/queue_processor.py`**:
  - **Línea 158**: El error `Argumento de tipo "Unknown | None" no puede ser asignado a parámetro "page"` indica que `self.page` puede ser `None`. Se debe añadir una guarda.
    ```python
    # ANTES
    self.group_ops.set_page(self.page)
    # DESPUÉS
    if self.page:
        self.group_ops.set_page(self.page)
    else:
        # Manejar el caso en que la página no está disponible
        print("ERROR: La página no está inicializada en QueueProcessor.")
    ```

### 2. Solución de Errores de API de Playwright

**Problema**: En varios archivos (`test_groups_tracker.py`, `home.py`, `groups_tracker.py`, `login_system.py`), se llama al método `.new_context()` sobre una variable que, según el error de Pylance, ya es un `BrowserContext` y no una instancia de `Browser`.

**Solución Arquitectónica**:

1.  **Auditoría de la inicialización de Playwright**: Se debe trazar el origen de la variable `browser_started` (o similar) en los puntos de entrada de la aplicación/tests.
2.  **Clarificar el patrón de uso**: El patrón correcto es: `browser = playwright.chromium.launch()` -> `context = browser.new_context()` -> `page = context.new_page()`.
3.  **Refactorización Propuesta**: La variable que se pasa a las funciones debe ser la instancia de `browser` y no la de `context`, si es que se necesita crear _nuevos_ contextos dentro de esas funciones. Si lo que se necesita es la página, se debe pasar el objeto `page` directamente.
4.  **Acción Correctiva Específica**: Se debe modificar la lógica de inicialización para que las funciones que necesitan crear contextos reciban el objeto `browser` principal. Si una función solo opera sobre un contexto existente, su firma debe reflejar que recibe un `BrowserContext`.

## Este plan de corrección de deuda técnica debe ser implementado por un desarrollador para sanear el código base antes de continuar con la adición de nuevas funcionalidades complejas.

## Tarea 3: Crear Módulo `OverlayHandler`

**Objetivo**: Implementar un mecanismo robusto y proactivo para detectar y cerrar elementos superpuestos (modales, pop-ups, banners) que interfieren con la extracción.

### 1. Diseño de la Clase `OverlayHandler`

**Ubicación**: Se creará un nuevo archivo en `flow/utils/overlay_handler.py`.

**Estructura de la Clase**:

```python
# flow/utils/overlay_handler.py
import time
from typing import List, Dict, Any
from playwright.sync_api import Page

class OverlayHandler:
    """
    Gestiona la detección y cierre de overlays y modales intrusivos.
    """

    OVERLAY_SELECTORS = [
        "div[role='dialog']",
        "[aria-modal='true']",
        "div[aria-label*='cookie' i]", # Banners de cookies
    ]

    CLOSE_BUTTON_SELECTORS = [
        "[aria-label*='Cerrar' i]",
        "[aria-label*='Close' i]",
        "div[role='button'][tabindex='0']",
    ]

    def __init__(self, page: Page):
        self.page = page

    def close_overlays(self) -> Dict[str, Any]:
        """
        Busca y cierra todos los overlays visibles conocidos.

        Returns:
            Un diccionario con estadísticas sobre los overlays cerrados.
        """
        closed_count = 0
        details = []

        for selector in self.OVERLAY_SELECTORS:
            overlays = self.page.locator(selector).all()
            for i, overlay in enumerate(overlays):
                if overlay.is_visible():
                    print(f"🔍 [OverlayHandler] Overlay detectado con selector: {selector}")

                    # Estrategia 1: Presionar Escape
                    self.page.keyboard.press("Escape")
                    print("  - Intentando cerrar con tecla 'Escape'...")
                    time.sleep(1) # Esperar a que la animación de cierre termine

                    if overlay.is_visible():
                        print("  - Overlay todavía visible. Intentando con botón de cierre.")
                        # Estrategia 2: Buscar y hacer clic en el botón de cierre
                        closed_by_button = False
                        for btn_selector in self.CLOSE_BUTTON_SELECTORS:
                            close_button = overlay.locator(btn_selector).first
                            if close_button.is_visible():
                                try:
                                    close_button.click()
                                    time.sleep(0.5)
                                    print(f"    - Clic en botón de cierre: {btn_selector}")
                                    closed_by_button = True
                                    break
                                except Exception as e:
                                    print(f"    - Error al hacer clic en el botón: {e}")

                        if not overlay.is_visible():
                            closed_count += 1
                            details.append({'selector': selector, 'method': 'button_click'})
                    else:
                        closed_count += 1
                        details.append({'selector': selector, 'method': 'escape_key'})

        if closed_count > 0:
            print(f"✅ [OverlayHandler] Se cerraron {closed_count} overlays.")

        return {'closed_count': closed_count, 'details': details}
```

### 2. Integración en `SimpleObserver`

Se llamará al `OverlayHandler` en el método `observe_group_with_page` justo después de la navegación, para limpiar el entorno antes de cualquier otra acción.

**Modificaciones en `flow/simple_observer.py`**:

```python
# flow/simple_observer.py

# ... (importaciones existentes)
from flow.utils.scroll_manager import ScrollManager
from flow.utils.overlay_handler import OverlayHandler # 1. Importar

class SimpleObserver:
    # ...
    def observe_group_with_page(self, page: Page, group_url: str) -> Dict[str, Any]:
        # ...
        try:
            # 1. Navegar al grupo
            page.goto(group_url, wait_until="domcontentloaded")

            # -- INICIO DE CAMBIO --
            # 2. Limpiar Overlays iniciales
            overlay_handler = OverlayHandler(page)
            overlay_stats = overlay_handler.close_overlays()
            results['overlay_handling'] = overlay_stats
            # -- FIN DE CAMBIO --

            # 3. Scroll para cargar contenido
            scroll_manager = ScrollManager(page, self.config.get('scroll_config', {}))
            scroll_stats = scroll_manager.scroll_to_load_content()
            # ... el resto del flujo continúa
```

**Diagrama de Flujo Actualizado (Mermaid)**:

```mermaid
graph TD
    A[Inicio] --> B(Navegar al grupo);
    B --> C{Instanciar y usar OverlayHandler};
    C --> D(Cerrar overlays iniciales);
    D --> E{Instanciar y usar ScrollManager};
    E --> F(Ejecutar scroll para cargar contenido);
    F --> G[Continuar con extracción...];
```

### 3. Conflictos y Estrategia de Transición

- **Riesgo**: Bajo. Este es un paso aditivo. En el peor de los casos (si no detecta un overlay o falla al cerrarlo), el comportamiento será el mismo que el actual. No interfiere con la lógica de extracción, solo la habilita.
- **Mejora**: Aumenta significativamente la robustez del scraper, evitando fallos por interrupciones visuales. Es un paso fundamental antes de implementar lógicas de extracción más complejas.
